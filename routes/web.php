<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| contains the "web" middleware group. Now create something great!
|
*/

Route::get('/', function () {
    return view('welcome');
});

// 邮箱验证
Route::get('user/email-verify', 'Api\EmailController@verify');

/*
 * Socialite
 */
Route::get('auth/{service}', 'Api\SocialiteController@redirectToProvider');
Route::get('auth/{service}/callback', 'Api\SocialiteController@handleProviderCallback');
