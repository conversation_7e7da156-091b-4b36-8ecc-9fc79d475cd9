<?php

use Illuminate\Support\Facades\Route;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
 */

Route::any('oss', 'OssController@getKey');

Route::middleware(['income'])->group(function () {
    Route::post('user/login', 'AuthController@login');
    Route::post('user/register', 'AuthController@register');
    Route::post('user/forget-password', 'AuthController@forget');
    Route::post('user/reset-password', 'AuthController@reset');
    Route::get('/home', 'HomeController@carousel');
    Route::get('/home/<USER>', 'HomeController@plug');
    Route::get('/home/<USER>', 'HomeController@course');
    Route::get('/home/<USER>', 'HomeController@goods');
    Route::get('/home/<USER>', 'AiController@getGallery');
    Route::get('/footer', 'FooterController@info');
// 关于我们
    Route::get('/about', 'ConfigController@about');
    Route::get('/config/info', 'ConfigController@info');
// 注册协议
    Route::get('/register', 'ConfigController@register');
// 常见问题
    Route::get('/question', 'ConfigController@question');
    Route::get('/service', 'ConfigController@service');
    Route::get('/private', 'ConfigController@private');
    // 下载声明
    Route::get('/download_statement', 'ConfigController@downloadStatement');
// 上传
    Route::post('upload', 'UploadController@upload');

// 验证码
    Route::get('verification-codes/check', 'VerificationCodesController@check');
    Route::post('verification-codes', 'VerificationCodesController@send');

// 邮箱验证
    Route::post('email-verify', 'EmailController@verify');

    //智能渲染风格
    Route::get('/ai/render_style', 'AiController@getRenderStyle');
    Route::post('/ai/gallery', 'AiController@getGalleryList');
    Route::get('/ai/vip_setting', 'AiController@getVipSetting');


    Route::any('getUserPutConfig', 'UserController@getUserPutConfig');

    //获取粉丝信息
    Route::any('getUserFansInfo/{fid}', 'UserFansController@getUserFansInfo');
    Route::any('getUserFansListInfo/{type}', 'UserFansController@getUserFansListInfo');
    //关注列表
    Route::any('getUserFansList', 'UserFansController@getUserFansList');
    //粉丝列表
    Route::any('getUserAttentionlist', 'UserFansController@getUserAttentionlist');

    Route::middleware(['auth:api', 'user.state'])->group(function () {
        Route::any('userGuanzhu/{fid}', 'UserFansController@userGuanzhu');


//标签
        Route::any('getTagLists', 'TagController@getTagLists');


        //上传提交
        Route::any('userput/{type}', 'UserController@userput');
        //列表
        Route::any('getUserPut/{type}', 'UserController@getUserPut');
        //删除
        Route::any('delUserPut/{type}', 'UserController@delUserPut');
        //修改
        Route::any('updateUserPut/{type}', 'UserController@updateUserPut');


        Route::get('/home/<USER>', 'HomeController@needLogin'); # test


        // 退出登陆
        Route::post('user/logout', 'AuthController@logout');

        // 实名认证
        Route::get('certification', 'CertificationController@show');
        Route::post('certification', 'CertificationController@store');
        Route::put('certification', 'CertificationController@update');


        Route::any('user/getUserCate', 'UserController@getUserCate');
        Route::get('user', 'UserController@info');
        Route::get('user/wallet_logs', 'UserController@walletlogs');
        // 用户设置
        Route::post('user/update', 'UserController@update');
        Route::post('user/set-password', 'UserController@setPassword');
        Route::post('user/security', 'UserController@security');
        Route::post('user/set-email', 'UserController@setEmail');
        Route::post('user/set-phone', 'UserController@setPhone');

        // 课程
        Route::post('courses/progress', 'CourseController@progress');
        Route::post('courses/collect', 'UserCourseController@collect');
        Route::post('courses/collect/cancel', 'UserCourseController@cancelCollect');
        // 用户观看课程列表
        Route::get('user/courses', 'UserCourseController@index');

        // 插件
        Route::post('plug/{plug_id}/details', 'PlugController@details');

        // 评论
        Route::get('user/reply', 'CommentController@replyUserList');
        Route::post('comments', 'CommentController@store');
        Route::post('comments/reply', 'CommentController@storeReply');
        Route::delete('comments/{comment}', 'CommentController@destroy');
        Route::post('comments/{comment}/like', 'CommentController@doLike');
        Route::post('comments/{comment}/unlike', 'CommentController@unLike');

        // 订单
        Route::post('create-order', 'OrderController@create');
        Route::post('free-course', 'OrderController@freeCourse');
        Route::post('get-order', 'OrderController@orderInfo');
        Route::post('order-pay', 'OrderController@toPay');
        Route::get('/user/order-list', 'OrderController@index');
        Route::get('/order/{order}', 'OrderController@info');
        // 新下单接口20240302
        Route::post('neworder/store', 'newOrderController@createOrder');
        Route::post('neworder/preinfo', 'newOrderController@preOrderInfo');

        // 充值
        Route::post('integral/recharge', 'RechargeController@toPay');
        Route::post('integral/order', 'RechargeController@info');

        // 发票管理
        Route::group(['prefix' => 'invoice'], function () {
            // 发票信息管理
            Route::get('/info', 'InvoiceController@getInvoiceInfos');
            Route::post('/info', 'InvoiceController@createInvoiceInfo');
            Route::put('/info/{id}', 'InvoiceController@updateInvoiceInfo');
            Route::delete('/info/{id}', 'InvoiceController@deleteInvoiceInfo');

            // 开票相关
            Route::get('/recharge-orders', 'InvoiceController@getRechargeOrders');
            Route::post('/apply', 'InvoiceController@applyInvoice');
            Route::put('/modify/{id}', 'InvoiceController@modifyInvoice');
            Route::get('/records', 'InvoiceController@getInvoiceRecords');
        });

        // 优惠码
        Route::post('coupon-code', 'CouponCodeController@info');

        //收货地址
        Route::get('address', 'UserAddressController@index');
        Route::post('address', 'UserAddressController@store');
        Route::put('address/{address_id}', 'UserAddressController@update');
        Route::delete('address/{address_id}/delete', 'UserAddressController@delete');

        //生成图片
        // Route::post('ai/generate', 'AiController@generateImg');
        //余额
        Route::get('balance', 'AiController@getBalance');
        //isvip
        Route::get('ai/vip', 'AiController@getVip');
        //chat
        Route::post('ai/chat', 'AiController@chat');
        //buy vip
        Route::post('ai/buy', 'AiController@buy');

        Route::post('/ai/gallery/like', 'AiController@likeGallery');
    });

    //Route::get('ai/vip', 'AiController@getVip');
    // Route::post('ai/chat', 'AiController@chat');
    Route::any('ai/generate', 'AiController@generateImg');
    //热门提问
    Route::post('ai/hot_question', 'AiController@getHotQuestion');
    //ai新闻
    Route::post('ai/news', 'AiController@getNews');
    Route::get('ai/news/{news_id}', 'AiController@getNewsInfo');
    //ai插件
    Route::get('ai/plug', 'AiController@getPlug');
    
    /**
     * 新闻列表
     */
    Route::get('news/list', 'ArticleController@getList');
// 新闻
    Route::get('news', 'NewsController@list');
    Route::get('news/cate', 'NewsController@cate');
    Route::get('news/{news_id}', 'NewsController@info');

// 课程
    Route::get('courses', 'CourseController@index');
    Route::get('courses/list', 'CourseController@list');
    Route::post('courses/view', 'CourseController@view');
    Route::get('courses/cate', 'CourseController@cate');
    Route::get('courses/cate/{cate_id}', 'CourseController@cateInfo');
    Route::get('courses/level', 'CourseController@level');
    Route::get('courses/{course_id}', 'CourseController@detail');
    Route::get('courses/{course_id}/comment', 'CourseController@comment');
    Route::get('courses/{course_id}/prevnext', 'CourseController@prevAndNext');

// 插件
    Route::get('plug', 'PlugController@index');
    Route::get('plug/list', 'PlugController@list');
    Route::get('plug/mapping', 'PlugController@mapping');
    Route::get('plug/cate', 'PlugController@cate');
    Route::get('plug/{plug_id}', 'PlugController@info');
    Route::post('plug/{plug_id}/count', 'PlugController@downloadCount');

// 周边
    Route::get('goods', 'GoodsController@index');
    Route::get('goods/cate', 'GoodsController@cate');
    Route::get('goods/{goods}', 'GoodsController@info');

// 搜索
    Route::get('search', 'SearchController@index');

// 评论
    Route::get('comments', 'CommentController@index');
});

// 二维码
Route::get('qrcode/{str}', 'QrcodeController@index')->name('qrcode');

// 支付宝回调
Route::post('/alipay-notify', "PayController@alipayNotify")->name('alipay-notify');
// 微信回调
Route::post('/wechat-notify', "PayController@wechatNotify")->name('wechat-notify');
