<?php

use Illuminate\Support\Facades\Route;

// 登入
Route::any('login', 'AuthController@login');
Route::post('check_bind', 'AuthController@checkBind');
Route::post('bind', 'AuthController@bind');
Route::get('token', 'AuthController@ossToken');
Route::group(['prefix' => 'public',], function () {
    Route::get('/user', ['uses' => 'UserController@userFilter']);
    Route::get('/user/author', ['uses' => 'UserController@authorFilter']);
    Route::get('/course/cate', ['uses' => 'CourseController@cateFilter']);
    Route::get('/course/filter', ['uses' => 'CourseController@courseFilter']);
    Route::get('/course_category/filter', ['uses' => 'CourseCategoryController@filter']);
    Route::get('/plug_category/filter', ['uses' => 'PlugCategoryController@filter']);
    Route::get('/goods_category/filter', ['uses' => 'GoodCategoryController@filter']);
    Route::get('/gallery_category/filter', ['uses' => 'AiController@gcFilter']);
    Route::get('/render_style/filter', ['uses' => 'AiController@rsFilter']);
    Route::get('/plug/cate', ['uses' => 'PlugController@cateFilter']);
    Route::get('/goods/cate', ['uses' => 'GoodsController@cateFilter']);
    Route::post('upload', ['uses' => 'UploadController@upload']);
    Route::get('sz_course', ['uses' => 'CourseController@szCourse']);
});
Route::group(['middleware' => ['auth:admin']], function () {
    //  管理员菜单
    Route::get('nav', ['uses' => 'AdminController@nav']);

    //  管理员退出登录
    Route::put('logout', ['uses' => 'AuthController@logout']);

    //  管理员模块
    Route::group(['prefix' => 'admins', 'middleware' => 'admin:admin'], function () {
        //  管理员列表
        Route::get('/', ['uses' => 'AdminController@index']);
        //  创建管理员
        Route::post('/', ['uses' => 'AdminController@store']);
        //  管理员详情
        Route::get('/{admin}', ['uses' => 'AdminController@show']);
        //  修改管理员信息
        Route::put('/{admin}', ['uses' => 'AdminController@update']);
        //  删除管理员
        Route::delete('/{admin}', ['uses' => 'AdminController@destroy']);
    });

    //  权限模块
    Route::group(['prefix' => 'menus', 'middleware' => 'admin:menu'], function () {
        //  权限列表
        Route::get('/', ['uses' => 'AuthorityController@index']);
        //  创建权限
        Route::post('/', ['uses' => 'AuthorityController@store']);
        //  权限详情
        Route::get('/{authority}', ['uses' => 'AuthorityController@show']);
        //  修改权限信息
        Route::put('/{authority}', ['uses' => 'AuthorityController@update']);
        //  删除权限
        Route::delete('/{authority}', ['uses' => 'AuthorityController@destroy']);
    });

    //  角色模块
    Route::group(['prefix' => 'roles', 'middleware' => 'admin:role'], function () {
        //  角色列表
        Route::get('/', ['uses' => 'RoleController@index']);
        //  创建角色
        Route::post('/', ['uses' => 'RoleController@store']);
        //  角色详情
        Route::get('/{role}', ['uses' => 'RoleController@show']);
        //  修改角色信息
        Route::put('/{role}', ['uses' => 'RoleController@update']);
        //  删除角色
        Route::delete('/{role}', ['uses' => 'RoleController@destroy']);
    });

    //  验证码列表
    Route::get('verifications', ['uses' => 'VerificationCodeController@index', 'middleware' => 'admin:verification']);

    //系统日志
    Route::group(['prefix' => 'syslog', 'middleware' => 'admin:log'], function () {
        Route::get('/', 'OperationLogsController@index');
    });

    //  系统配置模块
    Route::group(['prefix' => 'configs', 'middleware' => 'admin:config'], function () {
        // 关于我们
        Route::any('/about', ['uses' => 'ConfigController@about']);
        Route::any('/private', ['uses' => 'ConfigController@private']);
        Route::any('/service', ['uses' => 'ConfigController@service']);
        Route::any('/question', ['uses' => 'ConfigController@question']);
        Route::any('/register', ['uses' => 'ConfigController@register']);
        Route::any('/about', ['uses' => 'ConfigController@about']);
        Route::any('/contact', ['uses' => 'ConfigController@contact']);
        Route::any('/ad', ['uses' => 'ConfigController@ad']);
        Route::any('/upload_statement', ['uses' => 'ConfigController@uploadStatement']);
        Route::any('/copyright_statement', ['uses' => 'ConfigController@copyrightStatement']);
        Route::any('/download_statement', ['uses' => 'ConfigController@downloadStatement']);
        Route::any('/aiServer', ['uses' => 'ConfigController@aiServer']);
        // 折扣配置
        Route::any('/discount', ['uses' => 'ConfigController@discountConfig']);
    });



    //新增
    // 上传配置
    Route::group(['prefix' => 'configs', 'middleware' => 'admin:config'], function () {
        // 关于我们
        Route::any('/myuploadconfig', ['uses' => 'ConfigController@myuploadconfig']);
    });

    // 稿件管理
    Route::group(['prefix' => 'contribution'], function () {
        // 关于我们
        Route::any('/{type}', ['uses' => 'ContributionController@getList']);
        Route::any('/{type}/{id}/delete', ['uses' => 'ContributionController@delete']);
        Route::any('/update/{type}/{id}', ['uses' => 'ContributionController@update']);
        Route::any('/wallet/{type}/{id}', ['uses' => 'ContributionController@userwallet']);
    });



    // 标签管理
    Route::group(['prefix' => 'tag'], function () {
        // 关于我们
        Route::any('/getList', ['uses' => 'TagController@getList']);
        Route::any('/poststore', ['uses' => 'TagController@store']);
        Route::any('/{id}/delete', ['uses' => 'TagController@delete']);
    });



    //首页数据
    Route::group(['prefix' => 'home'], function () {
        Route::get('/', 'HomeController@index');
    });


    //  用户模块
    Route::group(['prefix' => 'users',
        'middleware' => 'admin:user'
    ], function () {
        Route::get('/cateTree', ['uses' => 'UserController@cateTree']);
        //  用户列表
        Route::get('/', ['uses' => 'UserController@index']);
        //  修改用户信息
        Route::put('/{user}', ['uses' => 'UserController@update']);
        // 用户删除
        Route::delete('/{user}/delete', ['uses' => 'UserController@closeAccount']);
        // 用户资产扣除
        Route::post('/{userId}/clear/wallet', ['uses' => 'UserController@clearWallet']);
        // 导出用户信息
        Route::get('/export/info', ['uses' => 'UserController@exportUser']);
        // 用户课程列表
        Route::get('/course/list', ['uses' => 'CourseController@userCourseList']);
        Route::post('/user_course', ['uses' => 'CourseController@userCourseSotre']);
        Route::delete('/course/{userCourse}/delete', ['uses' => 'CourseController@userCourseDelete']);
    });

    //  课程模块
    Route::group(['prefix' => 'courses', 'middleware' => 'admin:course'], function () {
        //  列表
        Route::get('/', ['uses' => 'CourseController@index']);
        Route::get('/{course}', ['uses' => 'CourseController@info']);
        Route::post('/', ['uses' => 'CourseController@store']);
        Route::put('/{course}', ['uses' => 'CourseController@update']);
        Route::delete('/{course}/delete', ['uses' => 'CourseController@delete']);
        Route::get('/{course_id}/chapter/filter', ['uses' => 'CourseController@chapterFilter']);
        // 详情
        Route::get('/detail/list', ['uses' => 'CourseDetailController@index']);
        Route::get('/detail/{courseDetail}', ['uses' => 'CourseDetailController@info']);
        Route::post('/detail', ['uses' => 'CourseDetailController@store']);
        Route::put('/detail/{courseDetail}', ['uses' => 'CourseDetailController@update']);
        Route::delete('/detail/{courseDetail}/delete', ['uses' => 'CourseDetailController@delete']);
    });

    //  课程分类管理
    Route::group(['prefix' => 'course_category', 'middleware' => 'admin:courseCate'], function () {
        Route::get('/', ['uses' => 'CourseCategoryController@index']);
        Route::post('/', ['uses' => 'CourseCategoryController@store']);
        Route::put('/{category}', ['uses' => 'CourseCategoryController@update']);
        Route::delete('/{category}', ['uses' => 'CourseCategoryController@destroy']);
        Route::patch('/{category}/sort', ['uses' => 'CourseCategoryController@settingSort']);
    });

    //  插件管理
    Route::group(['prefix' => 'plugs', 'middleware' => 'admin:plug'], function () {
        //  列表
        Route::get('/', ['uses' => 'PlugController@index']);
        Route::get('/{plug}', ['uses' => 'PlugController@info']);
        Route::post('/', ['uses' => 'PlugController@store']);
        Route::put('/{plug}', ['uses' => 'PlugController@update']);
        Route::delete('/{plug}/delete', ['uses' => 'PlugController@delete']);

        // 详情
        Route::get('/detail/list', ['uses' => 'PlugDetailController@index']);
        Route::get('/detail/{plugDetail}', ['uses' => 'PlugDetailController@info']);
        Route::post('/detail', ['uses' => 'PlugDetailController@store']);
        Route::put('/detail/{plugDetail}', ['uses' => 'PlugDetailController@update']);
        Route::delete('/detail/{plugDetail}/delete', ['uses' => 'PlugDetailController@delete']);
    });

    //  插件分类管理
    Route::group(['prefix' => 'plug_category', 'middleware' => 'admin:plugCate'], function () {
        Route::get('/', ['uses' => 'PlugCategoryController@index']);
        Route::post('/', ['uses' => 'PlugCategoryController@store']);
        Route::put('/{category}', ['uses' => 'PlugCategoryController@update']);
        Route::delete('/{category}', ['uses' => 'PlugCategoryController@destroy']);
        Route::patch('/{category}/sort', ['uses' => 'PlugCategoryController@settingSort']);
    });

    // 订单管理
    Route::group(['prefix' => 'order', 'middleware' => 'admin:order'], function () {
        Route::get('/', ['uses' => 'OrderController@index']);
        Route::get('/export', ['uses' => 'OrderController@export']);
        Route::get('/goodexport', ['uses' => 'OrderController@goodExport']);
        Route::post('/deliver', 'OrderController@deliver');
    });

    // 充值订单管理
    Route::group(['prefix' => 'recharge_order', 'middleware' => 'admin:recharge_order'], function () {
        Route::get('/', ['uses' => 'RechargeOrderController@index']);
        Route::get('/export', ['uses' => 'RechargeOrderController@export']);

        // 开票管理功能
        Route::get('/{id}/invoice-records', ['uses' => 'RechargeOrderController@getInvoiceRecords']);
        Route::post('/invoice/upload', ['uses' => 'RechargeOrderController@uploadInvoice']);
        Route::put('/invoice/{id}/status', ['uses' => 'RechargeOrderController@updateInvoiceStatus']);
    });

    // 光子充值管理
    Route::group(['prefix' => 'recharge', 'middleware' => 'admin:recharge'], function () {
        // 管理员充值光子
        Route::post('/admin-recharge', ['uses' => 'RechargeController@adminRecharge']);
        // 充值记录列表
        Route::get('/list', ['uses' => 'RechargeController@rechargeList']);
        // 导出充值记录
        Route::get('/export', ['uses' => 'RechargeController@exportRechargeList']);
        // 充值统计
        Route::get('/stats', ['uses' => 'RechargeController@rechargeStats']);
    });

    //  周边管理
    Route::group(['prefix' => 'goods', 'middleware' => 'admin:good'], function () {
        //  列表
        Route::get('/', ['uses' => 'GoodsController@index']);
        Route::get('/{goods}', ['uses' => 'GoodsController@info']);
        Route::post('/', ['uses' => 'GoodsController@store']);
        Route::put('/{goods}', ['uses' => 'GoodsController@update']);
        Route::delete('/{goods}/delete', ['uses' => 'GoodsController@delete']);
        Route::post('/status/{goods}', ['uses' => 'GoodsController@status']);
    });
    //  周边分类管理
    Route::group(['prefix' => 'good_category', 'middleware' => 'admin:goodCate'], function () {
        Route::get('/', ['uses' => 'GoodCategoryController@index']);
        Route::post('/', ['uses' => 'GoodCategoryController@store']);
        Route::put('/{category}', ['uses' => 'GoodCategoryController@update']);
        Route::delete('/{category}', ['uses' => 'GoodCategoryController@destroy']);
        Route::patch('/{category}/sort', ['uses' => 'GoodCategoryController@settingSort']);
    });
    //  新闻管理
    Route::group(['prefix' => 'news', 'middleware' => 'admin:new'], function () {
        //  列表
        Route::get('/', ['uses' => 'NewController@index']);
        Route::get('/{news}', ['uses' => 'NewController@info']);
        Route::post('/', ['uses' => 'NewController@store']);
        Route::put('/{news}', ['uses' => 'NewController@update']);
        Route::delete('/{news}/delete', ['uses' => 'NewController@delete']);
    });
    //  优惠码管理
    Route::group(['prefix' => 'coupon_codes', 'middleware' => 'admin:coupon_code'], function () {
        //  列表
        Route::get('/', ['uses' => 'CouponController@index']);
        Route::get('/{couponCode}', ['uses' => 'CouponController@info']);
        Route::post('/', ['uses' => 'CouponController@store']);
        Route::delete('/{couponCode}/delete', ['uses' => 'CouponController@delete']);
    });

    //  评论
    Route::group(['prefix' => 'comments', 'middleware' => 'admin:comment'], function () {
        Route::get('/', ['uses' => 'CommentController@index']);
        Route::get('/{comment}/reply', ['uses' => 'CommentController@reply']);
        Route::delete('/{comment}', ['uses' => 'CommentController@commentDestroy']);
        Route::delete('/replys/{reply}', ['uses' => 'CommentController@replyDestroy']);
    });

    //  AI管理
    Route::group(['prefix' => 'ai'], function () {
        //资讯列表
        Route::group(['prefix' => '/', 'middleware' => 'admin:aiNews'], function () {
            Route::get('/news', ['uses' => 'AiController@news']);
            Route::get('/news/{news}', ['uses' => 'AiController@newsInfo']);
            Route::post('/news', ['uses' => 'AiController@newsStore']);
            Route::put('/news/{news}', ['uses' => 'AiController@newsUpdate']);
            Route::delete('/news/{news}/delete', ['uses' => 'AiController@newsDelete']);
        });

        //作品集分类
        Route::group(['prefix' => 'gallery_category', 'middleware' => 'admin:aiGalleryCategory'], function () {
            Route::get('/', ['uses' => 'AiController@galleryCategory']);
            Route::get('/{category}', ['uses' => 'AiController@galleryCategoryInfo']);
            Route::post('/', ['uses' => 'AiController@galleryCategoryStore']);
            Route::put('/{category}', ['uses' => 'AiController@galleryCategoryUpdate']);
            Route::delete('/{category}/delete', ['uses' => 'AiController@galleryCategoryDelete']);
        });

        //作品集
        Route::group(['prefix' => '/', 'middleware' => 'admin:aiGallery'], function () {
            Route::get('/gallery', ['uses' => 'AiController@gallery']);
            Route::get('/gallery/{gallery}', ['uses' => 'AiController@galleryInfo']);
            Route::post('/gallery', ['uses' => 'AiController@galleryStore']);
            Route::put('/gallery/{gallery}', ['uses' => 'AiController@galleryUpdate']);
            Route::delete('/gallery/{gallery}/delete', ['uses' => 'AiController@galleryDelete']);
        });

        //热门问题
        Route::group(['prefix' => '/', 'middleware' => 'admin:aiHot'], function () {
            Route::get('/hot', ['uses' => 'AiController@hot']);
            Route::get('/hot/{hot}', ['uses' => 'AiController@hotInfo']);
            Route::post('/hot', ['uses' => 'AiController@hotStore']);
            Route::put('/hot/{hot}', ['uses' => 'AiController@hotUpdate']);
            Route::delete('/hot/{hot}/delete', ['uses' => 'AiController@hotDelete']);
        });

        //渲染风格
        Route::group(['prefix' => '/', 'middleware' => 'admin:aiRenderStyle'], function () {
            Route::get('/render_style', ['uses' => 'AiController@renderStyle']);
            Route::get('/render_style/{category}', ['uses' => 'AiController@renderStyleInfo']);
            Route::post('/render_style', ['uses' => 'AiController@renderStyleStore']);
            Route::put('/render_style/{category}', ['uses' => 'AiController@renderStyleUpdate']);
            Route::delete('/render_style/{category}/delete', ['uses' => 'AiController@renderStyleDelete']);
        });

        //插件
        Route::group(['prefix' => '/plug', 'middleware' => 'admin:aiPlug'], function () {
            Route::get('/{plug}', ['uses' => 'AiController@plugInfo']);
            Route::post('/', ['uses' => 'AiController@plugStore']);
            Route::put('/{plug}', ['uses' => 'AiController@plugUpdate']);

            Route::get('/detail/list', ['uses' => 'AiController@plugDetails']);
            Route::get('/detail/{detail}', ['uses' => 'AiController@plugDetailsInfo']);
            Route::post('/detail', ['uses' => 'AiController@plugDetailsStore']);
            Route::put('/detail/{detail}', ['uses' => 'AiController@plugDetailsUpdate']);
            Route::delete('/detail/{detail}/delete', ['uses' => 'AiController@plugDetailsDelete']);
        });

        //VIP配置
        Route::group(['prefix' => '/vip_setting', 'middleware' => 'admin:aiVipSetting'], function () {
            Route::get('/{setting}', ['uses' => 'AiController@vipSettingInfo']);
            Route::post('/', ['uses' => 'AiController@vipSettingStore']);
            Route::put('/{setting}', ['uses' => 'AiController@vipSettingUpdate']);
        });

        //VIP
        Route::group(['prefix' => '/', 'middleware' => 'admin:aiVip'], function () {
            Route::get('/vip', ['uses' => 'AiController@vip']);
            Route::get('/vip/{vip}', ['uses' => 'AiController@vipInfo']);
            Route::post('/vip', ['uses' => 'AiController@vipStore']);
            Route::put('/vip/{vip}', ['uses' => 'AiController@vipUpdate']);
            Route::delete('/vip/{vip}/delete', ['uses' => 'AiController@vipDelete']);
        });
    });
});
