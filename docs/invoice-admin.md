# 开票系统管理端文档

## 概述

管理端开票系统集成在充值订单管理中，提供开票申请处理、发票上传和状态管理功能。

## 认证

所有管理端API接口都需要在请求头中携带管理员认证token：

```
Authorization: Bearer {admin_token}
```

## 充值订单管理（增强版）

### 1. 获取充值订单列表

**接口地址：** `GET /admin/recharge_order`

**请求参数：**
- `phone`: 用户手机号筛选
- `order_no`: 订单号筛选
- `start_time`: 开始时间
- `end_time`: 结束时间
- `status`: 支付状态筛选（0-未支付，1-已支付，2-已关闭）
- `pay_method`: 支付方式筛选（0-无，1-微信，2-支付宝）
- `invoice_status`: 开票状态筛选（0-未开票，1-已申请，2-已完成）
- `limit`: 每页数量，默认15

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "data": {
            "current_page": 1,
            "data": [
                {
                    "id": 1,
                    "order_no": "R202501101234567",
                    "user_id": 123,
                    "num": 10,
                    "status": 1,
                    "pay_method": 1,
                    "order_amount": "100.00",
                    "total_amount": "100.00",
                    "pay_time": "2025-01-10 10:30:00",
                    "invoice_count": 2,
                    "invoice_status": 1,
                    "invoice_status_text": "已申请",
                    "user": {
                        "id": 123,
                        "name": "张三",
                        "phone": "***********"
                    },
                    "latest_invoice_record": {
                        "id": 5,
                        "status": 1,
                        "status_text": "待处理",
                        "apply_time": "2025-01-10 15:00:00",
                        "invoice_file": "",
                        "invoice_info": {
                            "id": 1,
                            "header_name": "北京科技有限公司",
                            "tax_number": "91110000********9X",
                            "invoice_type_text": "电子普通发票",
                            "header_type_text": "单位"
                        }
                    },
                    "created_at": "2025-01-10 10:00:00",
                    "updated_at": "2025-01-10 15:00:00"
                }
            ],
            "per_page": 15,
            "total": 1
        },
        "wait_count": 5,
        "pending_invoice_count": 3
    }
}
```

**响应字段说明：**
- `data`: 充值订单列表数据
- `wait_count`: 待支付订单数量
- `pending_invoice_count`: 待处理开票申请数量

### 2. 导出充值订单

**接口地址：** `GET /admin/recharge_order/export`

**请求参数：** 同获取充值订单列表

**响应示例：**
```json
{
    "code": 200,
    "message": "导出成功",
    "data": {
        "filename": "20250110120000-充值订单导出.xlsx"
    }
}
```

**导出字段包含：**
- ID
- 用户昵称
- 用户手机号
- 订单号
- 支付金额
- 总金额
- 支付时间
- 支付状态
- 支付方式
- 开票次数
- 开票状态
- 创建时间

## 开票管理功能

### 3. 获取订单开票记录

**接口地址：** `GET /admin/recharge_order/{id}/invoice-records`

**请求参数：** 无

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "recharge_order": {
            "id": 1,
            "order_no": "R202501101234567",
            "user_id": 123,
            "num": 10,
            "order_amount": "100.00",
            "total_amount": "100.00",
            "pay_time": "2025-01-10 10:30:00",
            "invoice_count": 2,
            "invoice_status": 1,
            "user": {
                "id": 123,
                "name": "张三",
                "phone": "***********"
            }
        },
        "invoice_records": [
            {
                "id": 1,
                "user_id": 123,
                "recharge_order_id": 1,
                "invoice_info_id": 1,
                "invoice_no": "********90********90",
                "invoice_amount": "100.00",
                "status": 2,
                "status_text": "已上传",
                "invoice_file": "invoices/2025/01/10/1736496000_invoice.pdf",
                "invoice_file_url": "https://example.com/storage/invoices/2025/01/10/1736496000_invoice.pdf",
                "apply_time": "2025-01-10 15:00:00",
                "upload_time": "2025-01-10 16:00:00",
                "remark": "开票备注",
                "created_at": "2025-01-10 15:00:00",
                "updated_at": "2025-01-10 16:00:00",
                "invoice_info": {
                    "id": 1,
                    "header_name": "北京科技有限公司",
                    "tax_number": "91110000********9X",
                    "invoice_type_text": "电子普通发票",
                    "header_type_text": "单位",
                    "registered_address": "北京市朝阳区xxx街道xxx号",
                    "registered_phone": "010-********",
                    "bank_name": "中国银行北京分行",
                    "bank_account": "********90********9"
                },
                "user": {
                    "id": 123,
                    "name": "张三",
                    "phone": "***********"
                }
            }
        ]
    }
}
```

### 4. 上传发票文件

**接口地址：** `POST /admin/recharge_order/invoice/upload`

**请求参数：**
- `invoice_record_id`: 开票记录ID（必填）
- `invoice_file`: 发票PDF文件（必填，最大10MB）
- `invoice_no`: 发票号码（可选）
- `remark`: 备注（可选）

**请求示例：**
```bash
curl -X POST \
  http://example.com/admin/recharge_order/invoice/upload \
  -H 'Authorization: Bearer {admin_token}' \
  -H 'Content-Type: multipart/form-data' \
  -F 'invoice_record_id=1' \
  -F 'invoice_file=@/path/to/invoice.pdf' \
  -F 'invoice_no=********90********90' \
  -F 'remark=发票已上传'
```

**响应示例：**
```json
{
    "code": 200,
    "message": "上传成功",
    "data": {
        "id": 1,
        "user_id": 123,
        "recharge_order_id": 1,
        "invoice_info_id": 1,
        "invoice_no": "********90********90",
        "invoice_amount": "100.00",
        "status": 2,
        "status_text": "已上传",
        "invoice_file": "invoices/2025/01/10/1736496000_invoice.pdf",
        "invoice_file_url": "https://example.com/storage/invoices/2025/01/10/1736496000_invoice.pdf",
        "apply_time": "2025-01-10 15:00:00",
        "upload_time": "2025-01-10 16:00:00",
        "remark": "发票已上传",
        "invoice_info": {
            "header_name": "北京科技有限公司",
            "tax_number": "91110000********9X"
        },
        "recharge_order": {
            "id": 1,
            "order_no": "R202501101234567",
            "order_amount": "100.00"
        }
    }
}
```

### 5. 更新开票状态

**接口地址：** `PUT /admin/recharge_order/invoice/{id}/status`

**请求参数：**
```json
{
    "status": 2,
    "remark": "发票处理完成"
}
```

**参数说明：**
- `status`: 状态值（1-待处理，2-已上传，3-重开中）
- `remark`: 备注（可选）

**响应示例：**
```json
{
    "code": 200,
    "message": "更新成功",
    "data": {
        "id": 1,
        "user_id": 123,
        "recharge_order_id": 1,
        "invoice_info_id": 1,
        "invoice_no": "********90********90",
        "invoice_amount": "100.00",
        "status": 2,
        "status_text": "已上传",
        "invoice_file": "invoices/2025/01/10/1736496000_invoice.pdf",
        "apply_time": "2025-01-10 15:00:00",
        "upload_time": "2025-01-10 16:00:00",
        "remark": "发票处理完成",
        "invoice_info": {
            "header_name": "北京科技有限公司",
            "tax_number": "91110000********9X"
        },
        "recharge_order": {
            "id": 1,
            "order_no": "R202501101234567",
            "order_amount": "100.00"
        }
    }
}
```

## 管理流程

### 开票处理流程

1. **查看待处理申请**
   - 在充值订单列表中，通过 `pending_invoice_count` 字段查看待处理开票数量
   - 使用 `invoice_status=1` 筛选已申请开票的订单

2. **查看开票详情**
   - 点击订单查看具体的开票记录
   - 查看用户填写的发票信息和开票金额

3. **上传发票文件**
   - 为待处理的开票记录上传PDF格式的发票文件
   - 填写发票号码和处理备注
   - 上传成功后状态自动变为"已上传"，充值订单状态变为"已完成"

4. **状态管理**
   - 可以手动调整开票记录状态
   - 支持重开发票流程

### 筛选和查询

**按开票状态筛选：**
- `invoice_status=0`: 未开票的订单
- `invoice_status=1`: 已申请开票的订单
- `invoice_status=2`: 开票已完成的订单

**组合筛选：**
- 可以结合用户手机号、订单号、时间范围等条件进行筛选
- 支持导出筛选结果

### 提醒机制

**待处理提醒：**
- 接口返回 `pending_invoice_count` 字段显示待处理开票数量
- 建议在管理端首页显示此数量作为提醒

**状态标识：**
- 在订单列表中通过 `invoice_status` 和 `latest_invoice_record` 字段显示开票状态
- 建议对有开票申请的订单进行高亮显示

## 权限说明

开票管理功能集成在充值订单管理权限中：
- 权限标识：`admin:recharge_order`
- 包含功能：查看充值订单、导出订单、查看开票记录、上传发票、更新开票状态

## 文件存储

**发票文件存储路径：**
- 存储位置：`storage/app/public/invoices/YYYY/MM/DD/`
- 文件命名：`{timestamp}_{original_name}`
- 访问URL：`{APP_URL}/storage/invoices/YYYY/MM/DD/{filename}`

**文件限制：**
- 格式：仅支持PDF格式
- 大小：最大10MB
- 安全：上传时会验证文件类型和大小

## 错误处理

**常见错误：**
- "充值订单不存在" - 订单ID无效
- "开票记录不存在" - 开票记录ID无效
- "当前状态不允许上传发票" - 状态不正确
- "发票文件必须为PDF格式" - 文件格式错误
- "发票文件大小不能超过10MB" - 文件过大

**错误码：**
- 200: 成功
- 400: 参数错误
- 401: 未认证
- 403: 权限不足
- 404: 资源不存在
- 500: 服务器错误

## 数据统计

**可统计的数据：**
- 总开票次数
- 待处理开票数量
- 各状态开票记录数量
- 按时间段的开票统计
- 按用户的开票统计

**建议监控指标：**
- 开票处理时效（申请到完成的时间）
- 开票成功率
- 重开发票比例
```
