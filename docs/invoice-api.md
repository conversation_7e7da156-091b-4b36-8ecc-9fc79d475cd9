# 开票系统 API 文档

## 概述

开票系统提供发票信息管理和开票申请功能，支持电子普通发票和电子专票。

## 认证

所有API接口都需要在请求头中携带用户认证token：

```
Authorization: Bearer {token}
```

## 发票信息管理

### 1. 获取发票信息列表

**接口地址：** `GET /api/invoice/info`

**请求参数：** 无

**响应示例：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": [
        {
            "id": 1,
            "user_id": 123,
            "invoice_type": 1,
            "invoice_type_text": "电子普通发票",
            "invoice_content": "技术咨询服务",
            "header_type": 1,
            "header_type_text": "个人",
            "header_name": "张三",
            "tax_number": "",
            "registered_address": "",
            "registered_phone": "",
            "bank_name": "",
            "bank_account": "",
            "is_default": 1,
            "created_at": "2025-01-10 10:00:00",
            "updated_at": "2025-01-10 10:00:00"
        }
    ]
}
```

### 2. 创建发票信息

**接口地址：** `POST /api/invoice/info`

**请求参数：**
```json
{
    "invoice_type": 1,
    "invoice_content": "技术咨询服务",
    "header_type": 2,
    "header_name": "北京科技有限公司",
    "tax_number": "91110000********9X",
    "registered_address": "北京市朝阳区xxx街道xxx号",
    "registered_phone": "010-********",
    "bank_name": "中国银行北京分行",
    "bank_account": "********90********9",
    "is_default": 1
}
```

**参数说明：**
- `invoice_type`: 发票类型，1-电子普通发票，2-电子专票
- `header_type`: 抬头类型，1-个人，2-单位
- 当`header_type=2`时，`tax_number`、`registered_address`、`registered_phone`、`bank_name`、`bank_account`为必填项

**响应示例：**
```json
{
    "code": 200,
    "message": "创建成功",
    "data": {
        "id": 1,
        "user_id": 123,
        "invoice_type": 1,
        "invoice_content": "技术咨询服务",
        "header_type": 2,
        "header_name": "北京科技有限公司",
        "tax_number": "91110000********9X",
        "registered_address": "北京市朝阳区xxx街道xxx号",
        "registered_phone": "010-********",
        "bank_name": "中国银行北京分行",
        "bank_account": "********90********9",
        "is_default": 1,
        "created_at": "2025-01-10 10:00:00",
        "updated_at": "2025-01-10 10:00:00"
    }
}
```

### 3. 更新发票信息

**接口地址：** `PUT /api/invoice/info/{id}`

**请求参数：** 同创建发票信息

**响应示例：** 同创建发票信息

### 4. 删除发票信息

**接口地址：** `DELETE /api/invoice/info/{id}`

**请求参数：** 无

**响应示例：**
```json
{
    "code": 200,
    "message": "删除成功",
    "data": null
}
```

## 开票管理

### 5. 获取可开票的充值订单

**接口地址：** `GET /api/invoice/recharge-orders`

**请求参数：**
- `limit`: 每页数量，默认15

**响应示例：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "order_no": "R202501101234567",
                "user_id": 123,
                "num": 10,
                "status": 1,
                "pay_method": 1,
                "order_amount": "100.00",
                "total_amount": "100.00",
                "pay_time": "2025-01-10 10:30:00",
                "invoice_count": 1,
                "invoice_status": 1,
                "invoice_status_text": "已申请",
                "latest_invoice_record": {
                    "id": 5,
                    "status": 1,
                    "status_text": "待处理",
                    "apply_time": "2025-01-10 15:00:00",
                    "invoice_file": "",
                    "invoice_info": {
                        "header_name": "北京科技有限公司",
                        "tax_number": "91110000********9X"
                    }
                },
                "created_at": "2025-01-10 10:00:00",
                "updated_at": "2025-01-10 15:00:00"
            }
        ],
        "per_page": 15,
        "total": 1
    }
}
```

### 6. 申请开票

**接口地址：** `POST /api/invoice/apply`

**请求参数：**
```json
{
    "recharge_order_id": 1,
    "invoice_info_id": 1,
    "invoice_amount": 100.00,
    "remark": "开票备注"
}
```

**参数说明：**
- `recharge_order_id`: 充值订单ID
- `invoice_info_id`: 发票信息ID
- `invoice_amount`: 开票金额，不能超过订单金额
- `remark`: 备注（可选）

**响应示例：**
```json
{
    "code": 200,
    "message": "申请成功",
    "data": {
        "id": 1,
        "user_id": 123,
        "recharge_order_id": 1,
        "invoice_info_id": 1,
        "invoice_no": "",
        "invoice_amount": "100.00",
        "status": 1,
        "status_text": "待处理",
        "invoice_file": "",
        "apply_time": "2025-01-10 15:00:00",
        "upload_time": null,
        "remark": "开票备注",
        "created_at": "2025-01-10 15:00:00",
        "updated_at": "2025-01-10 15:00:00",
        "invoice_info": {
            "id": 1,
            "header_name": "北京科技有限公司",
            "tax_number": "91110000********9X"
        },
        "recharge_order": {
            "id": 1,
            "order_no": "R202501101234567",
            "order_amount": "100.00"
        }
    }
}
```

### 7. 修改开票申请

**接口地址：** `PUT /api/invoice/modify/{id}`

**请求参数：**
```json
{
    "invoice_info_id": 1,
    "invoice_amount": 100.00,
    "remark": "修改后的备注"
}
```

**说明：** 只有待处理状态的开票申请才能修改

**响应示例：** 同申请开票

### 8. 获取开票记录

**接口地址：** `GET /api/invoice/records`

**请求参数：**
- `limit`: 每页数量，默认15
- `status`: 状态筛选，1-待处理，2-已上传，3-重开中

**响应示例：**
```json
{
    "code": 200,
    "message": "获取成功",
    "data": {
        "current_page": 1,
        "data": [
            {
                "id": 1,
                "user_id": 123,
                "recharge_order_id": 1,
                "invoice_info_id": 1,
                "invoice_no": "********90********90",
                "invoice_amount": "100.00",
                "status": 2,
                "status_text": "已上传",
                "invoice_file": "invoices/2025/01/10/1736496000_invoice.pdf",
                "invoice_file_url": "https://example.com/storage/invoices/2025/01/10/1736496000_invoice.pdf",
                "apply_time": "2025-01-10 15:00:00",
                "upload_time": "2025-01-10 16:00:00",
                "remark": "开票备注",
                "created_at": "2025-01-10 15:00:00",
                "updated_at": "2025-01-10 16:00:00",
                "invoice_info": {
                    "id": 1,
                    "header_name": "北京科技有限公司",
                    "tax_number": "91110000********9X",
                    "invoice_type_text": "电子普通发票",
                    "header_type_text": "单位"
                },
                "recharge_order": {
                    "id": 1,
                    "order_no": "R202501101234567",
                    "order_amount": "100.00",
                    "pay_time": "2025-01-10 10:30:00"
                }
            }
        ],
        "per_page": 15,
        "total": 1
    }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 成功 |
| 400 | 参数错误 |
| 401 | 未认证 |
| 404 | 资源不存在 |
| 500 | 服务器错误 |

## 常见错误信息

- "发票类型无效" - invoice_type 参数不在允许范围内
- "抬头类型无效" - header_type 参数不在允许范围内
- "单位税号不能为空" - 选择单位抬头时必填字段为空
- "充值订单不存在或未支付" - 订单不存在或状态不正确
- "开票金额不能超过订单金额" - 申请金额超出限制
- "该订单已有待处理的开票申请" - 重复申请
- "当前状态不允许修改" - 非待处理状态无法修改
- "该发票信息已有开票记录，无法删除" - 有关联记录的发票信息无法删除

## 业务流程

1. **创建发票信息**：用户首先创建发票信息，支持个人和单位两种类型
2. **选择充值订单**：从已支付的充值订单中选择需要开票的订单
3. **申请开票**：选择发票信息和开票金额，提交开票申请
4. **等待处理**：管理员处理开票申请，上传发票文件
5. **下载发票**：开票完成后，用户可以下载发票文件

## 状态说明

### 充值订单开票状态
- 0: 未开票
- 1: 已申请
- 2: 已完成

### 开票记录状态
- 1: 待处理
- 2: 已上传
- 3: 重开中
```
