<?php

return [
    'wechat_pay' => [
        // 必要配置
        'app_id'     => 'wxb59bcb77b747401b',
        'mch_id'     => '1614983223',
//        'key'        => '60e4234e21e55222597146f4a09c25b8', // API 密钥
        'key'        => env('WXPAY_KEY', ''), // API 密钥
        // 如需使用敏感接口（如退款、发送红包等）需要配置 API 证书路径(登录商户平台下载 API 证书)
        // 'cert_path'  => 'path/to/your/cert.pem', // XXX: 绝对路径！！！！
        // 'key_path'   => 'path/to/your/key', // XXX: 绝对路径！！！！
        'notify_url' => env('WXPAY_NOTIFY_URL', 'https://main.radirhino.com/api/wechat-notify')
    ],
    'alipay'     => [
        //应用ID,您的APPID。
        'app_id'               => "2021002187644000",

        //支付宝公钥,查看地址：https://openhome.alipay.com/platform/keyManage.htm 对应APPID下的支付宝公钥。
//        'alipay_public_key'    => "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAh+MilXxd7K8Z+fAC+wiEopJ0T5rijQPbWXzDT4zYMMNdB6CRpgXgzlbwyoSKcnULxvifyp1JKEfgNXZvWyrG1okoVydQKxwknydAuHAg8he73EsJ4rUd9TMnUx6OmrwFrxUEutfakQtyB4HoOoyWh/TKiq5FnuZkfpR6iG1s04JtKKgH0Va85nvZ0n1bjM7kBUGQcFHicZRdEBZBgCw5Ckf8hL2t3CwwIE4XHwIphzXaLrmURZhT6YIJFjfOr/JK+1SDugITTP8ijTKMQ8jXE0XjuuzMqzTwJP5gHLmgGT/0NiS2ywewm3be6jn7oqTwcHfKS+cBiojcNg2C6d3rRQIDAQAB",
        'alipay_public_key'    => env('ALIPAY_PUBLIC_PRIVATE_KEY', ''),
        //商户私钥
//        'merchant_private_key' => "MIIEpAIBAAKCAQEAgff8z8kVjHxehSXOUWKlUUrzWpBQobu8NmFUgK6Ev235Qacnl4XhmEGNZUvFveHHWvIkoccTbnrFSM8x0RwQ/78tgWscutgmt+gMucgxPGnsiC8ZUZxxGena6SfSl9EfcbkVtG857ewCtWAKQ/btkrllpp108Vd0tQiiX3Xp/2YCzO7nIFTDiVV0dL+f1O4aNaDv6DUFtTer0tik74bKMZRBf7XgXEk2dzA05N9/41gKLwHW0Yznx6rA9hNdfHUU4Gv5OlUpkHEzj38JLMjyngwnd8WF3EntrUbn/+7OThY268hKwtN5v6VQVHvIY5i3M4puO+3cZTpKVzVTJ6v+rwIDAQABAoIBAE6flIKmpLCN9CgcDMnTISsiakEZGI4eoedLzfDiok+j2avL0Pmq9iRDU6Q5yG/sC8uKvHpZkfm9r2cyYHKdxfLQ1lh6UbD+Xb3XVQt2fcKMWZiw1WI3f3wrqlBt1MoeqiUD0IuM1cCGi8TpSox+7o1aNpIqorn1LBbwGDojfOERGiS0jogVot4OKbPy5p1+jwA5yBP0J8SMon8CH8WQzIK2pUz/KBd+FiQCuItMXEHxzj4fdcZrSVtMCV5lvpm/LwHe2SQfyO/oXZ2Sf9SbfQg1+Cm8ZamM3kHKL8GeKOGwwo64YUDSAjXh7PQYpkcJgwIgHAnUizMLD66yOj3m0JECgYEAvy/vPEXEtNR6uR0oZzLNL7KfjwI8O1XZEetlxVjhefPZKTvyVRBQxrYkFXeKCKdY3uoIk8br40SwhC46K7rNKznhmEC3LKsePo1YEFpZBMHiirGs9hrrZvpVrR8ec+uFkEa/2ZHe/jclwqKwmfJTaMuV3CmHTeOUltHs0XImQm0CgYEArgc9dEy2CEBVxI/ujpNLntQ0lOs2qijk+wWZfrhptqeU8iTesiOFkPgR0HtdqAJ54SECYUmeN3e4W3mh3POcGqqYuFV/EG/mHlWkc3mNdBsjmtasnWf1zjAx9Zgh/nZJmEzcbi/3IuCV9FMBXgvrtOVvscORaZzrqov5MacNNAsCgYEAsWFD0OPaMPvCMOVAix6zCEHtj70/LL3le56u+X4RFBiNrpgTqaBNkU+EAva5dnXtGkSAWLsCOAWOz9Qj3Faf7C7zg1Lnn+XTVa3FusZYOPW5vXxDQMKRDSXNP6XTlzE45jb94FpaTfnUatSOjQYrJNtd2roc8sLnJVb3JQuHO7ECgYAvKQceXSt3UPftLJQZTxFPGXKmaJeHxU1IOVw/WvpK5V8Xv6YZ4EZx4sfxF31p6kHdIHTkqG6X2Xm8UFns5peHInQAgCrO6YQS9GJ8aJRMwo4Dh+l/Poz+M1FWnMOZkbSf8TRLJ/RxHyuD6nqUYeaePw7lvXnKhRvYO9Zr/jk1wQKBgQCNKzeq5lnkdG/W2wUDPwtqNZYImzjCAnUZWafEZa4NZ+Xvxfqecqqt8YT63uqFMzU/CV3SA2RX7aEuJao7qjRmUZLT8oflFc+cHcXTTDrP+TWxddundZUcXOZ96asBmYwTxNtpc89Urc6GNYJXv41NbQiREXCXDCaVuJV69Zxsiw==",
        'merchant_private_key' => env('ALIPAY_MERCHANT_PRIVATE_KEY', ''),
        //异步通知地址
        'notify_url'           => env('ALIPAY_NOTIFY_URL', 'https://main.radirhino.com/api/alipay-notify'),

        //同步跳转
        'return_url'           => env('RETURN_NOTIFY_URL', 'https://www.radirhino.com/#/payment-result'),

        //编码格式
        'charset'              => "UTF-8",

        //签名方式
        'sign_type'            => "RSA2",

        //支付宝网关
        'gatewayUrl'           => "https://openapi.alipay.com/gateway.do"
    ]
];
