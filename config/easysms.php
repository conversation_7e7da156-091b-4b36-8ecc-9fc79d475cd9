<?php

return [
    // HTTP 请求的超时时间（秒）
    'timeout' => 10.0,
    // 短信验证码有效时间（单位：秒）
    'valid_time' => 600,

    // 默认发送配置
    'default' => [
        // 网关调用策略，默认：顺序调用
        'strategy' => \Overtrue\EasySms\Strategies\OrderStrategy::class,

        // 默认可用的发送网关
        'gateways' => [
            'aliyun',
        ],
    ],
    // 可用的网关配置
    'gateways' => [
        'errorlog' => [
            'file' => '/tmp/easy-sms.log',
        ],
        'aliyun' => [
            'access_key_id' => env('EASYSMS_ACCESS_KEY'),
            'access_key_secret' => env('EASYSMS_ACCESS_SECRET'),
            'sign_name' => '犀光'
        ],
    ],
];
