{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^7.3|^8.0", "alibabacloud/cloudauth-20190307": "^2.0", "alibabacloud/sts": "^1.8", "aliyuncs/oss-sdk-php": "^2.7", "codingyu/laravel-ueditor": "~2.0", "codingyu/ueditor": "^3.0", "encore/laravel-admin": "1.*", "endroid/qr-code": "^4.3", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0.1", "jacobcyl/ali-oss-storage": "^2.1", "laravel/framework": "^8.54", "laravel/sanctum": "^2.11", "laravel/socialite": "^5.2", "laravel/tinker": "^2.5", "medz/id-card-of-china": "^1.1", "overtrue/easy-sms": "^2.0", "overtrue/wechat": "~5.0", "rap2hpoutre/fast-excel": "^3.1", "socialiteproviders/github": "^4.1", "socialiteproviders/qq": "^4.1", "socialiteproviders/wechat-web": "^4.1", "socialiteproviders/weixin": "^4.1", "socialiteproviders/weixin-web": "^4.1"}, "require-dev": {"facade/ignition": "^2.5", "fakerphp/faker": "^1.9.1", "laravel/sail": "^1.0.1", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^5.0", "phpunit/phpunit": "^9.3.3"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"easywechat-composer/easywechat-composer": true}}, "minimum-stability": "dev", "prefer-stable": true}