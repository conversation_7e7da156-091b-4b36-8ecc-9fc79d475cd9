<?php

namespace App\Console\Commands;

use App\Jobs\SendMessageJob;
use App\Models\Message;
use App\Models\Order;
use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletLog;
use App\Services\PaymentService;
use App\Services\ShenzaoService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Overtrue\EasySms\EasySms;

class guangliExchange extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'guangliExchange';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = '光粒兑换';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    public function handle()
    {
        $wallets = Wallet::query()->where('income', '>', 0)->get();
        foreach ($wallets as $wallet) {
            $this->handleWalletInfo($wallet);
        }
    }

    public function handleWalletInfo($wallet)
    {
        $balance = $wallet->balance > 100 ? $wallet->balance : 100;
        $balance = bcdiv($balance, 1000, 2);

        try {
            $wallet->income = $balance;
            $wallet->balance = $balance;
            $wallet->expend = 0;
            $wallet->save();

            $walletLog = new WalletLog();
            $walletLog->title = '光粒1000比1兑换光子';
            $walletLog->target_type = '';
            $walletLog->target_id = 0;
            $walletLog->amount = $wallet->balance;
            $walletLog->user_id = $wallet->user_id;
            $walletLog->currency = 'integral';
            $walletLog->action = WalletLog::ActionMappings['income'];
            $walletLog->status = WalletLog::StatusMappings['success'];
            $walletLog->type = WalletLog::TypeMappings['exchange'];
            $walletLog->extend = ['balance'=>$wallet->balance];
            $walletLog->save();

        } catch (\Exception $exception) {
            logger()->error($exception);
        }
    }
}
