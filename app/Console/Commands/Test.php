<?php

namespace App\Console\Commands;

use App\Jobs\SendMessageJob;
use App\Models\Message;
use App\Models\Order;
use App\Models\User;
use App\Services\PaymentService;
use App\Services\ShenzaoService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Overtrue\EasySms\EasySms;

class Test extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'test';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'test';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    // 获取在线网页数据




    public function handle()
    {

        dd($productTypeMap = array_flip(Order::ProductTypeMappings));
        $str = sprintf('犀光_%s_订单支付', 'dfandfjna');
        dd(explode('_', $str));

        die();
        try {
            app('easysms')->send('460224400', [
                'template' => 'SMS_226985028',
                'data' => ['code' => 985028],
            ]);
        } catch (\Exception $exception) {
            Log::error('eee', [$exception->getExceptions()]);
        }


        dd();
//        $courseInfos = [
//            [
//                "courseId" => "CI_dLxfwazMGQPk85gReKp5TLb5EvSCC",
//                "expireDay" => 0
//            ],
//            [
//                "courseId" => "CI_dLxfwazMGQPk85gReKp5TLb5EvSCC1",
//                "expireDay" => 0
//            ]
//        ];
//        $courseId = 'CI_dLxfwazMGQPk85gReKp5TLb5EvSCC';
//
//        $courseInfos = array_values(array_filter($courseInfos, function ($item) use ($courseId) {
//            return $courseId !== $item['courseId'];
//        }));
////        $courseInfos = collect($courseInfos)->where('courseId', '!=' ,$courseId)->map(function ($value) {
////            return $value->toArray();
////        });
//        dd($courseInfos);

//        $courseDatas = [];
//        dd(current($courseDatas));
//        $data = (new ShenzaoService())->getUserByUserName('13800138000');
        $data = (new ShenzaoService())->authorizeUserCourseInfo('13800138002', 1,'CI_dLxfwazMGQPk85gReKp5TLb5EvSCC');
//        $data = (new ShenzaoService())->findByLicenseCode('KettyBIM单元幕墙GH系');
        dump($data);

        return;
        $order = Order::query()->first();
//        dump($order->toArray());
//        Log::info('111',[$order->user->phone, 'publish', $order->product_id, $order->product_type, [
//            'name' => $order->user->name,
//            'code' => $order->order_no,
//            'time' => $order->created_at,
//            'commodity' => $order->product_name,
//            'money' => $order->order_amount,
//        ]]);
//        die();


        dispatch(new SendMessageJob('13147115671', 'publish', $order->product_id, $order->product_type, [
            'name' => $order->user->name,
            'code' => $order->order_no,
            'time' => $order->created_at,
            'commodity' => $order->product_name,
            'money' => $order->order_amount,
        ]));
//        PaymentService::getInstance()->smsPublish($order);
    }
}
