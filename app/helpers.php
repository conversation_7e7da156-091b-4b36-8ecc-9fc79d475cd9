<?php
use Illuminate\Support\Facades\Storage;

if ( ! function_exists('is_dev')) {
    /**
     * @return bool|string
     */
    function is_dev()
    {
//        return app()->environment(['dev', 'local']);
        return app()->environment(['dev']);
    }
}

if ( ! function_exists('is_phone')) {
    /**
     * 手机号格式验证
     * @return bool|string
     */
    function is_phone($number)
    {
        return (bool) preg_match('/^(\+?0?86\-?)?1[3-9]\d{9}$/', $number);
    }
}

if ( ! function_exists('is_email')) {
    /**
     * 邮箱格式验证
     * @return bool|string
     */
    function is_email($email)
    {
        return (bool) preg_match('/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/', $email);
    }
}

if ( ! function_exists('alipay_order_no')) {
    /**
     * 支付宝订单号生成
     * @return mixed
     */
    function alipay_order_no()
    {
        @date_default_timezone_set("PRC");
        $order_id_main = date('YmdHis') . rand(10000000, 99999999);
        $order_id_len  = strlen($order_id_main);
        $order_id_sum  = 0;
        for ($i = 0; $i < $order_id_len; $i++) {
            $order_id_sum += (int) (substr($order_id_main, $i, 1));
        }
        $order_no = $order_id_main . str_pad((100 - $order_id_sum % 100) % 100, 2, '0', STR_PAD_LEFT);
        return $order_no;
    }
}

if ( ! function_exists('wechat_order_no')) {
    /**
     * 微信订单号生成
     * @return mixed
     */
    function wechat_order_no()
    {
        return strtoupper(date('YmdHis') . \Str::random(18));
    }
}

if (!function_exists('upload')) {
    /**
     * 上传图片到阿里云
     * @param $path
     * @param $file
     * @param string $drive
     * @return bool
     */
    function upload($path, $file, $drive = 'oss')
    {
        $disk = Storage::disk($drive);

        //将图片上传到OSS中，并返回图片路径信息 值如:avatar/WsH9mBklpAQUBQB4mL.jpeg
        $path = $disk->put($path, $file);

        return $path;
    }
}

if (!function_exists('request_ip')) {
    /**
     * 获取请求IP
     * @return array|false|mixed|string
     */
    function request_ip()
    {
        if (getenv("HTTP_CLIENT_IP") && strcasecmp(getenv("HTTP_CLIENT_IP"), "unknown")) {
            $ip = getenv("HTTP_CLIENT_IP");
        } elseif (getenv("HTTP_X_FORWARDED_FOR") && strcasecmp(getenv("HTTP_X_FORWARDED_FOR"), "unknown")) {
            $ip = getenv("HTTP_X_FORWARDED_FOR");
        } elseif (getenv("REMOTE_ADDR") && strcasecmp(getenv("REMOTE_ADDR"), "unknown")) {
            $ip = getenv("REMOTE_ADDR");
        } elseif (isset($_SERVER['REMOTE_ADDR']) && $_SERVER['REMOTE_ADDR'] && strcasecmp($_SERVER['REMOTE_ADDR'], "unknown")) {
            $ip = $_SERVER['REMOTE_ADDR'];
        } else {
            $ip = "unknown";
        }

        return $ip;
    }
}

if (!function_exists('get_class_name')) {
    /**
     * 获取类名
     * @param string $class
     * @return string
     */
    function get_class_name(string $class)
    {
        return basename(str_replace('\\', '/', $class));
    }
}

if (!function_exists('substr_with_powerful')) {
    /**
     * 清除html,换行，空格类并且可以截取内容长度
     * @param $str
     * @param int $length
     * @param bool $clearSpace
     * @param string $charset
     * @return string
     */
    function substr_with_powerful($str, $length=-1, $clearSpace = true, $charset = 'utf-8')
    {
        $str = strip_tags(trim($str), ""); // 利用php自带的函数清除html格式
        $str = preg_replace("/\r/", "", $str);
        $str = preg_replace("/\n/", "", $str);
        // 判断是否清除空格
        if ($clearSpace) {
            $str = preg_replace("/\t/", "", $str);
            $str = preg_replace("/ /", "", $str);
            $str = preg_replace("/&nbsp;/", "", $str); // 匹配html中的空格
        }
        $str = trim($str); // 清除字符串两边的空格

        if ($length!=-1) {
            if (function_exists("mb_substr")) {
                $substr = mb_substr($str, 0, $length, $charset);
            } else {
                $c['utf-8'] = "/[\x01-\x7f]|[\xc2-\xdf][\x80-\xbf]|[\xe0-\xef][\x80-\xbf]{2}|[\xf0-\xff][\x80-\xbf]{3}/";
                $c['gbk']   = "/[\x01-\x7f]|[\x81-\xfe][\x40-\xfe]/";
                preg_match_all($c[$charset], $str, $match);
                $substr = join("", array_slice($match[0], 0, $length));
            }
        } else {
            $substr = $str;
        }

        return $substr;
    }
}

if (!function_exists('handle_url')) {
    /**
     * 全链接地址处理
     * @param $link
     * @return string|null
     */
    function handle_url($link)
    {
        $preg = "/http[s]?:\/\/[\w.]+[\w\/]*[\w.]*\??[\w=&\+\%]*/is";
        if(!preg_match($preg,$link)){
            $link = $link ? config('app.download_url').'/'.$link : $link;
        }
        return $link;
    }
}

if (!function_exists('is_mobile')) {
    /**
     * 是否为手机端
     * @return bool
     */
    function is_mobile()
    {
        // 如果有HTTP_X_WAP_PROFILE则一定是移动设备
        if (isset($_SERVER['HTTP_X_WAP_PROFILE'])) {
            return true;
        }
        // 如果via信息含有wap则一定是移动设备,部分服务商会屏蔽该信息
        if (isset($_SERVER['HTTP_VIA'])) {
            // 找不到为flase,否则为true
            return stristr($_SERVER['HTTP_VIA'], "wap") ? true : false;
        }
        // 脑残法，判断手机发送的客户端标志,兼容性有待提高。其中'MicroMessenger'是电脑微信
        if (isset($_SERVER['HTTP_USER_AGENT'])) {
            $clientkeywords = array(
                'nokia','sony','ericsson','mot','samsung','htc','sgh','lg','sharp','sie-','philips','panasonic','alcatel','lenovo','iphone','ipod','blackberry','meizu','android','netfront','symbian','ucweb','windowsce','palm','operamini','operamobi','openwave','nexusone','cldc','midp','wap','mobile','MicroMessenger',
                'UCBrowser', 'MZBrowser', 'MiuiBrowser', 'HuaweiBrowser'
            );            // 从HTTP_USER_AGENT中查找手机浏览器的关键字
            if (preg_match("/(" . implode('|', $clientkeywords) . ")/i", strtolower($_SERVER['HTTP_USER_AGENT']))) {
                return true;
            }
        }
        // 协议法，因为有可能不准确，放到最后判断
        if (isset ($_SERVER['HTTP_ACCEPT'])) {
            // 如果只支持wml并且不支持html那一定是移动设备
            // 如果支持wml和html但是wml在html之前则是移动设备
            if ((strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') !== false) && (strpos($_SERVER['HTTP_ACCEPT'], 'text/html') === false || (strpos($_SERVER['HTTP_ACCEPT'], 'vnd.wap.wml') < strpos($_SERVER['HTTP_ACCEPT'], 'text/html')))) {
                return true;
            }
        }
        return false;
    }
}

if (!function_exists('is_wechat')) {
    /**
     * 是否为微信浏览器
     * @return bool
     */
    function is_wechat()
    {
        if (stristr($_SERVER['HTTP_USER_AGENT'], "micromessenger") || stristr($_SERVER['HTTP_USER_AGENT'], "weixin")) {
            return true;
        }
        return false;
    }
}

if (!function_exists('coupon_code')) {
    /**
     * 生成优惠码
     * @return string
     */
    function coupon_code()
    {
        static $source_string = 'EFCDGHQABNOPIJRSTUVMWXKLYZ5438790216';
        $num = mt_rand(100000, 999999) + time();
        $code = '';
        $val = mt_rand(20, 40);
        while ($num > 0) {
            $mod = $num % $val;
            $num = ($num - $mod) / $val;
            $code = $source_string[$mod] . $code;
        }

        if (empty($code[5])) {
            $code = str_pad($code, 6, '0', STR_PAD_LEFT);
        }

        return $code;
    }
}


function get_button($name, $url)
{
    return '<div><a href="'.$url.'"><button class="btn btn-sm btn-success" style="margin-top: 3px">'.$name.'</button></a></div>';
}


/**
 * 验证是否是中国验证码.
 *
 * @param string $number
 * @return bool
 */
function validateChinaPhoneNumber(string $number)
{
    return (bool)preg_match('/^(\+?0?86\-?)?1[3-9]\d{9}$/', $number);
}







