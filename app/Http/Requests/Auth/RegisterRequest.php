<?php

/*
 * This file is part of the Qsnh/meedu.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 */

namespace App\Http\Requests\Auth;

use App\Http\Requests\BaseRequest;

class RegisterRequest extends BaseRequest
{
    public function rules()
    {
        switch ($this->method()) {
            case "POST":
                return [
                    'name' => 'required|unique:users',
                    'phone' => 'required｜unique:users',
                    'code' => 'required',
                    'password' => 'require|min:6',
                    // todo 注册时密码确认是否需要
                ];
            default:
                return [];
        }
    }

    public function messages()
    {
        return [
            'name.required' => '用户名不能为空',
            'name.unique' => '用户昵称已存在',
            'phone.required' => '手机号不能为空',
            'phone.unique' => '手机号已被注册',
            'code.required' => '验证码不能为空',
            'password.required' => '密码不能为空',
            'password.min' => '密码长度最小6位',
        ];
    }
}
