<?php
namespace App\Http\Requests\Auth;

use App\Http\Requests\BaseRequest;

class PasswordLoginRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'phone' => 'bail|required',
            'password' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'phone.required' => '请输入账号',
            'password.required' => '请输入密码',
        ];
    }

    public function filldata()
    {
        return [
            'phone' => $this->post('phone'),
            'password' => $this->post('password'),
        ];
    }
}
