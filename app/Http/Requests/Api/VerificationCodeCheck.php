<?php

namespace App\Http\Requests\Api;

use App\Http\Requests\BaseRequest;

class VerificationCodeCheck extends BaseRequest
{
    /**
     * @return string[]
     */
    public function rules()
    {
        return [
            'scene' => 'required|in:register,login',
            'code' => 'required',
        ];
    }

    /**
     * @return string[]
     */
    public function messages()
    {
        return [
            'scene.required' => '发送场景错误',
            'scene.in' => '发送场景错误',
            'code.required' => '验证码错误',
        ];
    }
}
