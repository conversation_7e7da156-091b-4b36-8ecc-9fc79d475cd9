<?php

namespace App\Http\Requests\Api;

use App\Http\Requests\BaseRequest;

class UserAddressRequest extends BaseRequest
{
    public function rules()
    {
        switch ($this->method()) {
            case "POST":
                return [
                    'name' => 'required|max:30',
                    'phone' => 'required|cn_phone',
                    'address' => 'required',
                ];
            case "PATCH":
            case "PUT":
                return [
                    'name' => 'nullable',
                    'phone' => 'nullable',
                    'address' => 'nullable'
                ];
            default :
                return [];
        }

    }

    public function messages()
    {
        return [
            'name.required' => '名称不能为空',
            'name.max' => '名称长度最多30位',
            'phone.required' => '联系电话不能为空',
            'phone.cn_phone' => '联系电话格式错误',
            'address.required' => '详细地址不能为空',
        ];
    }
}
