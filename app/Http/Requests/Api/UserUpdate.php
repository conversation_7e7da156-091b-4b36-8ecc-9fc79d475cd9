<?php

namespace App\Http\Requests\Api;

use App\Http\Requests\BaseRequest;

class UserUpdate extends BaseRequest
{
    public function rules()
    {
        return [
            'type' => 'required|in:name,avatar,sign,intro,company,occupation,wechat,qq,sex',
            'value' => 'required'
        ];
    }

    public function messages()
    {
        return [
            'type.required' => '类型错误',
            'type.in' => '不支持当前类型修改',
            'value.required' => '数据不能为空',
        ];
    }
}
