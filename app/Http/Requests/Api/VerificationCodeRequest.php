<?php

namespace App\Http\Requests\Api;

use App\Http\Requests\BaseRequest;

class VerificationCodeRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'scene' => 'required|in:register,login,set-phone',
            'phone' => 'required',
        ];
    }


    public function messages()
    {
        return [
            'phone.required' => '手机号不能为空',
            'phone.cn_phone' => '手机号格式错误',
            'scene.required' => '发送场景错误',
            'scene.in' => '发送场景错误',
        ];
    }
}
