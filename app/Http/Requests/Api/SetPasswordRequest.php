<?php
namespace App\Http\Requests\Api;

use App\Http\Requests\BaseRequest;

class SetPasswordRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'old_password' => 'bail|required',
            'password' => 'required|min:6|max:32',
        ];
    }

    public function messages()
    {
        return [
            'old_password.required' => '请输入旧密码',
            'password.required' => '新密码不能为空',
            'password.min' => '密码最少6为',
            'password.max' => '密码最多32为',
        ];
    }
}
