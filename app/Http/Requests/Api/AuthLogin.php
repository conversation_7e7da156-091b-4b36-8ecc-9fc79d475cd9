<?php

namespace App\Http\Requests\Api;

use App\Http\Requests\BaseRequest;

class AuthLogin extends BaseRequest
{
    public function rules()
    {
        return [
            'phone' => 'required',
            'type' => 'required|string|in:pass,code',
            'password' => 'required_if:type,pass|string|min:6',
            'code' => 'required_if:type,code',
        ];
    }

    public function messages()
    {
        return [
            'phone.required' => '请输入正确账号',
            'password.required_if' => '请输入密码',
            'password.string' => '密码格式错误',
            'password.min' => '密码长度最少6位',
            'code.required_if' => '请输入验证码',
            'code.string' => '验证码型格式错误',
            'type.required' => '登陆方式错误',
            'type.in' => '仅支持密码或手机登陆',
        ];
    }
}
