<?php

namespace App\Http\Requests\Api;

use App\Http\Requests\BaseRequest;

class RegisterRequest extends BaseRequest
{
    public function rules()
    {
        return [
            'name' => 'required|unique:users',
//            'phone' => 'required|unique:users',
            'phone' => 'required',
            'code' => 'required',
            'password' => 'required|string|min:6|confirmed|regex:/^[a-zA-Z\d_]{6,}$/',
            'password_confirmation' => 'required|string|min:6|regex:/^[a-zA-Z\d_]{6,}$/',
            // todo 注册时密码确认是否需要
        ];
    }

    public function messages()
    {
        return [
            'name.required' => '用户名不能为空',
            'name.unique' => '用户昵称已存在',
            'phone.required' => '账号不能为空',
            'phone.unique' => '手机号已被注册',
            'code.required' => '验证码不能为空',
            'password.required' => '请输入密码',
            'password.string' => '密码格式错误',
            'password.min' => '密码长度最小6位',
            'password.confirmed' => '两次密码不一致',
            'password.regex' => '密码格式错误',
            'password_confirmation.required' => '确认密码错误',
            'password_confirmation.string' => '确认密码错误',
            'password_confirmation.min' => '确认密码长度错误',
            'password_confirmation.regex' => '确认密码格式错误',
        ];
    }
}
