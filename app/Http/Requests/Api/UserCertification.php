<?php

namespace App\Http\Requests\Api;

use App\Http\Requests\BaseRequest;

class UserCertification extends BaseRequest
{
    public function rules()
    {
        return [
            'name' => 'required',
            'number' => 'required',
            'fpic' => 'required',
            'bpic' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'name.required' => '请填写真实姓名',
            'number.required' => '请填写身份证号',
            'bpic.required' => '请上传身份证反面照',
            'fpic.required' => '请上传正面照',
        ];
    }
}
