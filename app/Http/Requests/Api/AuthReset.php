<?php

namespace App\Http\Requests\Api;

use App\Http\Requests\BaseRequest;

class AuthReset extends BaseRequest
{
    public function rules()
    {
        return [
            'email' => 'required|email',
            'password' => 'required|string|min:6|regex:/^[a-zA-Z\d_]{6,}$/',
//            'password_confirmation' => 'required|string|min:6|regex:/^[a-zA-Z\d_]{6,}$/',
        ];
    }

    public function messages()
    {
        return [
            'email.required' => '邮箱不能为空',
            'email.email' => '邮箱格式错误',
            'password.required' => '请输入密码',
            'password.string' => '密码格式错误',
            'password.min' => '密码长度最小6位',
//            'password.confirmed' => '两次密码不一致',
            'password.regex' => '密码格式错误',
//            'password_confirmation.required' => '确认密码错误',
//            'password_confirmation.string' => '确认密码错误',
//            'password_confirmation.min' => '确认密码长度错误',
//            'password_confirmation.regex' => '确认密码格式错误',
        ];
    }
}
