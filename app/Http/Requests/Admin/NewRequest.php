<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;

class NewRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch ($this->method()) {
            case "POST":
                return [
                    'title' => 'required',
                    'content' => 'required',
                    'author' => 'required',
                    'images' => 'required',
                ];
            case "PUT":
                return [];
            default :
                return [];

        }

    }

    public function messages()
    {
        return [
            'title.required' => '标题不能为空',
            'author.required' => '作者不能为空',
            'content.required' => '内容不能为空',
            'images.required' => '封面图不能为空',
        ];
    }
}
