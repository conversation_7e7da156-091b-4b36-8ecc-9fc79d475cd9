<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;

class PlugRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch ($this->method()) {
            case "POST":
                return [
                    'name' => 'required',
                    'cate_id' => 'required|exists:plug_category,id',
                    'intro' => 'required',
//                    'images' => 'required',
                    'give_integral' => 'required',
                    'is_integral' => 'required',
                    'price' => 'required',
                    'web_link' => 'nullable|url',
                ];
            case "PUT":
                return [
                    'web_link' => 'nullable|url',
                ];
            default :
                return [];

        }

    }

    public function messages()
    {
        return [
            'name.required' => '名称不能为空',
            'author_id.required' => '讲师不能为空',
            'cate_id.required' => '分类不能为空',
            'cate_id.exists' => '分类不存在',
            'intro.required' => '介绍不能为空',
            'images.required' => '封面图不能为空',
            'is_integral.required' => '光粒抵扣不能为空',
            'give_integral.required' => '赠送光粒数量不能为空',
            'price.required' => '售卖价格不能为空',
            'web_link.url' => '功能介绍网站链接类型错误',
        ];
    }
}
