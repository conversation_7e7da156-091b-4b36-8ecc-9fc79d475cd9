<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;

class PlugDetailRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'version_code' => 'required',
            'platform' => 'required',
            'description' => 'required',
            'type' => 'required',
            // 'link' => 'required',
        ];
    }

    public function messages()
    {
        return [
            'version_code.required' => '版本号不能为空',
            'platform.required' => '品台不能为空',
            'description.required' => '秒描述不能为空',
            'type.required' => '类型不能为空',
            'link.required' => '文件不能为空',
        ];
    }
}
