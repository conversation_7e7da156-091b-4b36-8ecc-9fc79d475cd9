<?php

namespace App\Http\Requests\Admin;

use Illuminate\Foundation\Http\FormRequest;

class RechargeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'amount' => 'required|numeric|min:0.01|max:999999.99',
            'remark' => 'nullable|string|max:255',
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages()
    {
        return [
            'phone.required' => '手机号不能为空',
            'phone.regex' => '手机号格式不正确',
            'amount.required' => '充值金额不能为空',
            'amount.numeric' => '充值金额必须为数字',
            'amount.min' => '充值金额不能小于0.01',
            'amount.max' => '充值金额不能超过999999.99',
            'remark.max' => '备注不能超过255个字符',
        ];
    }
}
