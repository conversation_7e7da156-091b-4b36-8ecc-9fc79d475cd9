<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;

class CourseDetailRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch ($this->method()) {
            case "POST":
                return [
                    'name' => 'required',
                    'type' => 'required',
                    'course_id' => 'required',
                ];
            case "PUT":
                return [];
            default :
                return [];

        }

    }

    public function messages()
    {
        return [
            'name.required' => '名称不能为空',
            'type.required' => '请选择类型',
            'course_id.required' => '请选择课程',
        ];
    }
}
