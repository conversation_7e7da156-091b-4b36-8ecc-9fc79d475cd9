<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;
use Illuminate\Validation\Rule;

class RoleRequest extends FormRequest
{
    public function rules()
    {
        switch ($this->method()) {
            case "POST":
                return [
                    'name' => 'required|unique:as_roles,name',
                    'alias' => 'required|unique:as_roles,alias',
                ];
            case "PATCH":
            case "PUT":
                return [
                    'name' => [
                        'nullable',
                        Rule::unique('as_roles')->ignore($this->segment(3))
                    ],
                    'alias' => [
                        'nullable',
                        Rule::unique('as_roles')->ignore($this->segment(3))
                    ],
                    'permissions' => 'nullable|array',
                    'permissions.*' => 'required_unless:permissions,null|exists:as_authorities,id'
                ];
            default :
                return [];
        }

    }

    public function messages()
    {
        return [
            'name.required' => '请填写角色名称',
            'name.unique' => '角色名称已经存在',
            'alias.required' => '请填写角色别名',
            'alias.unique' => '角色别名已经存在',
            'permissions.array' => '权限格式不正确',
            'permissions.*.required_unless' => '权限不能为空',
            'permissions.*.exists' => '权限不存在',
        ];
    }
}
