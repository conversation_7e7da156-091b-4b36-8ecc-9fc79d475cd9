<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;

class CreateSensitive extends FormRequest
{
    /**
     * @return array|string[]
     */
    public function rules(): array
    {
        $method = $this->method();
        return [
            'word' => 'required|string|unique:sensitives,word' . ($method === "PUT" ? (',' . $this->segment(2)) : ''),
            'type' => 'required|string|in:replace,warning',
            'replace' => 'required_if:type,replace|nullable|string',
        ];
    }

    /**
     * @return array|string[]
     */
    public function messages()
    {
        return [
            'word.required' => '敏感词不能为空',
            'word.unique' => '敏感词已存在',
            'type.required' => '敏感词类型不能为空',
            'type.in' => '敏感词类型错误',
            'replace.required_if' => '敏感词替换词不能为空',
        ];
    }
}
