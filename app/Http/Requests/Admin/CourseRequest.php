<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;

class CourseRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch ($this->method()) {
            case "POST":
                return [
                    'name' => 'required',
                    'user_id' => 'required',
                    'cate_id' => 'required',
                    'explain' => 'required',
                    'intro' => 'required',
//                    'images' => 'required',
                    'give_integral' => 'required',
                    'is_integral' => 'required',
                    'is_publish' => 'required',
                    'market_price' => 'required',
                    'price' => 'required',
//                    'num' => 'integer',
//                    'sort' => 'number',
                ];
            case "PUT":
                return [];
            default :
                return [];

        }

    }

    public function messages()
    {
        return [
            'name.required' => '名称不能为空',
            'user_id.required' => '推荐人不能为空',
            'cate_id.required' => '分类不能为空',
            'explain.required' => '简介不能为空',
            'intro.required' => '介绍不能为空',
            'images.required' => '封面图不能为空',
            'give_integral.required' => '赠送光粒不能为空',
            'is_integral.required' => '赠送光粒不能为空',
            'is_publish.required' => '发布状态不能为空',
            'market_price.required' => '划线价格不能为空',
            'price.required' => '售卖价格不能为空',
            'num.*' => '购买量错误',
            'sort.*' => '排序错误',
        ];
    }
}
