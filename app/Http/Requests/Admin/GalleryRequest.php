<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;

class GalleryRequest extends FormRequest
{
    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        switch ($this->method()) {
            case "POST":
                return [
                    'name' => 'required',
                    'cate_id' => 'required',
                    'cover' => 'required',
                    // 'desc' => 'required',
//                    'num' => 'integer',
//                    'sort' => 'number',
                ];
            case "PUT":
                return [];
            default :
                return [];

        }

    }

    public function messages()
    {
        return [
            'name.required' => '名称不能为空',
            'cate_id.required' => '分类不能为空',
            'desc.required' => '介绍不能为空',
            'cover.required' => '封面图不能为空',
        ];
    }
}
