<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;

class GoodsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required', // 商品名称
            'desc' => 'required', // 商品介绍
            'images' => 'required|array',
//            'cover' => 'required',
//            'stock' => 'required',
            'cate_id' => 'required|integer|exists:goods_category,id',
        ];
    }

    public function messages()
    {
        return [
            'name.required' => '商品名称不能为空',
            'images.required' => '商品图片不能为空',
            'images.array' => '商品图片格式错误',
            'desc.required' => '商品介绍不能为空',
            'limit_num.integer' => '限量必须是整型',
            'sort.required' => '商品排序不能为空',
            'sort.integer' => '商品排序必须是整型',
            'cover.required' => '封面图不能为空',
            'stock.required' => '库存不能为空',
            'cate_id.required' => '分类不能为空',
            'cate_id.integer' => '分类必须是整型',
            'cate_id.exists' => '分类不存在',
        ];
    }
}
