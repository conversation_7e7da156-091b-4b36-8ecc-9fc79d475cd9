<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;
use Illuminate\Validation\Rule;

class AdminRequest extends FormRequest
{

    public function rules()
    {
        $id = $this->segment(3);

        switch ($this->method()) {
            case "POST":
                return [
                    'name' => 'required|unique:as_admins',
                    'phone' => 'required|cn_phone|unique:as_admins',
                    'email' => 'required|email|unique:as_admins',
                    'password' => 'required|between:6,20',
                    'state' => 'required|in:0,1'
                ];
            case "PATCH":
            case "PUT":
                return [
                    'name' => [
                        'nullable',
                        Rule::unique('as_admins')->ignore($id)
                    ],
                    'phone' => [
                        'nullable',
                        'cn_phone',
                        Rule::unique('as_admins')->ignore($id)
                    ],
                    'email' => [
                        'nullable',
                        'email',
                        Rule::unique('as_admins')->ignore($id)
                    ],
                    'password' => 'nullable|between:6,20',
                    'state' => 'nullable|in:0,1',
                    'roles' => 'nullable|array',
                    'roles.*' => 'nullable|exists:as_roles,id'
                ];
            default :
                return [];
        }
    }

    public function messages()
    {
        return [
            'name.required' => '昵称不能为空',
            'name.unique' => '昵称已存在',
            'phone.required' => '手机号不能为空',
            'phone.unique' => '手机号已存在',
            'phone.regex' => '手机号格式错误',
            'email.regex' => '邮箱格式错误',
            'email.unique' => '邮箱已存在',
            'password.required' => '密码不能为空',
            'password.between' => '密码长度6-20位',
            'state.required' => '用户状态不能为空',
            'state.in' => '用户状态格式错误',
            'type.required' => '用户类型不能为空',
            'type.in' => '用户类型格式错误',
            'roles.array' => '角色格式错误',
            'roles.*.exists' => '角色不存在',
        ];
    }
}
