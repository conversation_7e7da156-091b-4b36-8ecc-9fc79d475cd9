<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;
use Illuminate\Validation\Rule;

class AuthorityRequest extends FormRequest
{
    public function rules()
    {
        $id = $this->segment(3);

        switch ($this->method()) {
            case "POST":
                return [
                    'name' => 'required|unique:as_authorities',
                    'alias' => 'required|unique:as_authorities',
                    'type' => 'nullable|in:0,1,2',
                    'show' => 'nullable|in:0,1'
                ];
            case "PATCH":
            case "PUT":
                return [
                    'name' => [
                        'nullable',
                        Rule::unique('as_authorities')->ignore($id)
                    ],
                    'alias' => [
                        'nullable',
                        Rule::unique('as_authorities')->ignore($id)
                    ],
                    'type' => 'nullable|in:0,1,2',
                    'show' => 'nullable|in:0,1'
                ];
            default :
                return [];
        }
    }

    public function messages()
    {
        return [
            'name.required' => '名称不能为空',
            'name.unique' => '名称已经存在',
            'alias.required' => '权限别名不能为空',
            'alias.unique' => '权限别名已存在',
            'type.in' => '类型格式错误',
            'show.in' => '展示格式错误'
        ];
    }
}
