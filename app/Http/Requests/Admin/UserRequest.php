<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\FormRequest;

class UserRequest extends FormRequest
{
    public function rules()
    {
        switch ($this->method()) {
            case "POST":
                $data = [
                    'name' => 'required|max:10',
                    'account' => 'required',
                    'password' => 'required|string|min:6|max:20|confirmed',
                    'password_confirmation' => 'required|string|min:6|max:20',

                ];
                return $data;
            case "PATCH":
            case "PUT":
                $data =  [
                    'name' => 'nullable',
                    'password' => 'nullable|string|min:6|max:20|confirmed',
                    'password_confirmation' => 'nullable|string|min:6|max:20',
                ];
                return $data;
            default :
                return [];
        }

    }

    public function messages()
    {
        return [
            'name.required' => '用户名不能为空',
            'name.max' => '用户名长度最多10位',
            'account.required' => '账号不能为空',
            'password.required' => '密码错误',
            'password.min' => '密码长度最小6位',
            'password.string' => '密码格式错误',
            'password.max' => '密码长度最大20位',
            'password.confirmed' => '确认密码错误',
            'password_confirmation.required' => '确认密码错误',
            'password_confirmation.string' => '确认密码错误',
            'password_confirmation.min' => '确认密码长度错误',
            'password_confirmation.max' => '确认密码长度错误',
            'state.in' => '禁用状态错误'
        ];
    }
}
