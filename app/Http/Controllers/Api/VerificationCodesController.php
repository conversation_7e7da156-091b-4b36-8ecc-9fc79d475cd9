<?php

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\VerificationCodeCheck;
use App\Http\Requests\Api\VerificationCodeRequest;
use App\Jobs\SendSmsJob;
use App\Models\VerificationCode;
use Illuminate\Support\Facades\Log;

class VerificationCodesController extends Controller
{
    /**
     * 发送验证码
     *
     * @param VerificationCodeRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function send(VerificationCodeRequest $request)
    {
        $user = $request->user('api');
        $phone = $request->input('phone');
        $scene = (string) $request->input('scene');

        if ($scene == 'register' && is_email($phone)) {
            $this->dispatch(new SendSmsJob($phone, $scene));
        } else {
            if ($scene != 'set-phone') {
                $phone = $user ? $user->phone : $phone;
            }

            if (!is_phone($phone)){
                throw new ApiException('账号无效', ResponseCode::FORBIDDEN);
            }

            if (is_phone($phone)) {
                Log::info('phone：', [$phone]);
                $this->dispatch(new SendSmsJob($phone, $scene));
            }
        }

        return $this->responseSuccess(null, '发送成功');
    }

    /**
     * 验证
     *
     * @param VerificationCodeCheck $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\ApiException
     */
    public function check(VerificationCodeCheck $request)
    {
        $user = $request->user('api');
        $code  = (string) $request->input('code');
        $scene = (string) $request->input('scene');

        $phone = $user ? $user->phone : $request->input('phone');

        VerificationCode::check($phone, $scene, $code);

        return $this->responseSuccess(['ok' => true]);
    }
}
