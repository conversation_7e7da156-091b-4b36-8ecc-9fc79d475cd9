<?php

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\CouponCode;
use App\Models\Course;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CouponCodeController extends Controller
{
    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function info(Request $request)
    {
        $user = $request->user('api');
        $code = $request->input('code');
        $product_type = $request->input('product_type');
        $product_id = $request->input('product_id', 0);
        $couponCode = CouponCode::query()->where('code', $code)->first();
        if (!$couponCode) {
            throw new ApiException('无效优惠码', ResponseCode::NOT_FOUND);
        }

        if ($couponCode->used) {
            throw new ApiException('优惠码已失效', ResponseCode::FORBIDDEN);
        }

        if ($couponCode->end_time && $couponCode->end_time <= now()) {
            throw new ApiException('优惠码已失效', ResponseCode::FORBIDDEN);
        }

        if ($couponCode->user_id && $couponCode->user_id != $user->id) {
            throw new ApiException('暂无权限使用此优惠码', ResponseCode::FORBIDDEN);
        }

        if ($couponCode->type !=0 && $product_type!=$couponCode->type) {
            throw new ApiException('优惠码类型不匹配，无法使用', ResponseCode::FORBIDDEN);
        }

        if ($couponCode->target_id && $product_id != $couponCode->target_id) {
            $name = Course::query()->where('id', $couponCode->target_id)->value('name');
            throw new ApiException('优惠码仅限指定课程「'.$name.'」使用', ResponseCode::FORBIDDEN);
        }

        return $this->responseSuccess($couponCode->price);
    }
}
