<?php

namespace App\Http\Controllers\Api;

use App\Constants\Common;
use App\Constants\ResponseCode;
use App\Events\SetEmail;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SetPasswordRequest;
use App\Http\Requests\Api\UserUpdate;
use App\Models\CommentReply;
use App\Models\Order;
use App\Models\SecurityAnswer;
use App\Models\User;
use App\Models\UserRoleCate;
use App\Models\VerificationCode;
use App\Models\Wallet;
use App\Models\WalletLog;
use App\Services\Oss;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class OssController extends Controller
{
    /**
     * 获取oss配置
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getKey(Request $request)
    {
        $oss = Oss::getInstance()->getTempAccess();
        $return_data = [
            'oss' => $oss,
        ];

        return $this->responseSuccess($return_data, '请求成功');
    }


}
