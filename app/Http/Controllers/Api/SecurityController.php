<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\SecurityQuestion;

class SecurityController extends Controller
{
    /**
     * 问题列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function info()
    {
        $questions = SecurityQuestion::query()
            ->where('hide', 0)
            ->limit(3)
            ->get();

        return $this->responseSuccess($questions);
    }
}
