<?php

namespace App\Http\Controllers\Api;

use App\Constants\Common;
use App\Constants\ResponseCode;
use App\Events\SetEmail;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SetPasswordRequest;
use App\Http\Requests\Api\UserUpdate;
use App\Models\CommentReply;
use App\Models\CourseCategory;
use App\Models\GoodsCategory;
use App\Models\Order;
use App\Models\PlugCategory;
use App\Models\PlugDetail;
use App\Models\SecurityAnswer;
use App\Models\TagList;
use App\Models\User;
use App\Models\UserFile;
use App\Models\UserGoods;
use App\Models\UserPlug;
use App\Models\UserRoleCate;
use App\Models\UserUploadConfig;
use App\Models\VerificationCode;
use App\Models\Wallet;
use App\Models\WalletLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class UserController extends Controller
{
    public function getUserPutConfig( Request $request)
    {
        $data = UserUploadConfig::query() -> pluck('value', 'key');
        return $this->responseSuccess($data);
    }

    public function delUserPut($type, Request $request)
    {
        $user = $request->user('api');
        //课件
        if($type == 'course'){
            $model = UserFile::query();
        } else if($type == 'plug'){ //插件
            $model = UserPlug::query();
        } else if($type == 'goods'){ //周边
            $model = UserGoods::query();
        }
        $res = $model -> where('id', $request -> id) -> where('user_id', $user['id']) -> delete();
        return $this->responseSuccess($res);
    }
    public function getUserPut($type, Request $request)
    {
        $user = $request->user('api');
        //课件
        if($type == 'course'){
            $model = UserFile::query() ;
        } else if($type == 'plug'){ //插件
            $model = UserPlug::query();
        } else if($type == 'goods'){ //周边
            $model = UserGoods::query();
        }
        $data = $model
            -> with(['tagList'])
            -> when(!is_null($request -> status)&&($request -> status||$request -> status==0), function ($q) use ($request){
//                dd($request -> status);
                return $q -> where('status', $request -> status);
            })
            -> when($request -> name, function ($q) use ($request){
                return $q -> where('name', $request -> name);
            })
            -> when($request -> isCheck, function ($q) use ($request){
                if($request -> isCheck==1){
                    return $q -> where(function ($q1)use ( $request){
                        return $q1 -> where('is_check', 1) -> orWhereNull('is_check');
                    });
                } else {
                    return $q -> where('is_check', $request -> isCheck);
                }
            })
            -> when($request -> cateId, function ($q)use ($request){
                return $q -> where('cate_id', $request -> cateId  );
            })

            -> when($user, function ($q)use ($request, $user){
                return $q -> where('user_id', $user -> id  );
            })
            -> when($request -> sort, function ()use ($request){

            })
            -> orderBy('id', 'desc')
            -> paginate($request->limit?:10);

        foreach ($data -> items() as $k => $v){
            $tags = [];
            if($v -> tagList){
                foreach ($v -> tagList as $k1 => $v1){
                    $tags[] = $v1 -> tag;
                }
            }
            $v['tag'] = $tags;
        }
        return $this->responseSuccess($data);
    }



    public function saveReloadPlug($request , $user, $info)
    {
        if($request -> detailList)$info -> detail = json_encode($request -> detailList, JSON_UNESCAPED_UNICODE);  //文件
        $info -> save();
        if($info['vid']){
            $detail = json_decode($info['detail'], true);
            PlugDetail::query() -> where('plug_id',$info['vid'] ) -> delete();
            $arr = [];
            foreach ($detail as $k => $v){
                $plugDetail = new PlugDetail();
                $plugDetail ->plug_id = $info['vid'];
                $plugDetail ->version_name = $v['version_name']??'';
                $plugDetail ->version_code = $v['version_code']??'';
                $plugDetail ->platform = $v['platform']??'';
                $plugDetail ->description = $v['description']??'';
                $plugDetail ->type = $v['type']??'';
                $plugDetail ->link = isset($v['fileList'])&&$v['fileList']&&(count($v['fileList'])>0)?$v['fileList'][0]['name']:'';
                $plugDetail ->user_id = $user['id']??'';
                $plugDetail -> save();
            }
        }
        return $info;
    }

    public function userput($type, Request $request)
    {
        $user = $request->user('api');
        $id = $request -> id;
        $model = null;
        if($request -> reload){
            $model = UserPlug::query() -> find($id);
            $info = $this->saveReloadPlug($request , $user, $model);

            return $this->responseSuccess([$model], '更新成功');
        }
        if($id){
            if($type == 'course'){
                $model = UserFile::query() -> find($id);
            } else if($type == 'plug'){ //插件
                $model = UserPlug::query() -> find($id);
            } else if($type == 'goods'){ //周边
                $model = UserGoods::query() -> find($id);
            }
        }
        //课件
        if($type == 'course'){
            $this->saveCourse($request , $user, $model, $type);
        } else if($type == 'plug'){ //插件
            $this->savePlug($request , $user, $model, $type);
        } else if($type == 'goods'){ //周边
            $this->saveGoods($request , $user, $model, $type);
        }
        return $this->responseSuccess([$type, $user]);
    }

    public function saveCourse($request, $user, $info=null, $type=null)
    {
        //课程保存
        if(is_null($info)){
            $info = new UserFile();
        }
        if($request -> name)$info -> name = $request -> name;       //课程
        if($request -> images)$info -> images = $request -> images;   //封面图
        if($request -> intro)$info -> intro = $request -> intro;     //课程介绍
        if($request -> price)$info -> price = $request -> price;     //光子
        $info -> sort = 0;
        if($request -> author)$info -> author = $request -> author;   //作者
        $info -> admin_id = 0;
        if($request -> cate_id)$info -> cate_id = $request -> cate_id; //分类
        if($request -> external_link)$info -> external_link = $request->type=='转载'?$request -> external_link:'';   //转载链接
        $info -> user_id = $user->id;                                       //用户链接
        $info -> is_check = 1;                                       //审核状态
        if($request -> detailList)$info -> detail = json_encode($request -> detailList, JSON_UNESCAPED_UNICODE);  //文件
        if($request -> web_link)$info -> web_link = $request->web_link;

        $info -> status = $request -> status;   //0 草稿 2发布
        $info -> save();
        $this -> saveTag($info, $request ->tag, $type);
    }

    public function saveTag($info, $tag, $type)
    {
        if(empty($tag))return;
        TagList::query()
        -> where('type', $type)
        -> where('uid', $info['id']) -> delete();
        foreach ($tag as $k => $v){
            $taglist = new TagList();
            $taglist -> tag = $v;
            $taglist -> type = $type;
            $taglist -> uid = $info['id'];
            $taglist -> aid = 0;
            $taglist -> save();
        }
    }

    public function savePlug($request, $user, $info=null, $type=null)
    {
        //课程保存
        if(is_null($info)){
            $info = new UserPlug();
        }
        if($request -> name)$info -> name = $request -> name;       //课程
        if($request -> images)$info -> images = $request -> images;   //封面图
        if($request -> intro)$info -> intro = $request -> intro;     //课程介绍
        if($request -> price)$info -> price = $request -> price;     //光子
        $info -> sort = 0;
        if($request -> author)$info -> author = $request -> author;   //作者
        $info -> admin_id = 0;
        if($request -> cate_id)$info -> cate_id = $request -> cate_id; //分类
        if($request -> external_link)$info -> external_link = $request->type=='转载'?$request -> external_link:'';   //转载链接
        $info -> user_id = $user->id;                                      //用户链接
        $info -> is_check = 1;                                    //用户链接
        if($request -> detailList)$info -> detail = json_encode($request -> detailList, JSON_UNESCAPED_UNICODE);  //文件
        if($request -> intros)$info -> intros = json_encode($request -> intros, JSON_UNESCAPED_UNICODE);  //文件
        if($request -> web_link)$info -> web_link = $request->web_link;

        $info -> status = $request -> status;   //0 草稿 2发布
        $info -> save();
        $this -> saveTag($info, $request ->tag, $type);
    }

    public function saveGoods($request, $user, $info=null, $type=null)
    {
        //课程保存
        if(is_null($info)){
            $info = new UserGoods();
        }
        $images = $request -> images;
        $jsonimg = '';
        $cover = '';
        if(count($images)>0){
            $cover = $images[0]['name'];
            $jsonimg = json_encode(array_column($images, 'name'), JSON_UNESCAPED_UNICODE);
        }
        $material = '';
        if($request -> fileList && count($request -> fileList)>0){
            $material = $request -> fileList[0]['name'];
        }
        $info -> name = $request -> name?:'';       //课程
        $info -> cover = $cover?:'';   //封面图
        $info -> images = $jsonimg?:'';     //课程介绍
        $info -> price = $request -> price?:0;     //光子
        $info -> cate_id = $request -> cate_id?:0; //分类
        $info -> market_price = 0; //分类
        $info -> desc = $request -> desc?:'';
        $info -> material = $material;
        $info -> sort = 0;
        $info -> admin_id = 0;
        $info -> user_id = $user->id;                                    //用户链接
        $info -> is_check = 1;                                      //用户链接
        $info -> status = $request -> status;   //0 草稿 2发布
        $info -> save();
        $this -> saveTag($info, $request ->tag, $type);
    }

    /**
     * @param UserUpdate $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getUserCate(Request $request)
    {
        $user = $request->user('api');
        $type = $request -> catetype;
        $cateIds = UserRoleCate::query()
            -> where('user_id', $user->id)
            -> where('type', $type)
            -> pluck('cate_id') -> toArray();
        if($type=='plug'){
            $cates = PlugCategory::query() -> whereIn('id', $cateIds) ->orderBy('parent_id', 'asc') -> get();
        } else if($type=='course'){
            $cates = CourseCategory::query() -> whereIn('id', $cateIds) ->orderBy('parent_id', 'asc') -> get();
        } else if($type=='goods'){
            $cates = GoodsCategory::query() -> whereIn('id', $cateIds) ->orderBy('parent_id', 'asc') -> get();
        }
//        $cates['ids'] = $cateIds;
        return $this->responseSuccess($cates);
    }


    /**
     * 用户信息
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function info(Request $request)
    {
        $user = $request->user('api');
        $wallet = Wallet::init($user->id);
        $user->integral = $wallet->balance;

        $wait_delivery = Order::query()
            ->where('status', Order::StatusMappings['paid'])
            ->where('logistic_status', 0)
            ->where('product_type', 3)
            ->where('user_id', $user->id)
            ->count();
        $user->wait_delivery = $wait_delivery;

        $wait_read = CommentReply::query()
            ->whereHas('comment', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->where('is_read', 0)
            ->count();

        $user->wait_read = $wait_read;
        $user->plug = UserRoleCate::query() ->where('user_id', $user['id'])->where('type', "plug") -> exists();
        $user->course = UserRoleCate::query() ->where('user_id', $user['id'])->where('type', "course") -> exists();
        $user->goods = UserRoleCate::query() ->where('user_id', $user['id'])->where('type', "goods") -> exists();

        return $this->responseSuccess($user);
    }

    /**
     * 基础信息修改
     *
     * @param UserUpdate $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function update(UserUpdate $request)
    {
        $user = $request->user('api');
        $type = $request->input('type');
        $value = $request->input('value');
        logger()->info($type);

        if (!in_array($type, ['name', 'avatar', 'sign', 'intro', 'sex', 'company', 'occupation', 'wechat', 'qq'])) {
            throw new ApiException('类型错误', ResponseCode::PARAM_ERR);
        }

        $user->{$type} = $value;
        if (!$user->save()) {
            throw new ApiException('修改失败', ResponseCode::SERVER_ERR);
        }

        return $this->responseSuccess('修改成功');
    }

    /**
     * 实名
     */
    public function certification()
    {

    }

    /**
     * 密码修改
     *
     * @param SetPasswordRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function setPassword(SetPasswordRequest $request)
    {
        $oldPassword = $request->input('old_password');
        $password = $request->input('password');

        $user = $request->user('api');

        if (!Hash::check($oldPassword, $user->password)) {
            throw new ApiException('旧密码验证失败', ResponseCode::FORBIDDEN);
        }

        $user->password = $password;
        $user->api_token = '';
        if (!$user->save()) {
            throw new ApiException('修改失败，请稍后再试', ResponseCode::SERVER_ERR);
        }

        return $this->responseSuccess('修改成功');
    }

    /**
     * 安全问题
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function security(Request $request)
    {
        $user = $request->user('api');
        $data = $request->input('data');

        // todo ... 设置安全问题密码验证
//        if (!Hash::check($request->input('password'), $user->password)) {
//            throw new ApiException('密码验证失败', ResponseCode::FORBIDDEN);
//        }

        if (SecurityAnswer::query()->where('user_id', $user->id)->exists()) {
            throw new ApiException('安全问题已设置', ResponseCode::FORBIDDEN);
        }

        $insertData = [];
        foreach ($data as $key=>$value) {
            $insertData[] = [
                'question_id' => $key,
                'content' => $value,
                'verify' => md5($value),
                'user_id' => $user->id,
                'created_at' => now(),
                'updated_at' => now(),
            ];
        }

        SecurityAnswer::query()->insert($insertData);

        return $this->responseSuccess('设置成功');
    }

    /**
     * 设置邮箱
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function setEmail(Request $request)
    {
        $user = $request->user('api');
        $data = $request->only(['email', 'password']);

        if (!is_email($data['email'])) {
            throw new ApiException('邮箱格式错误', ResponseCode::FORBIDDEN);
        }

        if (!Hash::check($data['password'], $user->password)) {
            throw new ApiException('密码验证失败', ResponseCode::FORBIDDEN);
        }

        event(new SetEmail($user, $data['email']));
        return $this->responseSuccess(null,'验证链接已发到相应邮箱，请前往验证');
    }

    /**
     * 设置手机号
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function setPhone(Request $request)
    {
        $user = $request->user('api');
        $data = $request->only(['phone', 'password', 'code']);

        if (!is_phone($data['phone'])) {
            throw new ApiException('手机号格式错误', ResponseCode::FORBIDDEN);
        }

        $verCode = VerificationCode::check($data['phone'], Common::SET_PHONE, $data['code']);

        if (User::query()
            ->where('phone', $data['phone'])
            ->where('id', '!=', $user->id)
            ->first()) {
            throw new ApiException('手机号已存在', ResponseCode::FORBIDDEN);
        }

        if (!Hash::check($data['password'], $user->password)) {
            throw new ApiException('密码验证失败', ResponseCode::FORBIDDEN);
        }
        $verCode->used();

        $user->phone = $data['phone'];
        if (!$user->save()) {
            throw new ApiException('设置失败', ResponseCode::SERVER_ERR);
        }

        return $this->responseSuccess('设置成功');
    }

    /**
     * 流水记录
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function walletlogs(Request $request)
    {
        $user = $request->user('api');
        $limit = $request->input('limit', 20);
        $offset = $request->input('offset', 0);

        $lists = WalletLog::query()
            ->where('user_id', $user->id)
            ->where('currency', WalletLog::CurrencyMappings['integral'])
            ->where('status', WalletLog::StatusMappings['success'])
            ->where('type', '!=', WalletLog::TypeMappings['login_give'])
            ->orderByDesc('created_at')
            ->offset($offset * $limit)
            ->limit($limit)
            ->get();

        $total = WalletLog::query()
            ->where('user_id', $user->id)
            ->where('currency', WalletLog::CurrencyMappings['integral'])
            ->where('status', WalletLog::StatusMappings['success'])
            ->count();

        // 用户资产
        $wallet = Wallet::init($user->id);
        $income = $wallet->income;
        $balance = $wallet->balance;
        $expend = $wallet->expend;

        return $this->responseSuccess(compact('lists', 'total', 'income', 'balance', 'expend'));
    }
}
