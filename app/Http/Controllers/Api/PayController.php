<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Services\PaymentService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PayController extends Controller
{
    /**
     * 支付宝异步通知处理
     * <AUTHOR> 2021-10-25T14:29:38+0800
     *
     * @param  Request $request
     * @return mixed
     */
    public function alipayNotify(Request $request)
    {
        try {
            if (PaymentService::getInstance()->handleRechargeAlipayNotify($request->all())) {
                echo 'success';
            } else {
                echo 'fail';
            }
        } catch (\Exception $e) {
            echo 'fail';
        }
    }

    /**
     * @param PaymentService $payment
     * @return mixed
     */
    public function wechatNotify()
    {
        try {
            return PaymentService::getInstance()->handleRechargeWechatNotify();
        } catch (\Exception $e) {
        }
    }
}
