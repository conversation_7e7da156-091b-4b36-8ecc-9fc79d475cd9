<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Config;
use App\Models\Link;

class FooterController extends Controller
{
    /**
     * 底部信息
     * @return \Illuminate\Http\JsonResponse
     */
    public function info()
    {
        $config = Config::query()->get();
        $data = [];
        // 友情链接
        $fri = $config->where('key', Config::KeyMappings['friend'])->first();
        foreach ($fri->value as $item) {
            $data['friends'][] = [
                'name' => $item['name'],
                'link' => $item['link']
            ];
        }

        // 合作伙伴
        $coo = $config->where('key', Config::KeyMappings['cooperation'])->first();
        foreach ($coo->value as $item) {
            $data['cooperations'][] = [
                'name' => $item['name'],
                'link' => $item['link']
            ];
        }

        // 联系方式
        $con = $config->where('key', Config::KeyMappings['contact'])->first();
        $mapping = [
            'qq' => 'QQ',
            'email' => 'Email',
            'phone' => 'Phone',
            'wechat' => 'WeChat',
        ];
        foreach ($con->value as $key => $item) {
            if ($item) {
                $data['contacts'][] = [
                    'name' => $mapping[$key],
                    'value' => $item
                ];
            }

        }

        return $this->responseSuccess($data);
    }
}
