<?php

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use GuzzleHttp\Client;
use App\Models\Wallet;
use App\Models\WalletLog;
use App\Models\AiGalleryCategory;
use App\Models\AiGallery;
use App\Models\AiRenderCategory;
use App\Models\AiQuestion;
use App\Models\AiNews;
use App\Models\AiPlug;
use App\Models\AiVip;
use App\Models\AiVipSetting;
use App\Models\AiGalleryLike;
use App\Models\Config;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;

class AiController extends Controller
{
    private $client;
    private $baseURI;

    public function __construct()
    {
        // $this->baseURI = env('AI_IP', 'http://**************:81');
        $this->baseURI = Config::getValueByKey('ai_server');
        // $this->baseURI = env('AI_IP', 'http://**************:81');
        // $this->baseURI = 'http://*************:81';
        $this->client = new Client();
    }

    /**
     * 作品集
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getGallery(Request $request)
    {
        $items = AiGalleryCategory::query()
            ->where('parent_id', 0)
            ->selectRaw('id,name,parent_id')
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->get()->toArray();

        array_unshift($items, [
            'id' => 0,
            'name' => '全部',
            'parent_id' => -1,
            'list' => [],
        ]);

        foreach ($items as &$item) {
            if($item['parent_id'] == -1) {
                $cateIds = AiGalleryCategory::query()->where('parent_id', $item['id'])->pluck('id')->toArray();
            } else {
                $cateIds = [$item['id']];
            }
            // var_dump($cateIds);
            $item['list'] = AiGallery::query()
                ->when( $item['id'] != 0, function ($query) use ($cateIds) {
                    $query->whereIn('cate_id', $cateIds);
                })
                ->where('status', AiGallery::StatusMapping['yes'])
                ->orderByDesc('sort')
                ->orderByDesc('id')
                ->limit(8)
                ->notDelete()
                ->get()
                ->map(function ($value) {
                    $value->cover = handle_url($value->cover);
                    if (is_dev()) {
                        $value->cover = 'https://www.helloimg.com/images/2022/04/05/RmAfdo.jpg';
                    }

                    return $value;
                })
                ->toArray();
            // var_dump($item);
            if ($item['id'] != 0 && empty($item['list'])) {
                unset($item);
            }
        }

        return $this->responseSuccess($items);
    }

    public function getGalleryList(Request $request)
    {
        $user = $request->user('api');
        $limit = $request->input('limit', 10);
        $offset = $request->input('offset', 0);
        $cate = $request->input('cate_id', ''); 

        $cates = AiGalleryCategory::query()
            ->where('parent_id', 0)
            ->selectRaw('id,name,parent_id')
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->get()->toArray();

        array_unshift($cates, [
            'id' => 0,
            'name' => '全部',
            'parent_id' => -1,
            'list' => [],
        ]);

        if($user) {
            // 获取已收藏的图片 ID
            $favoritedPictureIds = AiGalleryLike::query()->where('user_id', $user->id)->pluck('gid')->toArray();
        } else {
            $favoritedPictureIds = [];
        }
        if($favoritedPictureIds) {
            $list = AiGallery::query()
            ->when($cate!='', function ($query) use ($cate) {
                return $query->where('cate_id', $cate);
            })
            ->offset($offset * $limit)
            ->limit($limit)
            ->orderByRaw(
                DB::raw("FIELD(id, " . implode(',', $favoritedPictureIds) . ") DESC"),
            )
            ->orderBy('like_count', 'DESC')
            ->get();
            
        } else {
            $list = AiGallery::query()
            ->when($cate!='', function ($query) use ($cate) {
                return $query->where('cate_id', $cate);
            })
            ->offset($offset * $limit)
            ->limit($limit)
            ->orderBy('like_count', 'DESC')
            ->get();
        }
        
        $total = AiGallery::query()
            ->when($cate!='', function ($query) use ($cate) {
                return $query->where('cate_id', $cate);
            })
            ->count();

        if ($list) {
            
            foreach ($list as $key=>&$item) {
                $item->load('author');
                $item->author->avatar = $item->author->avatar ? handle_url($item->author->avatar) : asset('upload/images/xg.jpg');
                $item->like = $user ? AiGalleryLike::isLike($user->id, $item->id) : false;
                $item->like_count = $item->like_count + AiGalleryLike::likeCount($item->id);
                $item->cover = $item->cover ? handle_url($item->cover) : asset('upload/images/xg.jpg');
                // $item->headpic = $item->headpic ? handle_url($item->headpic) : asset('upload/images/xg.jpg');
                if (is_dev()) {
                    $item->cover = asset('upload/images/xg.jpg');
                }
                $images = $item->images;
                foreach ($images as $key=>$image){
                    $images[$key] = handle_url($image);
                    if (is_dev()) {
                        $images[$key] = asset('upload/images/xg.jpg');
                    }
                }
                $item->images = $images;
            }
        }

        return $this->responseSuccess(compact('cates', 'list', 'total'));
    }


    public function likeGallery(Request $request)
    {
        $id = $request->input('id');
        $user = $request->user('api');

        try{
            AiGalleryLike::like($user->id, $id);
        } catch (\Exception $exception) {
            Log::error('galleryLike', [ 'AI', $exception->getMessage()]);
        }

        return $this->responseSuccess(null, '操作成功');
    }

    public function getRenderStyle(Request $request)
    {
        $items = AiRenderCategory::query()
            ->where('parent_id', 0)
            ->selectRaw('id,name,parent_id')
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->get()->toArray();
        foreach ($items as &$item) {
            $item['children'] = AiRenderCategory::query()
                ->where('parent_id', $item['id'])
                ->orderByDesc('sort')
                ->orderByDesc('id')
                // ->limit(8)
                ->notDelete()
                ->get()
                ->map(function ($value) {
                    $value->cover = handle_url($value->cover);
                    if (is_dev()) {
                        $value->cover = 'https://www.helloimg.com/images/2022/04/05/RmAfdo.jpg';
                    }

                    return $value;
                })
                ->toArray();
            // var_dump($item);
            if ($item['id'] != 0 && empty($item['list'])) {
                unset($item);
            }
        }
        return $this->responseSuccess($items);
    }

    /**
     * 生成图片
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function generateImg(Request $request)
    {
        // $result = ['img' => 'http://xiguang2.oss-cn-beijing.aliyuncs.com//uploads/ComfyUI_00020_.png', 'tasks' => 0];
        // return $this->responseSuccess($result);
        $user = $request->user('api');

        $style = $request->input('style');
        $prompt = $request->input('promt');
        $fprompt = $request->input('fpromt');
        $image1 = $request->input('image1');
        $image2 = $request->input('image2');
        $image3 = $request->input('image3');
        $type1 = $request->input('type1');
        $type2 = $request->input('type2');
        $type3 = $request->input('type3');
        $highres = $request->input('highres');
        $weight1 = $request->input('weight1');
        $weight2 = $request->input('weight2');
        $weight3 = $request->input('weight3');
        $quality = $request->input('quality', 1);
        $size = $request->input('size');
        $result = ['img' => '', 'tasks' => 50];

        $vip = AiVip::isVip($user->id);
        $wallet = Wallet::init($user->id);
        $deduct_integral = 0.01 * $quality;
        $cacheKey = 'genFree_'.$user->id;
        $cost = true;
        if(!$vip) {
            if (!Cache::has($cacheKey)) {
                $cost = false;
            } else {
                if ($wallet->balance < $deduct_integral) {
                    throw new ApiException('光子不足，请充值', ResponseCode::FORBIDDEN);
                }
            }
        } else {
            $cost = false;
        }

        try {
            // $res = $this->client->request('GET', $this->baseURI.'/getrenderedimage', ['query' => [
            //     'id' => $user->id,
            //     'style' => $style,
            //     'prompt' => $prompt,
            //     'fprompt' => $fprompt,
            //     'size' => $size,
            //     'image1' => $image1 ? $this->imageToBase64($image1) : '',
            //     'image2' => $image2 ? $this->imageToBase64($image2) : '',
            //     'image3' => $image3 ? $this->imageToBase64($image3) : '',
            //     'type1' => $type1,
            //     'type2' => $type2,
            //     'type3' => $type3,
            //     'highres' => $highres,
            //     'weight1' => $weight1,
            //     'weight2' => $weight2,
            //     'weight3' => $weight3,
            // ]]);
            $res = $this->client->request('POST', $this->baseURI.'/getrenderedimage', [ 'json' => [
                'id' => $user->id,
                'style' => $style,
                'prompt' => $prompt,
                'fprompt' => $fprompt,
                'size' => $size,
                'image1' => $image1 ? $this->getUrl($image1,$request) : '',
                'image2' => $image2 ? $this->getUrl($image2,$request) : '',
                'image3' => $image3 ? $this->getUrl($image3,$request) : '',
                'type1' => $type1,
                'type2' => $type2,
                'type3' => $type3,
                'highres' => $highres,
                'weight1' => $weight1,
                'weight2' => $weight2,
                'weight3' => $weight3,
                'quality' => $quality
            ]]);
            
            $content = json_decode($res->getBody()->getContents(), true);
            // var_dump($content);
            Log::info('generateImgRes', [ 'AI', json_encode($content)]);
            if ($content && $content['image']) {
                $result = ['img' => $content['image'], 'tasks' => $content['tasks']];

                if($cost) {
                    //扣款
                    // $wallet = Wallet::init($user->id);
                    if ($wallet->balance >= $deduct_integral) {
                        $wallet->balance -= $deduct_integral;
                        $wallet->expend += $deduct_integral;
                        $wallet->save();

                        $type = WalletLog::TypeMappings['image'];

                        $walletLog = new WalletLog();
                        $walletLog->title = 'AI智能渲染';
                        $walletLog->target_type = 'user';
                        $walletLog->target_id = $user->id;
                        $walletLog->amount = $deduct_integral;
                        $walletLog->user_id = $user->id;
                        $walletLog->currency = 'integral';
                        $walletLog->action = WalletLog::ActionMappings['expend'];
                        $walletLog->status = WalletLog::StatusMappings['success'];
                        $walletLog->type = $type;
                        $walletLog->extend = ['balance'=>$wallet->balance];
                        $walletLog->save();

                    } else {
                        //throw new ApiException('光子不足，请充值', ResponseCode::FORBIDDEN);
                        return $this->responseError('光子不足，请充值', ResponseCode::FORBIDDEN);
                    }
                    
                } else {
                    if(!$vip) {
                        if (!Cache::has($cacheKey)) {
                            $timestamps = strtotime(date('Y-m-d') . ' +1 day') - time();
                            Cache::put($cacheKey, 1, $timestamps);
                        } 
                    }
                }
                
            } else {
                if ($content && $content['tasks']) {
                     $result = ['img' => '', 'tasks' => $content['tasks']];
                }
            }
            
        } catch (\Exception $exception) {
            Log::error('generateImg', [ 'AI', $exception->getMessage().' '.$exception->getLine()]);
        }
        
        return $this->responseSuccess($result);
    }

    public function getUrl($path, $request) {
        //$request = Request::instance();
        $scheme = $request->getScheme();
        
        return $scheme.'://'.$_SERVER['HTTP_HOST'] . '/' . $path;
    }

    public function getBalance(Request $request) 
    {
        $user = $request->user('api');
        $wallet = Wallet::init($user->id);
        return $this->responseSuccess($wallet->balance);
    }

    public function getVip(Request $request)
    {
        $user = $request->user('api');
        $vip = AiVip::isVip($user->id);
        if ($vip) {
            $res = [
                'is_vip' => $vip,
                'free_img' => 0,
                'free_chat' => 0
            ];
        } else {
            $res = [
                'is_vip' => $vip,
                'free_img' => 3,
                'free_chat' => 10
            ];

            $imgKey = 'genFree_'.$user->id;
            if (Cache::has($imgKey)) {
                $res['free_img'] = 0;
            }
            $chatKey = 'chatFree_'.$user->id;
            if (Cache::has($chatKey)) {
                $res['free_chat'] = 2 - Cache::get($chatKey);
            }
        }

        return $this->responseSuccess($res);
    }

    public function buy(Request $request)
    {
        $price = $request->input('price', 0);
        $user = $request->user('api');

        $setting = AiVipSetting::query()->where('id', 1)->first()->toArray();
        $arrMap = [
            $setting['vip_price1'] => 30,
            $setting['vip_price2'] => 90,
            $setting['vip_price3'] => 360,
            $setting['vip_price4'] => 0
        ];
        $arrTittleMap = [
            $setting['vip_price1'] => '月度VIP',
            $setting['vip_price2'] => '季度VIP',
            $setting['vip_price3'] => '年度VIP',
            $setting['vip_price4'] => '永久VIP'
        ];

        if (!$price || !isset($arrMap[$price])) {
            throw new ApiException('参数无效', ResponseCode::FORBIDDEN);
        }

        $wallet = Wallet::init($user->id);

        $vip = AiVip::query()->where('user_id', $user->id)->first();
        if ($vip) {
            if ($vip->permanent) {//永久
                return $this->responseSuccess(null, '你已经是永久VIP了');
            } else { 
                //判读余额
                if($price > $wallet->balance) {
                    throw new ApiException('光子不足，请充值', ResponseCode::FORBIDDEN);
                }
                //有效期
                if(!$arrMap[$price]) {
                    $vip->permanent = 1;
                    $vip->endtime = 0;
                } else {
                    $vip->permanent = 0;
                    if($vip->endtime > time()) {
                        $endtime = $vip->endtime + $arrMap[$price] * 24 * 3600;
                    } else {
                        $endtime = strtotime(date('Y-m-d')) + $arrMap[$price] * 24 * 3600;
                    }
                    $vip->endtime = $endtime;
                }
                
                if ($vip->save()) {
                    //扣款
                    $wallet->balance = bcsub($wallet->balance, $price, 2);

                    $wallet->expend = bcadd($wallet->expend, $price, 2);
                    $wallet->save();

                    $type = WalletLog::TypeMappings['ai_vip'];

                    $walletLog = new WalletLog();
                    $walletLog->title = $arrTittleMap[$price];
                    $walletLog->target_type = 'user';
                    $walletLog->target_id = $user->id;
                    $walletLog->amount = $price;
                    $walletLog->user_id = $user->id;
                    $walletLog->currency = 'integral';
                    $walletLog->action = WalletLog::ActionMappings['expend'];
                    $walletLog->status = WalletLog::StatusMappings['success'];
                    $walletLog->type = $type;
                    $walletLog->extend = ['balance'=>$wallet->balance];
                    $walletLog->save();
                } else {
                    return $this->responseError('开通失败', ResponseCode::FORBIDDEN);
                }
            }
        } else {
            //判读余额
            if($price > $wallet->balance) {
                throw new ApiException('光子不足，请充值', ResponseCode::FORBIDDEN);
            }
            $vipObj = new AiVip();
            //是否永久
            if(!$arrMap[$price]) {
                $vipObj->permanent = 1;
                $vipObj->endtime = 0;
            } else {
                $vipObj->permanent = 0;
                $vipObj->endtime = strtotime(date('Y-m-d')) + $arrMap[$price] * 24 * 3600;
            }
            $vipObj->user_id = $user->id;
            if ($vipObj->save()) {
                //扣款
                $wallet->balance = bcsub($wallet->balance, $price, 2);

                $wallet->expend = bcadd($wallet->expend, $price, 2);

                $wallet->save();

                $type = WalletLog::TypeMappings['ai_vip'];

                $walletLog = new WalletLog();
                $walletLog->title = $arrTittleMap[$price];
                $walletLog->target_type = 'user';
                $walletLog->target_id = $user->id;
                $walletLog->amount = $price;
                $walletLog->user_id = $user->id;
                $walletLog->currency = 'integral';
                $walletLog->action = WalletLog::ActionMappings['expend'];
                $walletLog->status = WalletLog::StatusMappings['success'];
                $walletLog->type = $type;
                $walletLog->extend = ['balance'=>$wallet->balance];
                $walletLog->save();
            } else {
                return $this->responseError('开通失败', ResponseCode::FORBIDDEN);
            }
            
        }

        $res = AiVip::query()->where('user_id', $user->id)->first();

        return $this->responseSuccess($res, '开通成功');
    }

    public function getHotQuestion(Request $request) 
    {
        $limit = $request->input('limit', 9);
        $offset = $request->input('offset', 0);

        $list = AiQuestion::query()->where('is_hide', 0)
            ->offset($offset * $limit)
            ->limit($limit)
            ->orderBy('sort', 'DESC')
            ->get();
        
        $total = AiQuestion::query()
            ->where('is_hide', 0)
            ->count();

        return $this->responseSuccess(compact('list', 'total'));
    }

    public function getNews(Request $request) 
    {
        $limit = $request->input('limit', 4);
        $offset = $request->input('offset', 0);
        $keywords = $request->input('keywords', '');
        $cate = $request->input('cate', '');

        $list = AiNews::query()->where('is_hide', 0)
            ->when($keywords!='', function ($query) use ($keywords) {
                return $query->where('title', 'like', "%$keywords%");
            })
            ->when($cate!='', function ($query) use ($cate) {
                return $query->where('cate_id', $cate);
            })
            ->offset($offset * $limit)
            ->limit($limit)
            ->orderBy('sort', 'DESC')
            ->get();
        
        $total = AiNews::query()
            ->where('is_hide', 0)
            ->when($keywords!='', function ($query) use ($keywords) {
                return $query->where('title', 'like', "%$keywords%");
            })
            ->when($cate!='', function ($query) use ($cate) {
                return $query->where('cate_id', $cate);
            })
            ->count();
        if ($list) {
            foreach ($list as $key=>&$item) {
                $item->time = Carbon::parse($item->created_at)->format('Y-m-d');
                $item->content = $limit!=3?substr_with_powerful($item->content, 30):substr_with_powerful($item->content, 150);
                $item->images = $item->images ? handle_url($item->images) : asset('upload/images/xg.jpg');
                if (is_dev()) {
                    $item->images = asset('upload/images/xg.jpg');
                }
            }
        }

        return $this->responseSuccess(compact('list', 'total'));
    }

    public function getNewsInfo($newsId)
    {
        $newsInfo = AiNews::query()->find($newsId);
        

        if($newsInfo) {
            $newsInfo->time = Carbon::parse($newsInfo->created_at)->format('Y-m-d');

            $newsInfo->cateList = AiNews::query()
                                    ->where('cate_id', $newsInfo->cate_id)
                                    ->where('id', '<>', $newsInfo->id)
                                    ->limit(4)
                                    ->orderByDesc('id')
                                    ->get();

            // 上
            $newsInfo->prev = AiNews::query()
            ->where('is_hide', 0)
            ->where('id', '<', $newsId)
            ->orderByDesc('id')
            ->first(['id', 'title']);

            $newsInfo->next = AiNews::query()
                ->where('is_hide', 0)
                ->where('id', '>', $newsId)
                ->orderBy('id')
                ->first(['id', 'title']);

        }

        return $this->responseSuccess($newsInfo);
    }

    public function imageToBase64($path) {
        if(!$path){
            return '';
        }
        $path = public_path($path);
        $type = File::mimeType($path);
        $file = File::get($path);
        if ($file) {
            $base64 = 'data:' . $type . ';base64,' . base64_encode($file);
        } else {
            $base64 = '';
        }
        return $base64;
    }

    public function getPlug(Request $request){
        $plugInfo = AiPlug::query()
            ->with([
                'detail:plug_id,version_code,version_name,type,platform,description,link,created_at,updated_at',
            ])
            ->where('id', 1)
            ->first();

        if (!$plugInfo) {
            throw new ApiException('插件不存在', ResponseCode::NOT_FOUND);
        }

        $plugInfo->images = handle_url($plugInfo->images);
        $plugInfo->link = handle_url($plugInfo->link);

        $plugInfo = $plugInfo->toArray();

        return $this->responseSuccess($plugInfo);
    }

    public function getVipSetting(Request $request){
        $setting = AiVipSetting::query()
            ->where('id', 1)
            ->first();

        if (!$setting) {
            throw new ApiException('配置有误', ResponseCode::NOT_FOUND);
        }

        $setting = $setting->toArray();

        return $this->responseSuccess($setting);
    }

    public function chat(Request $request) {
        // return $this->responseSuccess("你好啊，我是来时爱上大叔来得及卡手机丢了卡升级，立刻决定离开就是了,阿时刻觉得很愧疚啊还是大家看哈电视卡多少离开就是贷款阿兰德斯绿卡大量的阿斯利康的啊了是的；");
        $user = $request->user('api');
        $prompt = $request->input('prompt');
        $vip = AiVip::isVip($user->id);
        try {
            $res = $this->client->request('GET', $this->baseURI.'/getaianswer', ['query' => [
                'prompt' => $prompt,
                'n' => 1,
                'id' => $user->id,
            ]]);
            // $res = $this->client->request('POST', $this->baseURI.'/getaianswer', [
            //     'prompt' => $prompt,
            //     'n' => 1,
            //     'id' => $user->id,
            // ]);
            $content = json_decode($res->getBody()->getContents(), true);
            if ($content && $content['answer']) {
                $result = $content['answer'][0];

                if(!$vip) {
                    $cacheKey = 'chatFree_'.$user->id;
                    if (!Cache::has($cacheKey)) {
                        $timestamps = strtotime(date('Y-m-d') . ' +1 day') - time();
                        Cache::put($cacheKey, 1, $timestamps);
                    } else {
                        $count = Cache::get($cacheKey);
                        if ($count < 2) {
                            $timestamps = strtotime(date('Y-m-d') . ' +1 day') - time();
                            Cache::put($cacheKey, 2, $timestamps);
                        } else {
                            $deduct_integral = 0.02;

                            //扣款
                            $wallet = Wallet::init($user->id);
                            if ($wallet->balance >= $deduct_integral) {
                                $wallet->balance -= $deduct_integral;
                                $wallet->expend += $deduct_integral;
                                $wallet->save();

                                $type = WalletLog::TypeMappings['chat'];

                                $walletLog = new WalletLog();
                                $walletLog->title = 'AI问答';
                                $walletLog->target_type = 'user';
                                $walletLog->target_id = $user->id;
                                $walletLog->amount = $deduct_integral;
                                $walletLog->user_id = $user->id;
                                $walletLog->currency = 'integral';
                                $walletLog->action = WalletLog::ActionMappings['expend'];
                                $walletLog->status = WalletLog::StatusMappings['success'];
                                $walletLog->type = $type;
                                $walletLog->extend = ['balance'=>$wallet->balance];
                                $walletLog->save();

                            } else {
                                return $this->responseError('光子不足，请充值', ResponseCode::FORBIDDEN);
                                // throw new ApiException('光子不足，请充值', ResponseCode::FORBIDDEN);
                            }
                        }
                    }
                }
            } else {
                $result = '链接超时';
            }
        }catch (\Exception $exception) {
            Log::error('aiChat', [ 'AI', $exception->getMessage()]);

            throw new ApiException('服务正在维护中', ResponseCode::SERVER_ERR);
        }
        
        return $this->responseSuccess($result);

    }
}