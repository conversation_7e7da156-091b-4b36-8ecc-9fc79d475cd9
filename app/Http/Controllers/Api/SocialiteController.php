<?php

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\User;
use App\Services\IntegralService;
use App\Services\SocialiteService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Laravel\Socialite\Facades\Socialite;
use App\Models\Socialite as SocialiteModel;

class SocialiteController extends Controller
{
    /**
     * 将用户重定向到认证页面
     * @param Request $request
     * @param $service
     * @return \Symfony\Component\HttpFoundation\RedirectResponse
     */
    public function redirectToProvider(Request $request, $service)
    {
        $userId = $request->input('user_id');
        Log::info('redirectToProvider', [$userId]);
        if ($userId) {
            if ($service=='weixinweb') {
                $redirect = config('services.weixinweb.redirect');
                Config::set('services.weixinweb.redirect',$redirect.'?_id='.$userId);
            }elseif ($service=='qq') {
                $redirect = config('services.qq.redirect');
                Config::set('services.qq.redirect',$redirect.'?_id='.$userId);
            }
        }

        return Socialite::driver($service)->redirect();
    }

    /**
     * 授权回调
     *
     * @param Request $request
     * @param $service
     * @return \Illuminate\Http\JsonResponse|\Illuminate\Http\RedirectResponse
     * @throws ApiException
     */
    public function handleProviderCallback(Request $request, $service)
    {
        $userId = $request->input('_id');
        Log::info('handleProviderCallback', [$userId]);
        $oauthUser = Socialite::driver($service)->user();

        $socialite = new SocialiteService($oauthUser, $service);

        // 用户绑定
        if ($userId) {
            $data = $socialite->binding($userId);

            $url = sprintf('%s/#/?bind=%s&code=%s', env('APP_URL'), json_encode($data['msg']), $data['code'] ?? 0);
            return redirect()->away($url);
        }

        // 授权登陆
        $item = $socialite->bindingInfo();
        if (!$item) {
            return redirect()->away(
                env('APP_URL').'/#/register?redirect=bind'
            );
        } else {
            $userId = $item->user_id;
            $user = User::query()->where('id', $userId)->first();
            $user->api_token = $user->toJson() . time();
            $user->save();

            // 登录赠送积分，每天一次
//            IntegralService::getInstance()->income($userId, 3, 'login_give', '登录赠送');
        }

        return redirect()->away(
            env('APP_URL').'/#/?token=' . $user->api_token
        );
    }

    /**
     * 取消绑定
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function unbind(Request $request)
    {
        $userId = $request->input('_id');
        $type = $request->input('type');

        if (!$userId || !$type) {
            throw new ApiException('参数错误', ResponseCode::PARAM_ERR);
        }

        SocialiteModel::query()
            ->where('user_id', $userId)
            ->where('type', $type)
            ->delete();

        return $this->responseSuccess('取消绑定成功');
    }
}
