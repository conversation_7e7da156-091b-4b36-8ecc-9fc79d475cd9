<?php

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\RechargeOrder;
use App\Services\PaymentService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class RechargeController extends Controller
{
    /**
     * 展示订单信息
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function info(Request $request)
    {
        $user = $request->user('api');

        $orderNo = $request->input('order_no');

        $order = RechargeOrder::query()->where('order_no', $orderNo)->first();
        if (!$order || $order->user_id != $user->id) {
            throw new ApiException('订单信息不存在！', ResponseCode::NOT_FOUND);
        }

        return $this->responseSuccess($order, '获取成功！');
    }

    /**
     * 创建充值订单,并生成支付二维码
     * @param Request $request
     * @return JsonResponse
     * @throws ApiException
     */
    public function toPay(Request $request)
    {
        $userId = $request->user('api')->id;
        $payMethod = $request->input('pay_method');
        // 光子数量
        $num = $request->input('num');

        if (!in_array($payMethod, [1, 2])) {
            throw new ApiException('支付方式错误', ResponseCode::PARAM_ERR);
        }

        if ($num < 1) {
            throw new ApiException('充值光子数量无效', ResponseCode::PARAM_ERR);
        }

//        if ($payMethod == 2) {
//            throw new ApiException('支付宝支付暂时关闭，请先使用微信支付', ResponseCode::PARAM_ERR);
//        }

        $orderNo = RechargeOrder::generateOrderNo();

        // 光子：人民币 = 1：10
        $amount = bcmul($num, 10, 2);

        $order = RechargeOrder::query()->create([
            'user_id' => $userId,
            'order_no' => $orderNo,
            'num' => $num,
            'pay_method' => $payMethod,
            'order_amount' => $amount,
            'total_amount' => $amount,
            'status' => Order::StatusMappings['wait'],
        ]);

        $payForm = PaymentService::getInstance()->getRechargePayCodeUrl($orderNo);

        if ($payMethod == 1) {
            return $this->responseSuccess([
                'payCodeImg' => route('qrcode', ['str' => encrypt($payForm)]),
                'orderNo' => $orderNo,
            ],
                '支付码生成成功'
            );
        } else {
            return $this->responseSuccess($payForm, '表单创建成功');
        }
    }
}
