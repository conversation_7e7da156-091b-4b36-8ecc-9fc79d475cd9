<?php

namespace App\Http\Controllers\Api;

use App\Constants\Common;
use App\Constants\ResponseCode;
use App\Events\Forget;
use App\Exceptions\ApiException;
use App\Http\Requests\Api\AuthLogin;
use App\Http\Requests\Api\AuthReset;
use App\Http\Requests\Api\RegisterRequest;
use App\Models\SecurityAnswer;
use App\Models\User;
use App\Models\VerificationCode;
use App\Models\Wallet;
use App\Services\IntegralService;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
    /**
     * 登录
     * @param AuthLogin $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function login(AuthLogin $request)
    {
        $data = $request->only(['code', 'phone', 'password', 'type']);

        $user = User::query()
            ->where('phone', $data['phone'])
            ->orWhere('email', $data['phone'])
            ->first();
        if (!$user || $user->state === 1) {
            throw new ApiException('用户不存在', ResponseCode::FORBIDDEN);
        }

        switch ($data['type']) {
            case Common::LOGIN_BY_PASS:
                if (!Hash::check($data['password'], $user->password)) {
                    throw new ApiException('账号或密码错误', ResponseCode::FORBIDDEN);
                }
                break;
            case Common::LOGIN_BY_CODE:
                $verCode = VerificationCode::check($data['phone'], Common::LOGIN, $data['code']);
                if (!is_dev()) {
                    $verCode->used();
                }
                break;
        }

        $user->api_token = $user->toJson() . time();
        if (!$user->save()) {
            throw new ApiException('登录失败', ResponseCode::SERVER_ERR);
        }
        $user->avatar = handle_url($user->avatar);

        $user->makeHidden(['register_id']);
        $user->load('vip');

        return $this->responseSuccess([
            'token' => $user->api_token,
            'user' => $user
        ]);
    }

    /**
     * 退出登录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function logout(Request $request)
    {
        $user = $request->user('api');
        $user->api_token = "";
        if (!$user->save()) {
            throw new ApiException('退出失败', ResponseCode::SERVER_ERR);
        }

        return $this->responseSuccess(null, '退出成功');
    }

    /**
     * 注册
     *
     * @param RegisterRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function register(RegisterRequest $request)
    {
        $data = $request->only(['phone', 'password', 'code', 'name']);
        // 20230823 新增邮箱注册
        $phone = $data['phone'];
        if (is_email($phone)) {
            $user = User::query()->where('email', $phone)->first();
            if ($user) {
                throw new ApiException('邮箱已注册', ResponseCode::SERVER_ERR);
            }
        } else {
            $user = User::query()->where('phone', $phone)->first();
            if ($user) {
                throw new ApiException('手机号已注册', ResponseCode::SERVER_ERR);
            }
        }


        $verCode = VerificationCode::check($data['phone'], Common::REGISTER, $data['code']);
        try {
            DB::beginTransaction();

            if (!is_dev()) {
                $verCode->used();
            }

            $user = new User();
            if (is_email($data['phone'])) {
                $user->email = $data['phone'];
            } else {
                $user->phone = $data['phone'];
            }
            $user->password = $data['password'];
            $user->name = $data['name'] ?: $user->phone;
            $user->register_ip = request_ip();
            $user->source = 'web';
            $user->api_token = $user->toJson() . time();
            $user->save();

            DB::commit();
            return $this->responseSuccess('注册成功');
        } catch (\Throwable $t) {
            DB::rollBack();
            throw new ApiException($t->getMessage(), ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 忘记密码
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function forget(Request $request)
    {
        $email = $request->input('email');
        if (!$email || !is_email($email)) {
            throw new ApiException('请填写正确邮箱', ResponseCode::SERVER_ERR);
        }

        if (!User::query()->where('email', $email)->exists()) {
            throw new ApiException('邮箱不存在', ResponseCode::SERVER_ERR);
        }

        event(new Forget($email));

        return $this->responseSuccess('已将重置密码链接发往邮箱，请前往进行重置操作');
    }

    /**
     * 密码重置
     *
     * @param AuthReset $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function reset(AuthReset $request)
    {
        $password = $request->input('password');
        $timestamp = $request->input('_timestamp');
        $verify_code = $request->input('verify_code');
        $email = $request->input('email');
        $time = strtotime(date('Y-m-d H'));

        $code = md5($email . 'xiguang'. $time);
        if ($verify_code != $code) {
            throw new ApiException('验证未通过，请重试或联系客服人员处理', ResponseCode::NOT_FOUND);
        }

        $answerData = [
            1 => $request->input('answer1'),
            2 => $request->input('answer2'),
            3 => $request->input('answer3'),
        ];

        $user = User::query()->where('email', $email)->first();
        if (!$user) {
            throw new ApiException('用户信息不存在', ResponseCode::NOT_FOUND);
        }

        $answers = SecurityAnswer::query()
            ->where('user_id', $user->id)
            ->get();
        if (!$answers->count()) {
            throw new ApiException('该账号暂未设置安全问题，无法进行密码重置', ResponseCode::NOT_FOUND);
        }

        foreach ($answers as $answer) {
            if (!isset($answerData[$answer->question_id]) || !$answerData[$answer->question_id]) {
                throw new ApiException('安全问题'.$answer->question_id.'错误', ResponseCode::NOT_FOUND);
            }

            if ($answerData[$answer->question_id] != $answer->content) {
                throw new ApiException('安全问题'.$answer->question_id.'错误', ResponseCode::NOT_FOUND);
            }
        }

        $user->password = $password;
        if (!$user->save()) {
            throw new ApiException('重置密码失败', ResponseCode::SERVER_ERR);
        }

        return $this->responseSuccess(null, '重置密码成功');
    }
}
