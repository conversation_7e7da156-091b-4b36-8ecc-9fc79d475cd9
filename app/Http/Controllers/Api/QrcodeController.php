<?php
/**
 * 二维码生成工具
 * @package App\Http\Controllers\Api
 *
 * <AUTHOR> <<EMAIL>>
 */

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use Endroid\QrCode\Color\Color;
use Endroid\QrCode\Encoding\Encoding;
use Endroid\QrCode\ErrorCorrectionLevel\ErrorCorrectionLevelHigh;
use Endroid\QrCode\Logo\Logo;
use Endroid\QrCode\QrCode;
use Endroid\QrCode\RoundBlockSizeMode\RoundBlockSizeModeMargin;
use Endroid\QrCode\Writer\PngWriter;
use Illuminate\Http\Request;

class QrcodeController extends Controller
{
    /**
     * @param Request $request
     * @throws ApiException
     */
    public function index(Request $request)
    {
        $str = $request->route('str');

        if (empty($str)) {
            throw new ApiException('参数str不能为空！', ResponseCode::PARAM_ERR);
        }

        $writer = new PngWriter();

        // Create QR code
        $qrCode = QrCode::create(decrypt($str))
            ->setEncoding(new Encoding('UTF-8'))
            ->setErrorCorrectionLevel(new ErrorCorrectionLevelHigh())
            ->setSize(300)
            ->setMargin(10)
            ->setRoundBlockSizeMode(new RoundBlockSizeModeMargin())
            ->setForegroundColor(new Color(0, 0, 0))
            ->setBackgroundColor(new Color(255, 255, 255));

        // Create generic logo
        $logo = Logo::create(public_path('upload/images/xg.jpg'))
            ->setResizeToWidth(100);

        $result = $writer->write($qrCode, $logo);

        header('Content-Type: ' . $result->getMimeType());
        echo $result->getString();
    }
}
