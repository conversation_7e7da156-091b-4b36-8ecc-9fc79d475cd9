<?php

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\CourseCategory;
use App\Models\Plug;
use App\Models\PlugCategory;
use App\Models\PlugDetail;
use App\Models\UserFans;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class PlugController extends Controller
{
    public function getchildIds($id)
    {
        $ids = PlugCategory::query()->when($id, function ($q) use ($id){
            if(is_array($id)){
                return $q -> whereIn('parent_id', $id);
            } else {
                return $q -> where('parent_id', $id);
            }
        }) -> pluck('id')->toArray();
        if(count($ids)){
            return array_merge($ids, $this -> getchildIds($ids));
        } else {
            return $ids;
        }
    }
    /**
     * 列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $limit = $request->input('limit', 20);
        $offset = $request->input('offset', 0);
        $cateId = $request->input('cate_id');
        $level = $request->input('level');
        $sort = $request->input('sort');
        $cateIds = [];
        $cateIds = $this->getchildIds($cateId);

        $plugs = Plug::query()
            ->with('suggestUser')
            ->where(function ($query) use ($cateId, $cateIds) {
                $query->where('cate_id', $cateId) -> orWhereIn('cate_id', $cateIds);
            })
            ->where('state', 1)
            ->when($sort!=null, function ($query) use ($sort){
                if ($sort == 0) {
                    $query->orderByDesc('updated_at');
                } else if ($sort == 1) {
                    $query->orderByDesc('sort')
                        ->orderByDesc('id');
                }
            })
            ->offset($offset * $limit)
            ->limit($limit)
            ->get();
        $total = Plug::query()
            ->where(function ($query) use ($cateId, $cateIds) {
                $query->where('cate_id', $cateId) -> orWhereIn('cate_id', $cateIds);
            })
            ->where('state', 1)
            ->count();

        $cate_name = '';
        if ($cateId) {
            $cate_name = PlugCategory::query()->where('id', $cateId)->value('name');
        }

        foreach ($plugs as $plug) {
            $plug->author = $plug->suggestUser->name ?? '犀光RadiRhino';
            $plug->images = handle_url($plug->images);
            if (is_dev()) {
                $plug->images = 'https://www.helloimg.com/images/2022/04/07/RsJYLA.jpg';
            }
            $plug->intro =  $limit != 3 ? substr_with_powerful($plug->intro, 20):substr_with_powerful($plug->intro, 150);
            $plug->time = Carbon::parse($plug->created_at)->format('Y-m-d');
        }

        return $this->responseSuccess(compact('plugs', 'total', 'cate_name'));
    }

    /**
     * 列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function list()
    {
        $items = PlugCategory::query()
            ->whereHas('plug')
            ->selectRaw('id,name,sort')
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->get();

        foreach ($items as $item) {
            $item->plug = Plug::query()
                ->with('suggestUser')
                ->where('state', 1)
                ->where('cate_id', $item->id)
                ->limit(6)
                ->selectRaw('id,name,images,created_at,author,price,user_id')
                ->get()
                ->map(function ($value) {
                    $value->author = $value->suggestUser->name ?? '犀光RadiRhino';
                    $value->images = handle_url($value->images);
                    $value->name = substr_with_powerful($value->name, 30);
                    $value->date = Carbon::parse($value->created_at)->format('Y/m/d');
                    $value->makeHidden(['cate', 'admin', 'created_at']);
                    return $value;
                })
                ->toArray();
        }

        return $this->responseSuccess($items);
    }

    /**
     * 获取插件基础信息
     *
     * @param Request $request
     * @param $plug_id
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function info(Request $request, $plug_id)
    {
        $user = $request->user('api');
        $plugInfo = Plug::query()
            ->where('state', 1)
            ->with([
                'detail:plug_id,version_code,version_name,type,platform,description,link,created_at,updated_at',
                'intros:plug_id,content,images',
                'cate:id,name',
                'suggestUser',
                'tagListInfo'
            ])
            ->where('id', $plug_id)
            ->where('state', 1)
            ->first();

        if (!$plugInfo) {
            throw new ApiException('插件不存在', ResponseCode::NOT_FOUND);
        }

        $plugInfo->append(['sales_num', 'purchase', 'web_link_purchase']);
        $plugInfo->author = $plugInfo->suggestUser->name ?? '犀光RadiRhino';
        $plugInfo->images = handle_url($plugInfo->images);

        foreach ($plugInfo->intros as &$item1) {
            $item1->images = handle_url($item1->images);
        }

        // 下载状态
        $plugInfo->download = $request->user('api') ? true : false;

        $plugInfo = $plugInfo->toArray();
        foreach ($plugInfo['detail'] as &$item) {
            if ($plugInfo['purchase']<=0 && $plugInfo['price'] > 0) {
                $item['download_link'] = '';
            }
            if ($plugInfo['web_link_purchase']<=0 && $plugInfo['web_link_price'] > 0) {
                $item['web_link'] = '';
            }
            $item['platform'] = is_array($item['platform']) ? implode('&',$item['platform']) : $item['platform'];
            $item['time'] = Carbon::parse($item['updated_at'])->toDateString();
            unset($item['link']);
            unset($item['plug_id']);
            unset($item['created_at']);
        }

        // 获取前一个id
        $plugInfo['prev_id'] = Plug::query()
            ->where('state', 1)
            ->where('id', '<', $plugInfo['id'])
            ->orderByDesc('id')
            ->value('id') ?: 0;

        $plugInfo['next_id'] = Plug::query()
            ->where('state', 1)
            ->where('id', '>', $plugInfo['id'])
            ->orderBy('id')
            ->value('id') ?: 0;
        $plugInfo['nofans'] = UserFans::getFansStatus($user, $plugInfo['user_id']);
        return $this->responseSuccess($plugInfo);
    }

    /**
     * 插件映射信息
     * @return \Illuminate\Http\JsonResponse
     */
    public function mapping()
    {
        $plugMap = Plug::query()
            ->where('state', 1)
            ->orderByDesc('sort')
            ->orderByDesc('created_at')
            ->get(['id', 'name']);

        foreach ($plugMap as $item) {
            $item->makeHidden(['admin']);
        }

        return $this->responseSuccess($plugMap);
    }

    /**
     * 分类映射
     * @return \Illuminate\Http\JsonResponse
     */
    public function cate(Request $request)
    {
        $parentId = $request->parent_id;

        $courseMap = PlugCategory::query()
//            ->with('childrenCategory:id,name,parent_id')
            ->with(['childrenCategory' => function($q){
                return $q -> with(['childrenCategory' => function($q){
                    return $q -> with(['childrenCategory'])->orderByDesc('sort');
                }])->orderByDesc('sort');
            }])
            ->where(function ($query) use ($parentId) {
                if ($parentId > 0) {
                    $query->where('parent_id', $parentId);
                } else {
                    $query->where('parent_id', 0);
                }
            })
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->get(['id', 'name', 'parent_id'])
            ->toArray();

        array_unshift($courseMap, [
            'id' => 0,
            'level' => empty($parentId) > 0 ? 1 : 2,
            'name' => '全部',
            'parent_id' => empty($parentId) ? 0 : $parentId,
            'children_category' => []
        ]);

        return $this->responseSuccess($courseMap);
    }

    /**
     * 指定详情 [此接口看情况可取消]
     * @param Request $request
     * @param $plugId
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function details(Request $request, $plug_id)
    {
        $type = $request->input('type');

        if (!$type || !in_array($type,PlugDetail::TypeMappings)) {
            throw new ApiException('类型错误', ResponseCode::PARAM_ERR);
        }

        $details = PlugDetail::query()
            ->where('plug_id', $plug_id)
            ->where('type', $type)
            ->get();

        return $this->responseSuccess($details);
    }

    /**
     * 下载数量统计
     * @param Request $request
     * @param $plug_id
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function downloadCount(Request $request, $plug_id)
    {
        $field = $request->input('field');

        if (!in_array($field, ['download_package', 'download_manual', 'download_case'])) {
            throw new ApiException('类型错误', ResponseCode::PARAM_ERR);
        }

        Plug::query()->where('id', $plug_id)->increment($field);
        return $this->responseSuccess();
    }
}
