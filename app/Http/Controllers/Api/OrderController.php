<?php

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Jobs\AutoCloseOrderJob;
use App\Jobs\ShenzaoAuthorize;
use App\Models\CouponCode;
use App\Models\Course;
use App\Models\Goods;
use App\Models\Order;
use App\Models\OrderConsignee;
use App\Models\Plug;
use App\Models\UserAddress;
use App\Models\UserCourse;
use App\Models\Wallet;
use App\Models\WalletLog;
use App\Services\IntegralService;
use App\Services\OrderService;
use App\Services\PaymentService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class OrderController extends Controller
{
    /**
     * 用户订单列表
     * <AUTHOR> 2021-10-04T17:49:33+0800
     *
     * @param  Request                         $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = $request->user('api');
        $offset = $request->input('offset', 0);
        $limit = $request->input('limit', 10);
        $keywords = $request->input('keywords');
        $cate_id = $request->input('cate_id');

        $lists = Order::query()
            ->when($keywords != '', function ($query) use ($keywords) {
                $query->where('order_no', 'like', '%' . $keywords . '%')
                    ->orWhere('product_name', 'like', '%' . $keywords . '%');
            })
//            ->when($cate_id != '', function ($query) use ($cate_id) {
//                if ($cate_id == '1') {
//                    $query->where('product_type', 3);
//                } else {
//                    $query->where('product_type', '!=', 3);
//                }
//            })
            ->where('user_id', $user->id)
            ->orderByDesc('created_at')
            ->offset($offset * $limit)
            ->limit($limit)
            ->get()->map(function ($value) {
                $value->product_img = handle_url($value->product_img);

                return $value;
            });

        $total = Order::query()
            ->when($keywords != '', function ($query) use ($keywords) {
                $query->where('order_no', 'like', '%' . $keywords . '%')
                    ->orWhere('product_name', 'like', '%' . $keywords . '%');
            })
//            ->when($cate_id != '', function ($query) use ($cate_id) {
//                if ($cate_id == '1') {
//                    $query->where('product_type', 3);
//                } else {
//                    $query->where('product_type', '!=', 3);
//                }
//            })
            ->where('user_id', $user->id)
            ->count();

        return $this->responseSuccess(compact('lists', 'total'), '获取成功！');
    }

    /**
     * 订单信息
     * <AUTHOR> 2021-10-04T17:52:56+0800
     *
     * @param  Request                         $request
     * @throws ApiException
     * @return \Illuminate\Http\JsonResponse
     */
    public function orderInfo(Request $request)
    {
        $userId  = $request->user('api')->id;
        $orderNo = $request->input('order_no');

        $where = [
            'user_id'  => $userId,
            'order_no' => $orderNo
        ];
        if ( ! $order = Order::query()->where($where)->first()) {
            throw new ApiException('订单信息不存在！', ResponseCode::NOT_FOUND);
        }

        $order->load('consignee');

        $order->product_img = handle_url($order->product_img);

        $order->usable_integral = 0;
        $order->usable_integral_price = 0;
        $integralInfo =OrderService::getInstance()->integralInfo($order->id);
        $order->is_integral = $integralInfo->is_integral ?? 0;
        if ($order->deduct_integral <= 0 && $order->is_integral) {
            $wallet = Wallet::init($userId);
            $integral_num = $integralInfo->integral_num ?? 0;
            if ($integral_num > 0 && $wallet->balance > $integral_num) {
                $order->usable_integral = $integral_num;
                $order->usable_integral_price = $integral_num/10;
            } else {
                $order->usable_integral = $wallet->balance;
                $order->usable_integral_price = $wallet->balance/10;
            }
        }

        return $this->responseSuccess($order, '获取成功！');
    }

    /**
     * 展示订单信息
     * @param Request $request
     * @param Order $order
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function info(Request $request, Order $order)
    {
        $user = $request->user('api');

        if (!$order || $order->user_id != $user->id) {
            throw new ApiException('订单信息不存在！', ResponseCode::NOT_FOUND);
        }

        $order->load('consignee');
        $order->load('logistic');

        $order->product_img = handle_url($order->product_img);

        return $this->responseSuccess($order, '获取成功！');
    }

    /**
     * 前去支付 / 生成支付码
     * <AUTHOR> 2021-10-04T17:52:56+0800
     *
     * @param  Request                         $request
     * @throws ApiException
     * @return \Illuminate\Http\JsonResponse
     */
    public function toPay(Request $request)
    {
        throw new ApiException('支付已停用', ResponseCode::PARAM_ERR);
        $userId    = $request->user('api')->id;
        $orderNo   = $request->input('order_no');
        $payMethod = $request->input('pay_method');
        $couponCode= $request->input('coupon_code');
        $deduct_integral = $request->input('deduct_integral', 0);
        $addressId= $request->input('address_id', 0);

        if ( ! in_array($payMethod, [1, 2])) {
            throw new ApiException('支付方式错误', ResponseCode::PARAM_ERR);
        }

        if ($payMethod) {
            PaymentService::$payMethod = $payMethod;
        }

        $where = [
            'user_id'  => $userId,
            'order_no' => $orderNo,
        ];

        if ( ! $order = Order::query()->where($where)->first()) {
            throw new ApiException('订单信息不存在！', ResponseCode::NOT_FOUND);
        }

        // 非正常关闭订单处理
        if ($order->created_at <= Carbon::now()->subMinutes(Order::expiredMinutes)) {
            $order->status = 2;
            $order->remark = '系统异常关闭订单';
            $order->close_time = now();
            $order->save();
            throw new ApiException('订单异常关闭，请重新下单购买！', ResponseCode::NOT_FOUND);
        }

        if ($order->order_amount < 0) {
            throw new ApiException('订单信息异常！', ResponseCode::FORBIDDEN);
        }

        // 实物商品购买需要收货地址
        $user_address = null;
        if ($order->product_type == 3||$order->product_type == 1) {
            if (!$addressId) {
                throw new ApiException('请填写选择地址！', ResponseCode::NOT_FOUND);
            }

            $user_address = UserAddress::query()->where('id',$addressId)->where('user_id', $userId)->first();
            if (!$user_address){
                return $this->responseError('用户地址不存在', ResponseCode::FORBIDDEN);
            }
        }
        // 扣除光粒
        $integralInfo = OrderService::getInstance()->integralInfo($order->id);
        $isIntegral = $integralInfo->is_integral ?? 0;
        $integralNum = $integralInfo->integral_num ?? 0;
        if ($deduct_integral > 0 && $isIntegral) {
            if ($integralNum>0 && $deduct_integral > $integralNum) {
                throw new ApiException('光粒抵扣数量错误！', ResponseCode::FORBIDDEN);
            }
            $wallet = Wallet::init($userId);
            if ($wallet->balance >= $deduct_integral) {
                $wallet->balance -= $deduct_integral;
                $wallet->expend += $deduct_integral;
                $wallet->save();

                $type = WalletLog::TypeMappings['goods'];
                switch ($order->product_type){
                    case 1:
                        $type = WalletLog::TypeMappings['course'];
                        break;
                    case 2:
                        $type = WalletLog::TypeMappings['plug'];
                        break;
                    case 3:
                        $type = WalletLog::TypeMappings['goods'];
                        break;
                }

                $walletLog = new WalletLog();
                $walletLog->title = '购买-'.$order->product_name;
                $walletLog->target_type = 'order';
                $walletLog->target_id = $order->id;
                $walletLog->amount = $deduct_integral;
                $walletLog->user_id = $order->user_id;
                $walletLog->currency = 'integral';
                $walletLog->action = WalletLog::ActionMappings['expend'];
                $walletLog->status = WalletLog::StatusMappings['success'];
                $walletLog->type = $type;
                $walletLog->extend = ['balance'=>$wallet->balance];
                $walletLog->save();

                $order->deduct_integral += $deduct_integral;
                $deduct = bcdiv($deduct_integral, 10, 2);
                $order->integral_deduct_price += $deduct;
                $orderAmount = bcsub($order->order_amount, $deduct, 2);
                $order->order_amount = $orderAmount > 0 ? $orderAmount : 0;
                $order->save();
            }
        }

        if ($couponCode && $order->coupon_code!=$couponCode) {
            $coupon_price = CouponCode::getPrice($couponCode, $userId, $order->product_type, $order->product_id);
            if ($coupon_price) {
                $curr_deduct_price = bcsub($coupon_price, $order->deduct_price, 2);
                $orderAmount = bcsub($order->order_amount, $curr_deduct_price, 2);
                $order->order_amount = $orderAmount > 0 ? $orderAmount : 0;
                $order->deduct_price = $coupon_price;
                $order->coupon_code = $couponCode;
                $order->save();
            }
        }

        if ($user_address) {
            //写入订单发货地址
            $orderConsignee = new OrderConsignee();
            $orderConsignee->user_id = $userId;
            $orderConsignee->order_id = $order->id;
            $orderConsignee->name = $user_address->name;
            $orderConsignee->phone = $user_address->phone;
            $orderConsignee->address = $user_address->address;
            $orderConsignee->save();
        }

        // 免费产品不进行支付
        if ($order->order_amount == 0) {
            $order->status = Order::StatusMappings['paid'];
            $order->pay_time = now()->toDateTimeString();
//            $order->pay_method = $payMethod;
            $order->save();

            // 使用优惠码
            if ($order->coupon_code) {
                CouponCode::use($order->coupon_code, $userId);
            }

            // 赠送积分处理
            PaymentService::getInstance()->giveIntegral($order);

            if ($order->product_type == Order::ProductTypeMappings['course']) {
                $userCourse = UserCourse::query()
                    ->where('course_id', $order->product_id)
                    ->where('user_id', $order->user_id)
                    ->where('type', UserCourse::TypeMappings['learn'])
                    ->first();

                if ( ! $userCourse) {
                    $userCourse            = new UserCourse();
                    $userCourse->user_id   = $userId;
                    $userCourse->course_id = $order->product_id;
                    $userCourse->type      = UserCourse::TypeMappings['learn'];
                    if ( ! $userCourse->save()) {
                        Log::error('toPay：用户课程：', [$orderNo, $userId]);
                    }

                    Course::query()->where('id', $order->product_id)->increment('num');
                } else {
                    return $this->responseError('已在学习课程中', ResponseCode::PARAM_ERR);
                }

                $shenzaoCourseId = $userCourse->course->sz_course_id;
                if ($shenzaoCourseId) {
                    // 课程授权深造绑定账号权限
                    dispatch(new ShenzaoAuthorize($order->user->phone, $shenzaoCourseId));
                }
            }

            return $this->responseSuccess(null, '成功');
        }

        $payForm = PaymentService::getInstance()->getPayCodeUrl($orderNo);

        if ($payMethod == 1) {
            return $this->responseSuccess([
                'payCodeImg' => route('qrcode', ['str' => encrypt($payForm)])],
                '支付码生成成功'
            );
        } else {
            return $this->responseSuccess($payForm, '表单创建成功');
        }
    }

    /**
     * 创建用户订单
     *
     * <AUTHOR> 2021-10-19T22:41:54+0800
     *
     * @param  Request        $request
     * @throws ApiException
     * @return mixed
     */
    public function create(Request $request)
    {
        throw new ApiException('下单已停用', ResponseCode::PARAM_ERR);
        $userId      = $request->user('api')->id;
        $productType = $request->input('type');
        $productId   = $request->input('id');
        $num         = $request->input('num', 1);

        if ( ! in_array($productType, [1, 2, 3])) {
            throw new ApiException('产品类型错误', ResponseCode::PARAM_ERR);
        }

        $where = [
            'user_id'      => $userId,
            'product_type' => $productType,
            'product_id'   => $productId,
            'status'       => 0
        ];

        $orderInfo = Order::query()
            ->where($where)
            ->first();

        // logger([$orderInfo]);die;

        if ($orderInfo) {
            // 非正常关闭订单处理
            if ($orderInfo->created_at <= Carbon::now()->subMinutes(Order::expiredMinutes)) {
                $orderInfo->status = 2;
                $orderInfo->remark = '系统异常关闭订单';
                $orderInfo->close_time = now();
                $orderInfo->save();
//                throw new ApiException('订单异常关闭，请重新下单购买！', ResponseCode::NOT_FOUND);
            } else {
                return $this->responseSuccess($orderInfo, '已存在订单信息');
            }
        }

        try {
            $order               = new Order();
            $order->user_id      = $userId;
            $order->order_no     = date('YmdHi') . \Str::random(9);
            $order->product_type = $productType;
            $order->product_id   = $productId;
            $order->num          = $num;
            switch ($productType) {
                case '1':
                    if ( ! $course = Course::query()->where('is_delete', Course::NOT_DELETE)->find($productId)) {
                        throw new ApiException('课程信息不存在', ResponseCode::NOT_FOUND);
                    }
                    $price = bcmul($order->num, $course->price, 2);

                    $order->product_name = $course->name;
                    $order->order_amount = bcadd($course->postage, $price ,2);
                    $order->total_amount = bcadd($course->postage, $price ,2);
                    $order->product_img  = $course->images;
                    $order->give_integral = $course->give_integral;
                    break;

                case '2':
                    if ( ! $plug = Plug::query()->where('state', 1)->find($productId)) {
                        throw new ApiException('插件不存在', ResponseCode::NOT_FOUND);
                    }
                    $price = bcmul($order->num, $plug->price, 2);

                    $order->product_name = $plug->name;
                    $order->order_amount = bcadd($plug->postage, $price ,2);
                    $order->total_amount = bcadd($plug->postage, $price ,2);
                    $order->product_img  = $plug->images;
                    $order->give_integral = $plug->give_integral;
                    break;
                case '3':
                    if ( ! $goods = Goods::query()->where('is_delete', Goods::NOT_DELETE)->where('status', 1)->find($productId)) {
                        throw new ApiException('商品不存在', ResponseCode::NOT_FOUND);
                    }

                    if ($goods->stock < $order->num) {
                        throw new ApiException('库存不足', ResponseCode::NOT_FOUND);
                    }

                    $price = bcmul($order->num, $goods->price, 2);

                    $order->product_name = $goods->name;
                    $order->order_amount = bcadd($goods->postage, $price ,2);
                    $order->total_amount = bcadd($goods->postage, $price ,2);
                    $order->product_img  = $goods->cover;
                    $order->postage = $goods->postage;
                    $order->give_integral = $goods->give_integral;

                    $updateStock = Goods::query()
                        ->where('id', $goods->id)
                        ->where('stock', $goods->stock)
                        ->update([
                            'sales_num' => DB::raw('sales_num + ' . $order->num),
                            'stock' => DB::raw('stock - ' . $order->num),
                        ]);
                    if (!$updateStock) {
                        throw new ApiException('库存不足', ResponseCode::NOT_FOUND);
                    }
                    break;
            }

            if ( ! $order->save()) {
                throw new ApiException('订单创建失败', ResponseCode::SERVER_ERR);
            }
            DB::commit();
            $this->dispatch((new AutoCloseOrderJob($order))->delay(now()->addMinutes(Order::expiredMinutes)));
        } catch (\Throwable $e) {
            DB::rollBack();
            throw new ApiException($e->getMessage(), ResponseCode::SERVER_ERR);
        }

        return $this->responseSuccess($order, '订单创建成功');
    }

    /**
     * 免费课程处理
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function freeCourse(Request $request)
    {
        $user = $request->user('api');
        $productType = $request->input('type');
        $productId = $request->input('id');

        if ($productType == 1) {
            if (!$course = Course::query()->find($productId)) {
                throw new ApiException('课程信息不存在', ResponseCode::NOT_FOUND);
            }

            if ($course->price > 0) {
                throw new ApiException('课程已需要收费', ResponseCode::NOT_FOUND);
            }

            $userCourse = UserCourse::query()
                ->where('user_id', $user->id)
                ->where('course_id', $course->id)
                ->where('type', UserCourse::TypeMappings['learn'])
                ->first();
            if ( ! $userCourse) {
                $userCourse = new UserCourse();
                $userCourse->user_id = $user->id;
                $userCourse->course_id = $course->id;
                $userCourse->type = UserCourse::TypeMappings['learn'];
                if (!$userCourse->save()) {
                    throw new ApiException('好像出错了，请刷新页面重试', ResponseCode::NOT_FOUND);
                }
            }

            $shenzaoCourseId = $userCourse->course->sz_course_id;
            if ($shenzaoCourseId) {
                // 课程授权深造绑定账号权限
                dispatch(new ShenzaoAuthorize($user->phone, $shenzaoCourseId));
            }

            return $this->responseSuccess(null, '已加入学习课程');
        }

        return $this->responseSuccess(null, '成功');
    }
}
