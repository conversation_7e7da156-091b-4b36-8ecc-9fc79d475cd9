<?php

namespace App\Http\Controllers\Api;

use App\Constants\Common;
use App\Constants\ResponseCode;
use App\Events\SetEmail;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\SetPasswordRequest;
use App\Http\Requests\Api\UserUpdate;
use App\Models\CommentReply;
use App\Models\Course;
use App\Models\CourseCategory;
use App\Models\Goods;
use App\Models\GoodsCategory;
use App\Models\Order;
use App\Models\Plug;
use App\Models\PlugCategory;
use App\Models\PlugDetail;
use App\Models\SecurityAnswer;
use App\Models\TagList;
use App\Models\User;
use App\Models\UserCourse;
use App\Models\UserFans;
use App\Models\UserFile;
use App\Models\UserGoods;
use App\Models\UserPlug;
use App\Models\UserRoleCate;
use App\Models\UserUploadConfig;
use App\Models\VerificationCode;
use App\Models\Wallet;
use App\Models\WalletLog;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Storage;

class UserFansController extends Controller
{


    public function getUserFansInfo($fid, Request $request)
    {
        $user = $request->user('api');
        $userInfo = User::query() -> select([
            'id', 'name', 'avatar', 'phone', 'intro', 'showfans'
        ])
            -> where('id', $fid) -> first();
        if(is_null($user)){
            $data['isfans'] = false;
        } else {
            $data['isfans'] = UserFans::query()
                -> where('fans_id', $user['id'])
                -> where('user_id', $userInfo['id'])
                -> exists();
            $data['isfansShow'] = $userInfo['id']!=$user['id']?true:false;
        }

        $data['userInfo'] = $userInfo;
        $data['fansNum'] = UserFans::getFansNum($userInfo['id'], $userInfo['showfans']);
        $data['attentionNum'] = UserFans::getAttentionNum($userInfo['id'],  $userInfo['showfans']);
        //课件
//        $res = $model -> where('id', $request -> id) -> where('user_id', $user['id']) -> delete();
        return $this->responseSuccess($data);
    }

    public function getUserFansListInfo($type, Request $request){

        $user = $request->user('api');
        $uid = $request->input('uid');
        if (!in_array($type, UserFans::$TypeMappings)) {
            throw new ApiException('参数错误', ResponseCode::PARAM_ERR);
        }
        if($type=='course'){
            $model = Course::query();
        } else if($type=='plug'){
            $model = Plug::query()->where('state', 1);
        } else if($type=='goods'){
            $model = Goods::query()->where('status', 1);
        }
        $list = $model->where('user_id', $uid) -> paginate(12);

        $data['list'] = $list;

        return $this->responseSuccess($data);
    }

    public function userGuanzhu($fid, Request $request)
    {
        $user = $request->user('api');
        if(!is_null($user)&&$user['id']!= $fid){
            $userFans = UserFans::query() -> where('fans_id', $user['id'])
                -> where('user_id', $fid) -> exists();
            if($userFans){
                $userFans = UserFans::query() -> where('fans_id', $user['id'])
                    -> where('user_id', $fid) -> delete();
                return $this->responseSuccess('', '取关成功');
            } else {
                $userFans = new UserFans();
                $userFans -> fans_id = $user['id'];
                $userFans -> user_id = $fid;
                $userFans -> save();
                return $this->responseSuccess('', '关注成功');
            }

        } else {
            if(is_null($user)){
                throw new ApiException('请登录', 401);
            } else if($user['id']== $fid){
                throw new ApiException('禁止关注本人', 4001);
            }
            throw new ApiException('关注失败', 4001);
        }
    }

    public function getUserFansList(Request $request)
    {
        $uid = $request -> uid;
        $limit = $request -> limit;
        $list = UserFans::query()
            ->with('fansInfo')
            ->where('user_id', $uid)
            ->paginate($limit);
        $total = $list->total();
        $list = $list -> map(function ($item) {
            return $item;
        });
        $data = [
            'list' => $list,
            'total' => $total
        ];
        return $this->responseSuccess($data, '粉丝列表');
    }

    public function getUserAttentionlist(Request $request)
    {
        $uid = $request -> uid;
        $limit = $request -> limit;
        //关注数
        $list = UserFans::query()
            ->with('attentionInfo')->where('fans_id', $uid)
            ->paginate($limit);

        $total = $list->total();
        $list = $list -> map(function ($item) {
            return $item;
        });
        $data = [
            'list' => $list,
            'total' => $total
        ];
        return $this->responseSuccess($data, '粉丝列表');
    }

}
