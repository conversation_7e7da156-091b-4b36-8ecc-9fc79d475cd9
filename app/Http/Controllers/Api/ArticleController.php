<?php

namespace App\Http\Controllers\Api;

use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\Article;
use Illuminate\Http\Request;

class ArticleController extends Controller
{

    /**
     * 获取文章列表
     * <AUTHOR> 2021-10-02T16:45:26+0800
     *
     * @return mixed
     */
    public function getList()
    {
        $list = Article::query()->where('show_status', 3)
            ->orderBy('sort', 'DESC')
            ->get();

        return $this->responseSuccess($list, '获取成功');
    }
}
