<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\UserUpdate;
use App\Models\Course;
use App\Models\CourseCategory;
use App\Models\Goods;
use App\Models\GoodsCategory;
use App\Models\Plug;
use App\Http\Controllers\Controller;
use App\Models\PlugCategory;

class HomeController extends Controller
{
    public function carousel()
    {
        $courses = Course::query()
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->limit(8)
            ->get()
            ->map(function ($data) {
                $images = config('app.download_url').'/'.$data->images;
                if (is_dev()) {
                    $images = 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg';
                }

                return [
                    'idView' => '',
                    'tips' => '',
                    'id' => $data->id,
                    'images' => $images,
                ];
            })->toArray();

        $plugs = [];
//        $plugs = Plug::query()
//            ->get()
//            ->map(function ($data) {
//                return [
//                    'idView' => 'https://radirhino.oss-cn-beijing.aliyuncs.com//files/homepage-opener-5a55a50aaa4c.f01bcbd.mp4',
//                    'tips' => '',
//                    'images' => config('app.download_url').'/'.$data->images,
//                ];
//            })->toArray();

        $data = array_merge($courses, $plugs);
        shuffle($data);

        return $this->responseSuccess($data);
    }

    /**
     * 列表
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function plug()
    {
        $items = PlugCategory::query()
//            ->whereHas('plug')
            ->where('parent_id', 0)
            ->selectRaw('id,name,sort')
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->get()->toArray();

        array_unshift($items, [
            'id' => 0,
            'name' => '全部',
            'list' => [],
            'sort' => 0,
        ]);

        foreach ($items as &$item) {
            $cateIds = PlugCategory::query()->where('parent_id', $item['id'])->pluck('id')->toArray();

            $cateIds2 = $this->getchildIds($cateIds, 'plug');
            $cateIds = array_merge($cateIds, $cateIds2);
            $item['list'] = Plug::query()
                ->with('suggestUser')
                ->when( $item['id'] != 0, function ($query) use ($cateIds) {
                    $query->whereIn('cate_id', $cateIds);
                })
                ->orderByDesc('sort')
                ->orderByDesc('id')
                ->limit(4)
                ->selectRaw('id,name,images,created_at,author,price,user_id')
                ->get()
                ->map(function ($value) {
                    $value->author = $value->suggestUser->name ?? '犀光RadiRhino';
                    $value->images = handle_url($value->images);
                    if (is_dev()) {
                        $value->images = 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg';
                    }
                    $value->name = substr_with_powerful($value->name, 30);
                    $value->makeHidden(['cate', 'admin', 'created_at']);
                    return $value;
                })
                ->toArray();

            if ($item['id'] != 0 && empty($item['list'])) {
                unset($item);
            }
        }

        return $this->responseSuccess($items);
    }



    public function getchildIds($id, $type='')
    {
        if($type=='course'){
            $model = CourseCategory::query();
        } else if($type=='goods'){
            $model = GoodsCategory::query();
        } else if($type == 'plug'){
            $model = PlugCategory::query();
        } else {
            return [];
        }
        $ids = $model->when($id, function ($q) use ($id){
            if(is_array($id)){
                return $q -> whereIn('parent_id', $id);
            } else {
                return $q -> where('parent_id', $id);
            }
        }) -> pluck('id')->toArray();
        if(count($ids)){
            return array_merge($ids, $this -> getchildIds($ids, $type));
        } else {
            return $ids;
        }
    }
    /**
     * 课程列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function course()
    {
        $items = CourseCategory::query()
//            ->whereHas('course')
            ->where('parent_id', 0)
            ->selectRaw('id,name,sort')
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->get()->toArray();

        array_unshift($items, [
            'id' => 0,
            'name' => '全部',
            'list' => [],
            'sort' => 0,
        ]);

        foreach ($items as &$item) {
            $cateIds = CourseCategory::query()->where('parent_id', $item['id'])->pluck('id')->toArray();

            $cateIds2 = $this->getchildIds($cateIds, 'course');
            $cateIds = array_merge($cateIds, $cateIds2);

            $item['list'] = Course::query()
                ->when($item['id'], function ($query) use ($cateIds) {
                    $query->whereIn('cate_id', $cateIds);
                })
                ->orderByDesc('sort')
                ->orderByDesc('id')
                ->limit(8)
                ->selectRaw('id,name,images,created_at,author,price,market_price')
                ->notDelete()
                ->get()
                ->map(function ($value) {
                    $value->images = $value->images_src;
                    if (is_dev()) {
                        $value->images = 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg';
                    }
                    $value->name = substr_with_powerful($value->name, 30);
                    $value->makeHidden(['cate', 'admin', 'is_annex', 'created_at', 'images_src', 'purchase', 'collect']);
                    return $value;
                })
                ->toArray();
            if ($item['id'] != 0 && empty($item['list'])) {
                unset($item);
            }
        }

        return $this->responseSuccess($items);
    }

    /**
     * 周边
     * @return \Illuminate\Http\JsonResponse
     */
    public function goods()
    {
        $items = GoodsCategory::query()
//            ->whereHas('goods')
            ->where('parent_id', 0)
            ->selectRaw('id,name')
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->get()->toArray();

        array_unshift($items, [
            'id' => 0,
            'name' => '全部',
            'list' => [],
        ]);

        foreach ($items as &$item) {
            $cateIds = GoodsCategory::query()->where('parent_id', $item['id'])->pluck('id')->toArray();

            $cateIds2 = $this->getchildIds($cateIds, 'goods');
            $cateIds = array_merge($cateIds, $cateIds2);
            $item['list'] = Goods::query()
                ->when( $item['id'] != 0, function ($query) use ($cateIds) {
                    $query->whereIn('cate_id', $cateIds);
                })
                ->where('status', Goods::StatusMapping['yes'])
                ->orderByDesc('sort')
                ->orderByDesc('id')
                ->limit(8)
                ->notDelete()
                ->get()
                ->map(function ($value) {
                    $value->cover = handle_url($value->cover);
                    if (is_dev()) {
                        $value->cover = 'https://www.helloimg.com/images/2022/04/05/RmAfdo.jpg';
                    }

                    return $value;
                })
                ->toArray();

            if ($item['id'] != 0 && empty($item['list'])) {
                unset($item);
            }
        }

        return $this->responseSuccess($items);
    }
}
