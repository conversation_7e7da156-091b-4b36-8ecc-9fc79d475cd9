<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\NewCategory;
use App\Models\News;
use Carbon\Carbon;
use Illuminate\Http\Request;

class NewsController extends Controller
{
    /**
     * 新闻列表
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function list(Request $request)
    {
        $limit = $request->input('limit', 20);
        $offset = $request->input('offset', 0);
        $cateId = $request->input('cate_id', 0);

        $news = News::query()
            ->when($cateId, function ($query) use ($cateId) {
                $query->where('cate_id', $cateId);
            })
            ->where('is_hide', 0)
            ->offset($offset * $limit)
            ->limit($limit)
            ->orderByDesc('sort')
            ->orderByDesc('created_at')
            ->get();

        $total = News::query()
            ->when($cateId, function ($query) use ($cateId) {
                $query->where('cate_id', $cateId);
            })
            ->where('is_hide', 0)
            ->count();
        $newest=null;
        foreach ($news as $key=>&$item) {
            $item->content = $limit!=3?substr_with_powerful($item->content, 30):substr_with_powerful($item->content, 150);
            $item->images = handle_url($item->images);
            if (is_dev()) {
                $item->images = 'https://www.helloimg.com/images/2022/04/05/RmD4eo.jpg';
            }
            $item->year = Carbon::parse($item->created_at)->format('Y-m');
            $item->day = Carbon::parse($item->created_at)->format('d');
            $item->time = Carbon::parse($item->created_at)->format('Y-m-d');


            if ($key==0) {
                $newest = $item;
            }
        }

        return $this->responseSuccess(compact('news', 'total', 'newest'));
    }

    /**
     * 新闻信息
     * @param $newsId
     * @return \Illuminate\Http\JsonResponse
     */
    public function info($newsId)
    {
        $newsInfo = News::query()->with([
            'cate:id,name', 'tagListInfo'
        ])->find($newsId);
        $newsInfo->time = Carbon::parse($newsInfo->created_at)->format('Y-m-d');

        // 上
        $newsInfo->prev = News::query()
            ->where('is_hide', 0)
            ->where('id', '<', $newsId)
            ->orderByDesc('id')
            ->first(['id', 'title']);

        $newsInfo->next = News::query()
            ->where('is_hide', 0)
            ->where('id', '>', $newsId)
            ->orderBy('id')
            ->first(['id', 'title']);

        return $this->responseSuccess($newsInfo);
    }

    /**
     * 分类映射
     * @return \Illuminate\Http\JsonResponse
     */
    public function cate()
    {
        $lists = NewCategory::query()
            ->orderBy('sort')
            ->orderBy('created_at')
            ->get(['id', 'name'])->toArray();

        array_unshift($lists, [
            'id' => 0,
            'name' => '全部新闻'
        ]);

        return $this->responseSuccess($lists);
    }
}
