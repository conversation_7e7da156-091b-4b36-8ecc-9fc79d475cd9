<?php

namespace App\Http\Controllers\Api;

use App\Models\Config;
use App\Http\Controllers\Controller;

class ConfigController extends Controller
{
    public function info()
    {

        $newData = [];
        Config::query()
            ->whereIn('key', [
                Config::KeyMappings['about'],
                Config::KeyMappings['question'],
                Config::KeyMappings['service'],
                Config::KeyMappings['private'],
                Config::KeyMappings['upload_statement'],
                Config::KeyMappings['copyright_statement'],
                Config::KeyMappings['download_statement'],
            ])
            ->get()->map(function ($value) use (&$newData) {
                $newData[$value->key] = $value->value;
            });
        return $this->responseSuccess($newData);
    }

    /**
     * 关于我们
     * @return \Illuminate\Http\JsonResponse
     */
    public function about()
    {
        $about = Config::query()->where('key', Config::KeyMappings['about'])->value('value');
        return $this->responseSuccess($about);
    }

    /**
     * 注册协议
     * @return \Illuminate\Http\JsonResponse
     */
    public function register()
    {
        $register = Config::query()->where('key', Config::KeyMappings['register'])->value('value');
        return $this->responseSuccess($register);
    }

    /**
     * 常见问题
     * @return \Illuminate\Http\JsonResponse
     */
    public function question()
    {
        $question = Config::query()->where('key', Config::KeyMappings['question'])->value('value');
        return $this->responseSuccess($question);
    }

    /**
     * 常见问题
     * @return \Illuminate\Http\JsonResponse
     */
    public function service()
    {
        $service = Config::query()->where('key', Config::KeyMappings['service'])->value('value');
        return $this->responseSuccess($service);
    }

    /**
     * 常见问题
     * @return \Illuminate\Http\JsonResponse
     */
    public function private()
    {
        $private = Config::query()->where('key', Config::KeyMappings['private'])->value('value');
        return $this->responseSuccess($private);
    }

    /**
     * 下载声明
     * @return \Illuminate\Http\JsonResponse
     */
    public function downloadStatement()
    {
        $value = Config::query()->where('key', Config::KeyMappings['download_statement'])->value('value');
        return $this->responseSuccess($value);
    }
}
