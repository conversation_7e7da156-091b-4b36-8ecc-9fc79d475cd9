<?php

namespace App\Http\Controllers\Api;

use Illuminate\Http\Request;
use App\Models\Certification;
use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Api\UserCertification;
use Medz\IdentityCard\China\Identity;

class CertificationController extends Controller
{
    /**
     * 用户认证详情.
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function show(Request $request)
    {
        $uid = $request->user('api')->id;

        $certification = Certification::query()->where('user_id', $uid)->first();

        if (!$certification) {
            throw new ApiException('未提交认证', ResponseCode::NOT_FOUND);
        }

        return $this->responseSuccess($certification);
    }

    /**
     * 用户认证
     *
     * @param UserCertification $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function store(UserCertification $request)
    {
        $uid = $request->user('api')->id;
        $number = $request->input('number');

        $certification = Certification::query()->where('user_id', $uid)->first();
        if ($certification) {
            throw new ApiException('请勿重复提交认证', ResponseCode::FORBIDDEN);
        }

        $identity = new Identity($number);
        if (!$identity->legal()) {
            throw new ApiException('证件号格式错误', ResponseCode::FORBIDDEN);
        }

        $certification = Certification::query()->where('number', $number)->where('status',1)->first();
        if ($certification) {
            throw new ApiException('该证件号已进行认证', ResponseCode::FORBIDDEN);
        }

        $certification = new Certification();
        $certification->user_id = $uid;
        $certification->name = $request->input('name');
        $certification->number = $request->input('number');
        $certification->fpic = $request->input('fpic');
        $certification->bpic = $request->input('bpic');
        $certification->status = 0;
        $certification->reason = null;

        if (!$certification->save()) {
            throw new ApiException('提交失败，请稍后再试', ResponseCode::SERVER_ERR);
        }

        return $this->responseSuccess($certification, '提交成功');
    }

    /**
     * 更新认证信息
     *
     * @param UserCertification $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function update(UserCertification $request)
    {
        $uid = $request->user('api')->id;
        $number = $request->input('number');

        $identity = new Identity($number);
        if (!$identity->legal()) {
            throw new ApiException('证件号格式错误', ResponseCode::FORBIDDEN);
        }

        $certification = Certification::query()->where('user_id', $uid)->first();
        if (!$certification){
            throw new ApiException('该账号还未进行认证', ResponseCode::FORBIDDEN);
        }

        $certification->user_id = $uid;
        $certification->name = $request->input('name');
        $certification->number = $request->input('number');
        $certification->fpic = $request->input('fpic');
        $certification->bpic = $request->input('bpic');
        $certification->status = 0;
        $certification->reason = null;

        if (!$certification->save()) {
            throw new ApiException('提交失败，请稍后再试', ResponseCode::SERVER_ERR);
        }

        return $this->responseSuccess($certification, '提交成功');
    }
}
