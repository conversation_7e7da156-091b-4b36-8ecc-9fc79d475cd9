<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use Laravel\Socialite\Facades\Socialite;

class WechatController extends Controller
{
    /**
     * 处理微信的请求消息
     *
     * @return string
     */
    public function serve()
    {
        $app = app('wechat.official_account');
        $app->server->push(function($message){
            return "欢迎关注 犀光RadiRhino！";
        });

        return $app->server->serve();
    }

    /**
     *
     */
    public function checkSignature()
    {
        $signature = $_GET['signature'];
        $token = 'mashi';
        $timestamp = $_GET['timestamp'];
        $nonce = $_GET['nonce'];
        $echostr = $_GET['echostr'];

        $tmpArr = array($token, $timestamp, $nonce);
        sort($tmpArr);
        $tmpStr = implode('', $tmpArr);
        $tmpStr = sha1( $tmpStr );
        if ($tmpStr == $signature) {
            echo $echostr;
        } else {
            echo '';
        }
    }
}
