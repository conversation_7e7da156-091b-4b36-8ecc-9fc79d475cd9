<?php

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\InvoiceInfo;
use App\Models\InvoiceRecord;
use App\Models\RechargeOrder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;

/**
 * 发票管理控制器
 */
class InvoiceController extends Controller
{
    /**
     * 获取用户发票信息列表
     */
    public function getInvoiceInfos(Request $request)
    {
        $user = $request->user('api');
        
        $invoiceInfos = InvoiceInfo::query()
            ->where('user_id', $user->id)
            ->orderByDesc('is_default')
            ->orderByDesc('id')
            ->get();

        return $this->responseSuccess($invoiceInfos, '获取成功');
    }

    /**
     * 创建发票信息
     */
    public function createInvoiceInfo(Request $request)
    {
        $user = $request->user('api');
        
        $invoiceType = $request->input('invoice_type', 1);
        $headerType = $request->input('header_type', 1);
        
        $validator = Validator::make($request->all(), 
            InvoiceInfo::getValidationRules($invoiceType, $headerType),
            InvoiceInfo::getValidationMessages()
        );

        if ($validator->fails()) {
            throw new ApiException($validator->errors()->first(), ResponseCode::PARAM_ERR);
        }

        try {
            DB::beginTransaction();

            $data = $request->only([
                'invoice_type', 'invoice_content', 'header_type', 'header_name',
                'tax_number', 'registered_address', 'registered_phone', 
                'bank_name', 'bank_account'
            ]);
            $data['user_id'] = $user->id;
            
            // 如果设置为默认，取消其他默认设置
            $isDefault = $request->input('is_default', 0);
            if ($isDefault) {
                InvoiceInfo::where('user_id', $user->id)
                    ->update(['is_default' => 0]);
                $data['is_default'] = 1;
            }

            $invoiceInfo = InvoiceInfo::create($data);

            DB::commit();

            return $this->responseSuccess($invoiceInfo, '创建成功');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('创建发票信息失败', ['error' => $e->getMessage(), 'user_id' => $user->id]);
            throw new ApiException('创建失败', ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 更新发票信息
     */
    public function updateInvoiceInfo(Request $request, $id)
    {
        $user = $request->user('api');
        
        $invoiceInfo = InvoiceInfo::where('id', $id)
            ->where('user_id', $user->id)
            ->first();
            
        if (!$invoiceInfo) {
            throw new ApiException('发票信息不存在', ResponseCode::NOT_FOUND);
        }

        $invoiceType = $request->input('invoice_type', $invoiceInfo->invoice_type);
        $headerType = $request->input('header_type', $invoiceInfo->header_type);
        
        $validator = Validator::make($request->all(), 
            InvoiceInfo::getValidationRules($invoiceType, $headerType),
            InvoiceInfo::getValidationMessages()
        );

        if ($validator->fails()) {
            throw new ApiException($validator->errors()->first(), ResponseCode::PARAM_ERR);
        }

        try {
            DB::beginTransaction();

            $data = $request->only([
                'invoice_type', 'invoice_content', 'header_type', 'header_name',
                'tax_number', 'registered_address', 'registered_phone', 
                'bank_name', 'bank_account'
            ]);
            
            // 如果设置为默认，取消其他默认设置
            $isDefault = $request->input('is_default', 0);
            if ($isDefault) {
                InvoiceInfo::where('user_id', $user->id)
                    ->where('id', '!=', $id)
                    ->update(['is_default' => 0]);
                $data['is_default'] = 1;
            }

            $invoiceInfo->update($data);

            DB::commit();

            return $this->responseSuccess($invoiceInfo, '更新成功');
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('更新发票信息失败', ['error' => $e->getMessage(), 'user_id' => $user->id, 'id' => $id]);
            throw new ApiException('更新失败', ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 删除发票信息
     */
    public function deleteInvoiceInfo(Request $request, $id)
    {
        $user = $request->user('api');
        
        $invoiceInfo = InvoiceInfo::where('id', $id)
            ->where('user_id', $user->id)
            ->first();
            
        if (!$invoiceInfo) {
            throw new ApiException('发票信息不存在', ResponseCode::NOT_FOUND);
        }

        // 检查是否有关联的开票记录
        $hasRecords = InvoiceRecord::where('invoice_info_id', $id)->exists();
        if ($hasRecords) {
            throw new ApiException('该发票信息已有开票记录，无法删除', ResponseCode::PARAM_ERR);
        }

        try {
            $invoiceInfo->delete();
            return $this->responseSuccess(null, '删除成功');
        } catch (\Exception $e) {
            Log::error('删除发票信息失败', ['error' => $e->getMessage(), 'user_id' => $user->id, 'id' => $id]);
            throw new ApiException('删除失败', ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 获取可开票的充值订单
     */
    public function getRechargeOrders(Request $request)
    {
        $user = $request->user('api');
        $limit = $request->input('limit', 15);

        $orders = RechargeOrder::query()
            ->where('user_id', $user->id)
            ->where('status', RechargeOrder::StatusMappings['paid']) // 只显示已支付的订单
            ->with(['latestInvoiceRecord.invoiceInfo'])
            ->orderByDesc('id')
            ->paginate($limit);

        return $this->responseSuccess($orders, '获取成功');
    }

    /**
     * 申请开票
     */
    public function applyInvoice(Request $request)
    {
        $user = $request->user('api');

        $validator = Validator::make($request->all(),
            InvoiceRecord::getValidationRules(),
            InvoiceRecord::getValidationMessages()
        );

        if ($validator->fails()) {
            throw new ApiException($validator->errors()->first(), ResponseCode::PARAM_ERR);
        }

        $rechargeOrderId = $request->input('recharge_order_id');
        $invoiceInfoId = $request->input('invoice_info_id');
        $invoiceAmount = $request->input('invoice_amount');
        $remark = $request->input('remark', '');

        try {
            DB::beginTransaction();

            // 验证充值订单
            $rechargeOrder = RechargeOrder::where('id', $rechargeOrderId)
                ->where('user_id', $user->id)
                ->where('status', RechargeOrder::StatusMappings['paid'])
                ->first();

            if (!$rechargeOrder) {
                throw new ApiException('充值订单不存在或未支付', ResponseCode::PARAM_ERR);
            }

            // 验证发票信息
            $invoiceInfo = InvoiceInfo::where('id', $invoiceInfoId)
                ->where('user_id', $user->id)
                ->first();

            if (!$invoiceInfo) {
                throw new ApiException('发票信息不存在', ResponseCode::PARAM_ERR);
            }

            // 验证开票金额不能超过订单金额
            if ($invoiceAmount > $rechargeOrder->order_amount) {
                throw new ApiException('开票金额不能超过订单金额', ResponseCode::PARAM_ERR);
            }

            // 检查是否有待处理的开票申请
            $pendingRecord = InvoiceRecord::where('recharge_order_id', $rechargeOrderId)
                ->whereIn('status', [InvoiceRecord::StatusMappings['pending'], InvoiceRecord::StatusMappings['reopen']])
                ->first();

            if ($pendingRecord) {
                throw new ApiException('该订单已有待处理的开票申请', ResponseCode::PARAM_ERR);
            }

            // 创建开票记录
            $invoiceRecord = InvoiceRecord::create([
                'user_id' => $user->id,
                'recharge_order_id' => $rechargeOrderId,
                'invoice_info_id' => $invoiceInfoId,
                'invoice_amount' => $invoiceAmount,
                'status' => InvoiceRecord::StatusMappings['pending'],
                'apply_time' => now(),
                'remark' => $remark,
            ]);

            // 更新充值订单开票状态
            $rechargeOrder->increment('invoice_count');
            $rechargeOrder->update(['invoice_status' => RechargeOrder::InvoiceStatusMappings['applied']]);

            DB::commit();

            return $this->responseSuccess($invoiceRecord->load(['invoiceInfo', 'rechargeOrder']), '申请成功');
        } catch (ApiException $e) {
            DB::rollBack();
            throw $e;
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('申请开票失败', ['error' => $e->getMessage(), 'user_id' => $user->id]);
            throw new ApiException('申请失败', ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 修改开票申请
     */
    public function modifyInvoice(Request $request, $id)
    {
        $user = $request->user('api');

        $invoiceRecord = InvoiceRecord::where('id', $id)
            ->where('user_id', $user->id)
            ->first();

        if (!$invoiceRecord) {
            throw new ApiException('开票记录不存在', ResponseCode::NOT_FOUND);
        }

        // 只有待处理状态才能修改
        if ($invoiceRecord->status != InvoiceRecord::StatusMappings['pending']) {
            throw new ApiException('当前状态不允许修改', ResponseCode::PARAM_ERR);
        }

        $validator = Validator::make($request->all(),
            InvoiceRecord::getValidationRules(),
            InvoiceRecord::getValidationMessages()
        );

        if ($validator->fails()) {
            throw new ApiException($validator->errors()->first(), ResponseCode::PARAM_ERR);
        }

        try {
            $invoiceInfoId = $request->input('invoice_info_id');
            $invoiceAmount = $request->input('invoice_amount');
            $remark = $request->input('remark', '');

            // 验证发票信息
            $invoiceInfo = InvoiceInfo::where('id', $invoiceInfoId)
                ->where('user_id', $user->id)
                ->first();

            if (!$invoiceInfo) {
                throw new ApiException('发票信息不存在', ResponseCode::PARAM_ERR);
            }

            // 验证开票金额
            if ($invoiceAmount > $invoiceRecord->rechargeOrder->order_amount) {
                throw new ApiException('开票金额不能超过订单金额', ResponseCode::PARAM_ERR);
            }

            $invoiceRecord->update([
                'invoice_info_id' => $invoiceInfoId,
                'invoice_amount' => $invoiceAmount,
                'remark' => $remark,
            ]);

            return $this->responseSuccess($invoiceRecord->load(['invoiceInfo', 'rechargeOrder']), '修改成功');
        } catch (ApiException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('修改开票申请失败', ['error' => $e->getMessage(), 'user_id' => $user->id, 'id' => $id]);
            throw new ApiException('修改失败', ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 获取开票记录
     */
    public function getInvoiceRecords(Request $request)
    {
        $user = $request->user('api');
        $limit = $request->input('limit', 15);
        $status = $request->input('status');

        $query = InvoiceRecord::query()
            ->where('user_id', $user->id)
            ->with(['invoiceInfo', 'rechargeOrder']);

        if ($status !== null && $status !== '') {
            $query->where('status', $status);
        }

        $records = $query->orderByDesc('id')->paginate($limit);

        return $this->responseSuccess($records, '获取成功');
    }
}
}
