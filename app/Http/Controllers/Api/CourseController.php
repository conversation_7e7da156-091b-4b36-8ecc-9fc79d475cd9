<?php

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\Base;
use App\Models\Comment;
use App\Models\Config;
use App\Models\Course;
use App\Models\CourseCategory;
use App\Models\CourseDetail;
use App\Models\CourseLevel;
use App\Models\User;
use App\Models\UserCourse;
use App\Models\UserFans;
use App\Models\Watch;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CourseController extends Controller
{

    public function getchildIds($id)
    {
        $ids = CourseCategory::query()->when($id, function ($q) use ($id){
            if(is_array($id)){
                return $q -> whereIn('parent_id', $id);
            } else {
                return $q -> where('parent_id', $id);
            }
        }) -> pluck('id')->toArray();
        if(count($ids)){
            return array_merge($ids, $this -> getchildIds($ids));
        } else {
            return $ids;
        }
    }
    /**
     * 课程列表
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $limit = $request->input('limit', 20);
        $offset = $request->input('offset', 0);
        $cateId = $request->input('cate_id');
        $level = $request->input('level');
        $sort = $request->input('sort');
        $cateIds = $this->getchildIds($cateId);
        $courses = Course::query()
            ->where(function ($query) use ($cateId, $cateIds) {
                $query->where('cate_id', $cateId) -> orWhereIn('cate_id', $cateIds);
            })
            ->where('is_delete', Course::NOT_DELETE)
            ->where('is_publish', 1)
            ->limit($limit)
            ->offset($offset * $limit)
            ->when($sort!=null, function ($query) use ($sort){
                if ($sort == 0) {
                    $query->orderByDesc('updated_at');
                } else if ($sort == 1) {
                    $query->orderByDesc('sort')
                    ->orderByDesc('id');
                } else {
                    $query->orderByDesc('sort')
                        ->orderByDesc('id');
                }
            })
            ->get();

        foreach ($courses as &$item) {
            $item->images = handle_url($item->images);
            $item->score = '8';
        }

        $cate_name = '';
        if ($cateId) {
            $cate_name = CourseCategory::query()->where('id', $cateId)->value('name');
        }

        $total = Course::query()
            ->where(function ($query) use ($cateId, $cateIds) {
                $query->where('cate_id', $cateId) -> orWhereIn('cate_id', $cateIds);
            })
            ->where('is_delete', Course::NOT_DELETE)
            ->where('is_publish', 1)
            ->count();

        return $this->responseSuccess(compact('courses', 'total', 'cate_name'));
    }

    /**
     * 课程列表
     * @return \Illuminate\Http\JsonResponse
     */
    public function list()
    {
        $items = CourseCategory::query()
            ->whereHas('course')
            ->selectRaw('id,name,sort')
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->get();

        foreach ($items as $item) {
            $item->source = Course::query()
                ->where('is_delete', Course::NOT_DELETE)
                ->where('cate_id', $item->id)
                ->limit(6)
                ->selectRaw('id,name,images,created_at,author,price,market_price')
                ->notDelete()
                ->get()
                ->map(function ($value) {
                    $value->images = $value->images_src;
                    $value->name = substr_with_powerful($value->name, 30);
                    $value->date = Carbon::parse($value->created_at)->format('Y/m/d');
                    $value->makeHidden(['cate', 'admin', 'is_annex', 'created_at', 'images_src', 'purchase', 'collect']);
                    return $value;
                })
                ->toArray();
        }
        return $this->responseSuccess($items);
    }

    /**
     * 课程详情信息
     * @param Request $request
     * @param $course_id
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function detail(Request $request, $course_id)
    {
        $user = $request->user('api');
        $task_id = $request->input('task_id');
        $userId = $user ? $user->id : 0;
        Log::info('is_mobile：', [is_mobile(), $userId, $course_id, $task_id, $_SERVER['HTTP_USER_AGENT']]);
        $course = Course::query()
            ->with(['authorInfo', 'suggestUser', 'tagListInfo'])
            ->where('id', $course_id)
            ->where('is_delete', Course::NOT_DELETE)
            ->where('is_publish', 1)
            ->first();

        //推荐老师 随机排序
        $teachers = User::query()
            ->whereHas('courses')
            ->limit(2)
            ->orderBy(DB::raw('RAND()'))
            ->get();
        if ($teachers) {
            foreach ($teachers as $item) {
                $item['nofans'] = UserFans::getFansStatus($user, $item['id']);
            }
        }

        if (!$course) {
            throw new ApiException('课程信息不存在', ResponseCode::NOT_FOUND);
        }

        // 获取广告
        $config_ad = Config::getValueByKey(Config::KeyMappings['ad']);
        if ($config_ad && isset($config_ad['file'])) {
            $config_ad['file'] = $config_ad['file'] ? handle_url($config_ad['file']) : '';
            $course->ad = $config_ad;
        }

        $details = CourseDetail::query()
            ->with(['task', 'annex'])
            ->where('course_id', $course_id)
            ->where('state',CourseDetail::SHOW)
            ->where('type', CourseDetail::TypeValueMappings['chapter'])
            ->orderBy('sort')
            ->orderBy('name')
            ->orderBy('created_at')
            ->get();

        $isFirst = true;
        $task = null;
        foreach ($details as $key => &$item) {
            foreach ($item->task as &$value) {
                $value->images = handle_url($value->images);

                if (is_wechat()) {
                    $value->link = handle_url($value->link);
                } else {
                    $value->link = is_mobile() ? '' : handle_url($value->link);
                }

                // 第一个课件
                $value->is_first = 0;
                if ($isFirst) {
                    $value->is_first = 1;
                    $isFirst = false;

                    // 默认第一个
                    $task = $value;
                } else {
                    if (!$course->purchase && $course->price > 0 || $value->lock_status!=0 || $item->lock_status!=0) {
                        $value->link = '';
                    }
                }

                if ($task_id == $value->id) {
                    $task = $value;
                }

                //  前端章节排序值
                $value->cpt_id = $key;
            }


            foreach ($item->annex as &$annex) {
                $annex->link = handle_url($annex->link);
                if (!$course->purchase && $course->price > 0) {
                    $annex->link = '';
                }
            }
        }

        $course->view_num = $details->sum('views');
        $course->nofans = UserFans::getFansStatus($user, $course['user_id']);

//        $course->;

        $course->makeHidden(['admin_id', 'cate_id', 'end_time', 'images', 'is_delete', 'is_publish', 'publish_time', 'sort', 'updated_at']);
        $details->makeHidden([
            'duration', 'admin_id', 'images', 'sort', 'state', 'type', 'chapter_id',
            'views', 'updated_at', 'course_id', 'id', 'is_free', 'link'
        ]);

        return $this->responseSuccess(compact('course', 'details', 'task', 'user', 'teachers'));
    }

    /**
     * 课程评论列表
     * @param Request $request
     * @param $course_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function comment(Request $request, $course_id)
    {
        $limit = $request->input('limit');
        $offset = $request->input('offset');

        $comments = Comment::query()
            ->with('reply')
            ->where('is_delete', Base::NOT_DELETE)
            ->where('target_id', $course_id)
            ->where('target_type', Comment::TypeMappings['task'])
            ->orderByDesc('is_top')
            ->orderByDesc('created_at')
            ->limit($limit)
            ->offset($offset)
            ->get();

        return $this->responseSuccess($comments);
    }

    /**
     * 课程分类映射
     * @return \Illuminate\Http\JsonResponse
     */
    public function cate(Request $request)
    {
        $parentId = $request->parent_id;

        $courseMap = CourseCategory::query()
//            ->with('childrenCategory:id,name,parent_id')
            ->with(['childrenCategory' => function($q){
                return $q -> with(['childrenCategory' => function($q){
                        return $q -> with(['childrenCategory'])->orderByDesc('sort');
                }])->orderByDesc('sort');
            }])
            ->where(function ($query) use ($parentId) {
                if ($parentId > 0) {
                    $query->where('parent_id', $parentId);
                } else {
                    $query->where('parent_id', 0);
                }
            })
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->get(['id', 'name', 'parent_id'])
            ->toArray();

        array_unshift($courseMap, [
            'id' => 0,
            'level' => empty($parentId) > 0 ? 1 : 2,
            'name' => '全部',
            'parent_id' => empty($parentId) ? 0 : $parentId,
            'children_category' => []
        ]);

        return $this->responseSuccess($courseMap);
    }

    /**
     * 分类信息
     * @return \Illuminate\Http\JsonResponse
     */
    public function cateInfo($cate_id)
    {
        $courseMap = CourseCategory::query()
            ->where('id', $cate_id)
            ->first();

        return $this->responseSuccess($courseMap);
    }

    /**
     * 课程层级
     * @return \Illuminate\Http\JsonResponse
     */
    public function level(Request $request)
    {
        $cateId = $request->input('cate_id');

        $levels = [];
        if($cateId){
            $levels = Course::query()
                ->where('is_delete', Base::NOT_DELETE)
                ->where('cate_id', $cateId)
                ->pluck('level')
                ->toArray();
        }

        $mappings = CourseLevel::query()
            ->when($levels,function ($query) use ($levels) {
                $query->whereIn('id', $levels);
            })
            ->get()
            ->map(function ($data) {
                return [
                    'level' => $data->id,
                    'name' => $data->title,
                ];
            })->toArray();
        return $this->responseSuccess($mappings);
    }

    /**
     * 获取课程上一条、下一条信息
     * @param $course_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function prevAndNext($course_id)
    {
        $course = new Course();
        $prev = $course->getPrevId($course_id);
        $next = $course->getNextId($course_id);
        $courses = Course::query()
            ->where('is_delete', Base::NOT_DELETE)
            ->whereIn('id', [$prev, $next])->orderBy('id')->get();
        foreach ($courses as &$item) {
            $item->images = handle_url($item->images);
        }

        $ids = [
            'prev_id' => $prev,
            'next_id' => $next,
        ];
        return $this->responseSuccess(compact('courses', 'ids'));
    }

    /**
     * 课程指定视频 观看次数
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function view(Request $request)
    {
        $course_detail_Id = $request->input('course_detail_id');
        $detail = CourseDetail::query()->where('id', $course_detail_Id)->first();
        $detail->views += 1;
        $detail->save();
        return $this->responseSuccess();
    }

    /**
     * 视频观看进度
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function progress(Request $request)
    {
        $user = $request->user('api');
        $courseId = $request->input('course_id');
        $courseDetailId = $request->input('course_detail_id');
        $time = $request->input('time');
        $duration = $request->input('duration', 0); # 视频总时长

        if (!$courseId || !$courseDetailId || !$time) {
            throw new ApiException('参数错误', ResponseCode::PARAM_ERR);
        }

        $userCourse = UserCourse::query()
            ->where('course_id', $courseId)
            ->where('user_id', $user->id)
            ->first();
        if (!$userCourse) {
            throw new ApiException('不在学习课程中', ResponseCode::FORBIDDEN);
        }

        $watch = Watch::query()
            ->where('course_id', $courseId)
            ->where('course_detail_id', $courseDetailId)
            ->where('user_id', $user->id)
            ->first();
        if (!$watch) {
            $watch = new Watch();
            $watch->course_id = $courseId;
            $watch->course_detail_id = $courseDetailId;
            $watch->user_id = $user->id;
            $watch->status = Watch::StatusMappings['looking'];
            $watch->last_time_out = 0;
            $watch->save();
        }

//        $duration = CourseDetail::query()->where('id', $courseDetailId)->value('duration');
        if ($time >= ($duration - 120) && $watch->status == Watch::StatusMappings['looking']) {
            $watch->status = Watch::StatusMappings['finish'];
        }

        $watch->last_time_out = $time;
        if (!$watch->save()) {
            throw new ApiException('出错了', ResponseCode::SERVER_ERR);
        }

        return $this->responseSuccess('成功');
    }
}
