<?php

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\Request;

class EmailController extends Controller
{
    /**
     * 邮箱验证
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function verify(Request $request)
    {
        $email = $request->input('email');
        $token = $request->input('_token');
        $timestamp = $request->input('timestamp');

        if (!$email) {
            throw new ApiException('邮箱不能为空', ResponseCode::PARAM_ERR);
        }

        if (time() - $timestamp > 10 * 60 * 60) {
            throw new ApiException('验证链接已失效', ResponseCode::FORBIDDEN);
        }

        if (!$token) {
            throw new ApiException('验证失败', ResponseCode::FORBIDDEN);
        }

        $user = User::query()->where('api_token', $token)->first();
        $user->email = $email;
        $user->email_verified_at = now();
        if (!$user->save()) {
            throw new ApiException('邮箱验证失败', ResponseCode::SERVER_ERR);
        }

        return $this->responseSuccess(null, '邮箱验证成功');
    }
}
