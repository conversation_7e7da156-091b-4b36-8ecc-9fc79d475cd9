<?php

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Jobs\AutoCloseOrderJob;
use App\Jobs\ShenzaoAuthorize;
use App\Models\CouponCode;
use App\Models\Course;
use App\Models\Goods;
use App\Models\Order;
use App\Models\OrderConsignee;
use App\Models\Plug;
use App\Models\UserAddress;
use App\Models\UserCourse;
use App\Models\Wallet;
use App\Models\WalletLog;
use App\Services\IntegralService;
use App\Services\OrderService;
use App\Services\PaymentService;
use App\Services\DiscountService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class newOrderController extends Controller
{
    /**
     * 订单列表
     * @param Request $request
     */
    public function index(Request $request)
    {
        // 就用旧的
    }

    /**
     * 订单详情
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function orderInfo(Request $request)
    {
        $userId  = $request->user('api')->id;
        $orderNo = $request->input('order_no');

        $order = Order::query()->where('user_id', $userId)->where('order_no', $orderNo)->first();
        if (!$order) {
            throw new ApiException('订单信息不存在！', ResponseCode::NOT_FOUND);
        }

        $order->load('consignee');
        $order->product_img = handle_url($order->product_img);
        return $this->responseSuccess($order);
    }

    /**
     * 获取预处理订单信息
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function preOrderInfo(Request $request)
    {
        $userId = $request->user('api')->id;
        $extendType = $request->input('product_extend_type', '');
        $productType = $request->input('product_type');
        $productId = $request->input('product_id');
        if (!$productId || !in_array($productType, [1, 2, 3])) {
            throw new ApiException('产品信息错误', ResponseCode::PARAM_ERR);
        }
        $num = 1;
        $productInfo = $this->getProductInfo($productType, $productId);
        if ($productType == 2 && $extendType == 'web_link') {
            // 使用插件Web链接折扣价格
            $price = bcmul($num, $productInfo->web_link_discount_price, 2);
            $postage = 0;
            $productName = $productInfo->name . ' - 功能介绍';
        } else {
            // 使用模型的折扣价格
            $price = bcmul($num, $productInfo->discount_price, 2);
            // 邮费价格
            $postage = $productInfo->postage ?? 0;
            $productName = $productInfo->name;
        }

        // 支付总额
        $totalAmount = $orderAmount = bcadd($postage, $price, 2);

        // 产品图
        $productImg = is_array($productInfo->images) ? $productInfo->images[0] : $productInfo->images;

        $preOrderInfo = [
            'user_id' => $userId,
            'product_type' => $productType,
            'product_id' => $productId,
            'num' => $num,
            'product_name' => $productName,
            'order_amount' => $orderAmount,
            'total_amount' => $totalAmount,
            'product_img' => handle_url($productImg),
            'postage' => $postage,
        ];

        return $this->responseSuccess($preOrderInfo);
    }


    /**
     * 下单
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function createOrder(Request $request)
    {
        $productType = $request->input('product_type');
        $productId = $request->input('product_id');

        if (!$productId || !in_array($productType, [1, 2, 3])) {
            throw new ApiException('产品信息错误', ResponseCode::PARAM_ERR);
        }
        switch ($productType) {
            case '1':
                $order = $this->courseOrder($request->all());
                break;
            case '2':
                $order = $this->plugOrder($request->all());
                break;
            case '3':
                $order = $this->goodsOrder($request->all());
                break;
            default:
                throw new ApiException('产品信息错误', ResponseCode::PARAM_ERR);
        }

        return $this->responseSuccess($order, '购买成功');
    }

    /**
     * 获取产品信息
     * @param $productType
     * @param $productId
     * @return Builder|Builder[]|\Illuminate\Database\Eloquent\Collection|Model|null
     */
    public function getProductInfo($productType, $productId)
    {
        switch ($productType) {
            case '1':
                return Course::query()
                    ->where('is_delete', Course::NOT_DELETE)
                    ->find($productId);
            case '2':
                return Plug::query()
                    ->where('state', 1)
                    ->find($productId);
            case '3':
                return Goods::query()
                    ->where('is_delete', Goods::NOT_DELETE)
                    ->where('status', 1)
                    ->find($productId);
            default:
                return null;
        }

    }

    /**
     * 课程下单
     * @param $data
     * @return Builder|Model
     * @throws ApiException
     */
    public function courseOrder($data)
    {
        $user = \request()->user('api');
        $userId = $user->id;
        $productType = $data['product_type'];
        $productId = $data['product_id'];
        $num = $data['num'] ?? 1;
        $addressId = $data['address_id'] ?? 0;
        $couponCode = $data['coupon_code'] ?? '';

        if (!$course = Course::query()->where('is_delete', Course::NOT_DELETE)->find($productId)) {
            throw new ApiException('课程信息不存在', ResponseCode::NOT_FOUND);
        }
        if ($addressId) {
            $user_address = UserAddress::query()->where('id', $addressId)->where('user_id', $userId)->first();
            if (!$user_address) {
                throw new ApiException('用户地址不存在！', ResponseCode::FORBIDDEN);
            }
        }

        // 验证用户课程
        $userCourse = UserCourse::query()
            ->where('course_id', $productId)
            ->where('user_id', $userId)
            ->where('type', UserCourse::TypeMappings['learn'])
            ->first();
        if ($userCourse) {
            throw new ApiException('已在学习课程中', ResponseCode::FORBIDDEN);
        }

        // 订单价格（使用模型的折扣价格）
        $totalAmount = $orderAmount = bcmul($num, $course->discount_price, 2);

        // 优惠码抵扣
        if ($couponCode) {
            $couponPrice = CouponCode::getPrice($couponCode, $userId, $productType, $productId);
            $orderAmount = bcsub($orderAmount, $couponPrice, 2);
        }
        // 获取用余额光子余额
        $wallet = Wallet::init($userId);
        if ($wallet->balance < $orderAmount) {
            throw new ApiException('余额不足', ResponseCode::BALANCE_NOT_ENOUGH);
        }
        try {
            DB::beginTransaction();
            $order = Order::query()->create([
                'user_id' => $userId,
                'order_no' => Order::generateOrderNo(),
                'product_type' => $productType,
                'product_id' => $productId,
                'num' => $num,
                'product_name' => $course->name,
                'order_amount' => $orderAmount,
                'total_amount' => $totalAmount,
                'product_img' => $course->images,
                'status' => Order::StatusMappings['paid'],
                'pay_time' => now()->toDateTimeString(),
                'postage' => 0,
            ]);
            // 发放课程
            UserCourse::query()->create([
                'user_id' => $userId,
                'course_id' => $course->id,
                'type' => UserCourse::TypeMappings['learn'],
            ]);
            // 课程购买数量维护
            Course::query()->where('id', $productId)->increment('num');
            //写入订单发货地址
            if (isset($user_address) && $user_address) {
                OrderConsignee::query()->create([
                    'user_id' => $userId,
                    'order_id' => $order->id,
                    'name' => $user_address->name,
                    'phone' => $user_address->phone,
                    'address' => $user_address->address,
                ]);
            }
            // 使用优惠码
            if ($couponCode) {
                CouponCode::use($couponCode, $userId);
            }
            // 扣减光子余额
            $currBalance = bcsub($wallet->balance, $orderAmount, 2);
            $wallet->update([
                'balance' => DB::raw('balance - ' . $orderAmount),
                'expend' => DB::raw('expend + ' . $orderAmount),
            ]);
            // 支付流水
            WalletLog::query()->create([
                'title' => '购买-' . $course->name,
                'target_type' => WalletLog::TargetTypeMappings['order'],
                'target_id' => $order->id,
                'amount' => $orderAmount,
                'user_id' => $userId,
                'currency' => 'integral',
                'action' => WalletLog::ActionMappings['expend'],
                'status' => WalletLog::StatusMappings['success'],
                'type' => WalletLog::TypeMappings['course'],
                'extend' => ['balance' => $currBalance],
            ]);

            // 如果下单目标存在推荐人，则将光子金额存入推荐人账户
            if ($course->user_id) {
                $suggestWallet = Wallet::init($course->user_id);
                $currSuggestBalance = bcsub($suggestWallet->balance, $orderAmount, 2);
                $suggestWallet->update([
                    'balance' => DB::raw('balance + ' . $orderAmount),
                    'income' => DB::raw('income + ' . $orderAmount),
                ]);
                WalletLog::query()->create([
                    'title' => '购买-' . $order->product_name,
                    'target_type' => WalletLog::TargetTypeMappings['order'],
                    'target_id' => $order->id,
                    'amount' => $orderAmount,
                    'user_id' => $course->user_id,
                    'currency' => 'integral',
                    'action' => WalletLog::ActionMappings['income'],
                    'status' => WalletLog::StatusMappings['success'],
                    'type' => WalletLog::TypeMappings['sale'],
                    'extend' => ['balance' => $currSuggestBalance],
                ]);
            }
            DB::commit();
            // 课程授权深造
            if ($course->sz_course_id) {
                PaymentService::getInstance()->smsPublish($order, $course->sz_course_id);
            }
        } catch (\Exception $exception) {
            DB::rollBack();
            logger()->error($exception);
            throw new ApiException($exception->getMessage(), ResponseCode::SERVER_ERR);
        }

        return $order;
    }

    /**
     * 插件下单
     * @param $data
     * @return Builder|Model
     * @throws ApiException
     */
    public function plugOrder($data)
    {
        $user = \request()->user('api');
        $userId = $user->id;
        $extendType = $data['product_extend_type'] ?? '';
        $productType = $data['product_type'];
        $productId = $data['product_id'];
        $num = $data['num'] ?? 1;
        $couponCode = $data['coupon_code'] ?? '';

        if (!$plug = Plug::query()->where('state', 1)->find($productId)) {
            throw new ApiException('插件不存在', ResponseCode::NOT_FOUND);
        }

        // 验证支付类型（使用模型的折扣价格）
        if ($productType == 2 && $extendType == 'web_link') {
            $totalAmount = $orderAmount = bcmul($num, $plug->web_link_discount_price, 2);
            $title = $plug->name . ' - 功能介绍';
        } else {
            // 订单价格
            $totalAmount = $orderAmount = bcmul($num, $plug->discount_price, 2);
            $title = $plug->name;
        }

        // 优惠码抵扣
        if ($couponCode) {
            $couponPrice = CouponCode::getPrice($couponCode, $userId, $productType, $productId);
            $orderAmount = bcsub($orderAmount, $couponPrice, 2);
        }
        // 获取用余额光子余额
        $wallet = Wallet::init($userId);
        if ($wallet->balance < $orderAmount) {
            throw new ApiException('余额不足', ResponseCode::BALANCE_NOT_ENOUGH);
        }

        try {
            DB::beginTransaction();
            $orderData = [
                'user_id' => $userId,
                'order_no' => Order::generateOrderNo(),
                'product_type' => $productType,
                'product_id' => $productId,
                'num' => $num,
                'product_name' => $title,
                'order_amount' => $orderAmount,
                'total_amount' => $totalAmount,
                'product_img' => $plug->images,
                'status' => Order::StatusMappings['paid'],
                'pay_time' => now()->toDateTimeString(),
            ];
            if ($productType == 2 && $extendType == 'web_link') {
                $orderData['extend_info'] = [
                    'product_extend_type' => 'web_link'
                ];
            }
            $order = Order::query()->create($orderData);

            // 使用优惠码
            if ($couponCode) {
                CouponCode::use($couponCode, $userId);
            }
            // 扣减光子余额
            $currBalance = bcsub($wallet->balance, $orderAmount, 2);
            $wallet->update([
                'balance' => DB::raw('balance - ' . $orderAmount),
                'expend' => DB::raw('expend + ' . $orderAmount),
            ]);
            // 支付流水
            WalletLog::query()->create([
                'title' => '购买-' . $order->product_name,
                'target_type' => WalletLog::TargetTypeMappings['order'],
                'target_id' => $order->id,
                'amount' => $orderAmount,
                'user_id' => $userId,
                'currency' => 'integral',
                'action' => WalletLog::ActionMappings['expend'],
                'status' => WalletLog::StatusMappings['success'],
                'type' => WalletLog::TypeMappings['plug'],
                'extend' => ['balance' => $currBalance],
            ]);

            // 如果下单目标存在推荐人，则将光子金额存入推荐人账户
            if ($plug->user_id) {
                $suggestWallet = Wallet::init($plug->user_id);
                $currSuggestBalance = bcsub($suggestWallet->balance, $orderAmount, 2);
                $suggestWallet->update([
                    'balance' => DB::raw('balance + ' . $orderAmount),
                    'income' => DB::raw('income + ' . $orderAmount),
                ]);
                WalletLog::query()->create([
                    'title' => '购买-' . $order->product_name,
                    'target_type' => WalletLog::TargetTypeMappings['order'],
                    'target_id' => $order->id,
                    'amount' => $orderAmount,
                    'user_id' => $plug->user_id,
                    'currency' => 'integral',
                    'action' => WalletLog::ActionMappings['income'],
                    'status' => WalletLog::StatusMappings['success'],
                    'type' => WalletLog::TypeMappings['sale'],
                    'extend' => ['balance' => $currSuggestBalance],
                ]);
            }
            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new ApiException('下单失败', ResponseCode::SERVER_ERR);
        }

        return $order;
    }

    /**
     * 商品下单
     * @param $data
     * @return Builder|Model
     * @throws ApiException
     */
    public function goodsOrder($data)
    {
        $user = \request()->user('api');
        $userId = $user->id;
        $productType = $data['product_type'];
        $productId = $data['product_id'];
        $num = $data['num'] ?? 1;
        $addressId = $data['address_id'] ?? 0;
        $couponCode = $data['coupon_code'] ?? '';

        if (!$goods = Goods::query()->where('is_delete', Goods::NOT_DELETE)->where('status', 1)->find($productId)) {
            throw new ApiException('商品不存在', ResponseCode::NOT_FOUND);
        }

        if (!$addressId) {
            throw new ApiException('请填写选择地址！', ResponseCode::NOT_FOUND);
        }
        $user_address = UserAddress::query()->where('id', $addressId)->where('user_id', $userId)->first();
        if (!$user_address) {
            throw new ApiException('用户地址不存在！', ResponseCode::FORBIDDEN);
        }

        // 订单价格（使用模型的折扣价格）
        $discountedPrice = bcmul($num, $goods->discount_price, 2);

        // 邮费价格
        $postage = $goods->postage;
        // 支付总额
        $totalAmount = $orderAmount = bcadd($postage, $discountedPrice, 2);

        // 优惠码抵扣
        if ($couponCode) {
            $couponPrice = CouponCode::getPrice($couponCode, $userId, $productType, $productId);
            $orderAmount = bcsub($orderAmount, $couponPrice, 2);
        }
        // 获取用余额光子余额
        $wallet = Wallet::init($userId);
        if ($wallet->balance < $orderAmount) {
            throw new ApiException('余额不足', ResponseCode::BALANCE_NOT_ENOUGH);
        }

        try {
            DB::beginTransaction();
            $order = Order::query()->create([
                'user_id' => $userId,
                'order_no' => Order::generateOrderNo(),
                'product_type' => $productType,
                'product_id' => $productId,
                'num' => $num,
                'product_name' => $goods->name,
                'order_amount' => $orderAmount,
                'total_amount' => $totalAmount,
                'product_img' => $goods->cover,
                'postage' => $postage,
                'status' => Order::StatusMappings['paid'],
                'pay_time' => now()->toDateTimeString(),
            ]);

            //写入订单发货地址
            OrderConsignee::query()->create([
                'user_id' => $userId,
                'order_id' => $order->id,
                'name' => $user_address->name,
                'phone' => $user_address->phone,
                'address' => $user_address->address,
            ]);
            // 使用优惠码
            if ($couponCode) {
                CouponCode::use($couponCode, $userId);
            }
            // 扣减光子余额
            $currBalance = bcsub($wallet->balance, $orderAmount, 2);
            $wallet->update([
                'balance' => DB::raw('balance - ' . $orderAmount),
                'expend' => DB::raw('expend + ' . $orderAmount),
            ]);
            // 支付流水
            WalletLog::query()->create([
                'title' => '购买-' . $order->product_name,
                'target_type' => WalletLog::TargetTypeMappings['order'],
                'target_id' => $order->id,
                'amount' => $orderAmount,
                'user_id' => $userId,
                'currency' => 'integral',
                'action' => WalletLog::ActionMappings['expend'],
                'status' => WalletLog::StatusMappings['success'],
                'type' => WalletLog::TypeMappings['goods'],
                'extend' => ['balance' => $currBalance],
            ]);

            // 如果下单目标存在推荐人，则将光子金额存入推荐人账户
            if ($goods->user_id) {
                $suggestWallet = Wallet::init($goods->user_id);
                $currSuggestBalance = bcsub($suggestWallet->balance, $orderAmount, 2);
                $suggestWallet->update([
                    'balance' => DB::raw('balance + ' . $orderAmount),
                    'income' => DB::raw('income + ' . $orderAmount),
                ]);
                WalletLog::query()->create([
                    'title' => '购买-' . $order->product_name,
                    'target_type' => WalletLog::TargetTypeMappings['order'],
                    'target_id' => $order->id,
                    'amount' => $orderAmount,
                    'user_id' => $goods->user_id,
                    'currency' => 'integral',
                    'action' => WalletLog::ActionMappings['income'],
                    'status' => WalletLog::StatusMappings['success'],
                    'type' => WalletLog::TypeMappings['sale'],
                    'extend' => ['balance' => $currSuggestBalance],
                ]);

            }
            DB::commit();
        } catch (ApiException $exception) {
            DB::rollBack();
            throw new ApiException($exception->getMessage(), $exception->getCode());
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new ApiException('下单失败'.$exception->getMessage(), ResponseCode::SERVER_ERR);
        }

        return $order;
    }

}
