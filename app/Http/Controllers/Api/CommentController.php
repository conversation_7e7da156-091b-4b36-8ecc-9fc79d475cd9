<?php

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\Base;
use App\Models\Comment;
use App\Models\CommentReply;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CommentController extends Controller
{
    /**
     * 评论列表
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function index(Request $request)
    {
        $limit = $request->input('limit', 20);
        $offset = $request->input('offset', 0);
        $targetId = $request->input('target_id');
        $targetType = $request->input('target_type', Comment::TypeMappings['task']);

        if (!$targetId) {
           throw new ApiException('参数错误', ResponseCode::PARAM_ERR);
        }

        $comments = Comment::query()
            ->with(['reply:id,comment_id,content,created_at,user_id', 'user:id,name,avatar', 'reply.user:id,name,avatar'])
            ->where('target_id', $targetId)
            ->where('target_type', Comment::TypeMappings[$targetType])
            ->where('is_delete', Base::NOT_DELETE)
            ->orderByDesc('is_top')
            ->orderByDesc('created_at')
            ->select(['content','created_at', 'id', 'target_id', 'target_type', 'user_id'])
            ->limit($limit)
            ->offset($offset * $limit)
            ->get();
        foreach ($comments as $comment){
            if ($comment->user) {
                $comment->user->makeHidden(['is_wechat', 'is_qq', 'is_problem', 'is_pass']);
            }
//            $reply = $comment->reply;
//            if ($reply) {
//                $reply->user->makeHidden(['is_wechat', 'is_qq', 'is_problem', 'is_pass']);
//            }
        }

        $total = Comment::query()
            ->where('target_id', $targetId)
            ->where('target_type', Comment::TypeMappings[$targetType])
            ->where('is_delete', Base::NOT_DELETE)
            ->count();

        return $this->responseSuccess(compact('comments', 'total'));
    }

    /**
     * 创建评论
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function store(Request $request)
    {
        $user = $request->user('api');
        $targetId = (string) $request->input('target_id');
        $targetType = (string) $request->input('target_type', Comment::TypeMappings['task']);
        $content = (string) $request->input('content');

        if (!$targetId || !$content || !in_array($targetType, Comment::TypeMappings)) {
            throw new ApiException('参数错误', ResponseCode::PARAM_ERR);
        }
        if (mb_strlen($content) > 200) {
            throw new ApiException('评论内容最多200个字符', ResponseCode::PARAM_ERR);
        }

        $comment = new Comment();
        $comment->user_id = $user->id;
        $comment->target_id = $targetId;
        $comment->target_type = $targetType;
        $comment->content = substr_with_powerful($content);
        if (!$comment->save()) {
            throw new ApiException('评论失败', ResponseCode::PARAM_ERR);
        }

        $comment->load('user:id,name,avatar');

        return $this->responseSuccess($comment, '评论成功');
    }

    /**
     * 评论回复
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function storeReply(Request $request)
    {
        $user = $request->user('api');
        $commentId = $request->input('comment_id');
        $content = (string) $request->input('content');

        if (!$commentId || !$content) {
            throw new ApiException('参数错误', ResponseCode::PARAM_ERR);
        }
        if (mb_strlen($content) > 200) {
            throw new ApiException('回复内容最多200个字符', ResponseCode::PARAM_ERR);
        }

        $comment = new CommentReply();
        $comment->user_id = $user->id;
        $comment->comment_id = $commentId;
        $comment->content = $content;
        if (!$comment->save()) {
            throw new ApiException('回复失败', ResponseCode::PARAM_ERR);
        }

        $comment->load('user:id,name,avatar');

        return $this->responseSuccess($comment, '回复成功');
    }

    /**
     * 删除评论
     *
     * @param Request $request
     * @param Comment $comment
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function destroy(Request $request, Comment $comment)
    {
        $user = $request->user('api');

        if ($user->id !== $comment->user_id) {
            throw new ApiException('无权限操作', ResponseCode::FORBIDDEN);
        }

        $comment->is_delete = Comment::HAS_DELETE;
        $comment->save();

        return $this->responseSuccess(null, '删除成功');
    }

    /**
     * 点赞
     *
     * @param Request $request
     * @param $comment $news
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function doLike(Request $request, Comment $comment)
    {
        $user = $request->user('api');

        if ($comment->isLike($user->id)) {
            throw new ApiException('已点赞', ResponseCode::FORBIDDEN);
        } else {
            $comment->doLike($user->id);
            $comment->like += 1;
            $comment->save();
        }

        return $this->responseSuccess(null, '点赞成功');
    }

    /**
     * 取消点赞
     *
     * @param Request $request
     * @param $comment $news
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function unLike(Request $request, Comment $comment)
    {
        $user = $request->user('api');

        if (!$comment->isLike($user->id)) {
            throw new ApiException('未点赞', ResponseCode::FORBIDDEN);
        } else {
            $comment->unLike($user->id);
            $comment->like -= 1;
            $comment->save();
        }

        return $this->responseSuccess(null, '取消成功');
    }


    /**
     * 用户评论回复列表
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function replyUserList(Request $request)
    {
        $user = $request->user('api');
        $limit = $request->input('limit', 20);
        $offset = $request->input('offset', 0);

        $lists = CommentReply::query()
            ->with(['comment:id,content,user_id,created_at,target_id,target_type', 'user:id,name,avatar'])
            ->whereHas('comment', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->where('is_delete', Base::NOT_DELETE)
            ->select(['id', 'comment_id','content', 'created_at', 'user_id'])
            ->orderByDesc('is_read')
            ->orderByDesc('created_at')
            ->limit($limit)
            ->offset($offset * $limit)
            ->get();
        foreach ($lists as $list){
            $list->user->makeHidden(['is_wechat', 'is_qq', 'is_problem', 'is_pass']);
        }

        $total = CommentReply::query()
            ->whereHas('comment', function ($query) use ($user) {
                $query->where('user_id', $user->id);
            })
            ->where('is_delete', Base::NOT_DELETE)
            ->count();

        CommentReply::query()
            ->whereIn('id', $lists->pluck('id'))
            ->update([
                'is_read' => 1
            ]);

        return $this->responseSuccess(compact('lists', 'total'));
    }
}
