<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use App\Models\Course;
use App\Models\Goods;
use App\Models\News;
use App\Models\Plug;
use App\Models\User;
use Illuminate\Http\Request;

class SearchController extends Controller
{
    public function index(Request $request)
    {
        $limit = $request->input('limit', 20);
        $offset = $request->input('offset', 0);
        $keywords = $request->input('keywords');
        $tag = $request->input('tag');
        $type = $request->input('type', 'course');
        if ($limit == 0) {
            $limit = 20;
        }

        $newData = [];
        $newData1 = [];
        $newData2 = [];
        $newData3 = [];
        $newData4 = [];
        $newData5 = [];

        // 数据加缓存
//        if ($type == 'course') {
            // 课程
            Course::query()
                ->where(function ($query) use ($keywords) {
                    $query->where('name', 'like', '%' . $keywords . '%')
                        ->orWhere('intro', 'like', '%' . $keywords . '%')
                        ->orWhere('author', 'like', '%' . $keywords . '%')
                        ->orWhereHas('cate', function ($query) use ($keywords) {
                            $query->where('name', 'like', '%' . $keywords . '%');
                        });
                })
                ->when($tag , function ($query) use ($tag) {
                    $query->whereHas('tagList', function ($query) use ($tag) {
                        $query->where('tag', $tag);
                    });
                })
                ->where('is_delete', Course::NOT_DELETE)
                ->where('is_publish', 1)
                ->selectRaw('id,name,intro,images,created_at,author')
                ->get()
                ->map(function ($value) use (&$newData1) {
                    $images = handle_url($value->images);
                    if (is_dev()) {
                        $images = 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg';
                    }

                    $newData1[] = [
                        'id' => $value->id,
                        'name' => $value->name,
                        'intro' => substr_with_powerful($value->intro, 150),
                        'images' => $images,
                        'created_at' => $value->created_at->toDateTimeString(),
                        'type' => 'course',
                    ];
                });
//        } else if ($type == 'plug') {

            // 插件
            Plug::query()
                ->where(function ($query) use ($keywords) {
                    $query->where('name', 'like', '%' . $keywords . '%')
                        ->orWhere('intro', 'like', '%' . $keywords . '%')
                        ->orWhere('author', 'like', '%' . $keywords . '%')
                        ->orWhereHas('cate', function ($query) use ($keywords) {
                            $query->where('name', 'like', '%' . $keywords . '%');
                        });
                })
                ->when($tag , function ($query) use ($tag) {
                    $query->whereHas('tagList', function ($query) use ($tag) {
                        $query->where('tag', $tag);
                    });
                })
                ->where('state', 1)
                ->selectRaw('id,name,intro,images,created_at,author')
                ->get()
                ->map(function ($value) use (&$newData2) {
                    $images = handle_url($value->images);
                    if (is_dev()) {
                        $images = 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg';
                    }

                    $newData2[] = [
                        'id' => $value->id,
                        'name' => $value->name,
                        'intro' => substr_with_powerful($value->intro, 150),
                        'images' => $images,
                        'created_at' => $value->created_at->toDateTimeString(),
                        'type' => 'plug',
                    ];
                });

//        } else if ($type == 'goods') {
            // 周边
            Goods::query()
                ->when($tag , function ($query) use ($tag) {
                    $query->whereHas('tagList', function ($query) use ($tag) {
                        $query->where('tag', $tag);
                    });
                })
                ->where(function ($query) use ($keywords) {
                    $query->where('name', 'like', '%' . $keywords . '%')
                        ->orWhere('desc', 'like', '%' . $keywords . '%')
                        ->orWhereHas('cate', function ($query) use ($keywords) {
                            $query->where('name', 'like', '%' . $keywords . '%');
                        });
                })
                ->where('is_delete', Goods::NOT_DELETE)
                ->where('status', Goods::StatusMapping['yes'])
                ->select(['id', 'name', 'desc', 'cover', 'created_at'])
                ->get()
                ->map(function ($value) use (&$newData3) {
                    $images = handle_url($value->cover);
                    if (is_dev()) {
                        $images = 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg';
                    }

                    $newData3[] = [
                        'id' => $value->id,
                        'name' => $value->name,
                        'intro' => substr_with_powerful($value->desc, 150),
                        'images' => $images,
                        'created_at' => $value->created_at->toDateTimeString(),
                        'type' => 'goods',
                    ];
                });
//        } else if ($type == 'news') {

            // 新闻
            News::query()
                ->when($tag , function ($query) use ($tag) {
                    $query->whereHas('tagList', function ($query) use ($tag) {
                        $query->where('tag', $tag);
                    });
                })
                ->where(function ($query) use ($keywords) {
                    $query->where('title', 'like', '%' . $keywords . '%')
                        ->orWhere('content', 'like', '%' . $keywords . '%')
                        ->orWhere('author', 'like', '%' . $keywords . '%')
                        ->orWhereHas('cate', function ($query) use ($keywords) {
                            $query->where('name', 'like', '%' . $keywords . '%');
                        });
                })
                ->where('is_hide', 0)
                ->selectRaw('id,title,content,images,created_at,author')
                ->get()
                ->map(function ($value) use (&$newData4) {
                    $images = handle_url($value->images);
                    if (is_dev()) {
                        $images = 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg';
                    }

                    $newData4[] = [
                        'id' => $value->id,
                        'name' => $value->title,
                        'intro' => substr_with_powerful($value->content, 150),
                        'images' => $images,
                        'created_at' => $value->created_at->toDateTimeString(),
                        'type' => 'news',
                    ];
                });



        User::query()
            ->where(function ($query) use ($keywords) {
                $query->where('name', 'like', '%' . $keywords . '%');
            })
            ->where('is_delete', 0)
            ->where('state', 0)
            ->select(['id', 'name', 'intro', 'avatar', 'created_at'])
            ->get()
            ->map(function ($value) use (&$newData5) {
                $images = handle_url($value->avatar);
                if (is_dev()) {
                    $images = 'https://cube.elemecdn.com/6/94/4d3ea53c084bad6931a56d5158a48jpeg.jpeg';
                }

                $newData5[] = [
                    'id' => $value->id,
                    'name' => $value->name,
                    'intro' => substr_with_powerful($value->intro, 150),
                    'images' => $images,
                    'created_at' => $value->created_at->toDateTimeString(),
                    'type' => 'users',
                ];
            });
//        }
        if($type == 'course'){
            $newData = $newData1;
        } else if($type == 'plug'){
            $newData = $newData2;
        } else if($type == 'goods'){
            $newData = $newData3;
        } else if($type == 'news'){
            $newData = $newData4;
        } else if($type == 'users'){
            $newData = $newData5;
        }


        $count = count($newData);
        $totalAll = count(array_merge($newData1, $newData2, $newData3, $newData4, $newData5));
        if(count($newData)){
            $lists = collect($newData)->sortByDesc('created_at')->values()->chunk($limit)->offsetGet($offset);
        } else {
            $lists = collect([]);
        }

        return $this->responseSuccess(compact('lists', 'count', 'totalAll'));
    }

}
