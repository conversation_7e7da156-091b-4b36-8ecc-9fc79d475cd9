<?php

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\Base;
use App\Models\Goods;
use App\Models\GoodsCategory;
use App\Models\Order;
use App\Models\PlugCategory;
use App\Models\UserFans;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class GoodsController extends Controller
{
    public function getchildIds($id)
    {
        $ids = GoodsCategory::query()->when($id, function ($q) use ($id){
            if(is_array($id)){
                return $q -> whereIn('parent_id', $id);
            } else {
                return $q -> where('parent_id', $id);
            }
        }) -> pluck('id')->toArray();
        if(count($ids)){
            return array_merge($ids, $this -> getchildIds($ids));
        } else {
            return $ids;
        }
    }
    /**
     * 列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $limit = $request->input('limit', 20);
        $offset = $request->input('offset', 0);
        $cateId = $request->input('cate_id', 0);
        $level = $request->input('level');
        $sort = $request->input('sort');
        $cateIds = [];
        $cateIds = $this->getchildIds($cateId);

        $goods = Goods::query()
            ->where(function ($query) use ($cateId, $cateIds) {
                $query->where('cate_id', $cateId) -> orWhereIn('cate_id', $cateIds);
            })
            ->where('status', Goods::StatusMapping['yes'])
            ->where('is_delete', Base::NOT_DELETE)

            ->when($sort!=null, function ($query) use ($sort){
                if ($sort == 0) {
                    $query->orderByDesc('updated_at');
                } else if ($sort == 1) {
                    $query->orderByDesc('sort')
                        ->orderByDesc('id');
                } else {
                    $query->orderByDesc('sort')
                        ->orderByDesc('id');
                }
            })
//            ->orderByDesc('sort')
//            ->orderByDesc('id')
            ->offset($offset * $limit)
            ->limit($limit)
            ->get();

        $total = Goods::query()
            ->where(function ($query) use ($cateId, $cateIds) {
                $query->where('cate_id', $cateId) -> orWhereIn('cate_id', $cateIds);
            })
            ->where('is_delete', Base::NOT_DELETE)
            ->where('status', Goods::StatusMapping['yes'])
            ->count();

        foreach ($goods as $item) {
            $item->cover = handle_url($item->cover);
            if (is_dev()) {
                $item->cover = 'https://www.helloimg.com/images/2022/04/05/RmAfdo.jpg';
            }
        }

        return $this->responseSuccess(compact('goods', 'total'));
    }

    /**
     * 获取商品信息
     * @param Request $request
     * @param Goods $goods
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function info(Request $request, Goods $goods)
    {
        if ($goods->is_delete == Goods::HAS_DELETE || $goods->status == 0) {
            throw new ApiException('商品不存在', ResponseCode::NOT_FOUND);
        }
        $user = $request->user('api');
        $goods->cover = handle_url($goods->cover);
        $goods->load('suggestUser');
        $goods->load('tagListInfo');

        // 用户购买周边后，如果存在素材则展示素材下载
        $goods->append(['purchase']);
        // 如果免费或者已购买则返回下载链接
        $goods->material = handle_url($goods->material);
        logger()->info('material', [$goods->material, $goods->purchase]);
        if ($goods->purchase<=0 && $goods->price > 0) {
            $goods->material = '';
        }

        $images = $goods->images;
        foreach ($images as $key=>$image){
            $images[$key] = handle_url($image);
            if (is_dev()) {
                $images[$key] = 'https://www.helloimg.com/images/2022/04/05/RmAfdo.jpg';
            }
        }
        $goods->images = $images;

        // 更新浏览量
        Goods::query()
            ->where('id', $goods->id)
            ->update([
                'sales_num' => DB::raw('sales_num + ' . 1),
            ]);

        // 相关推荐
        $recommends = Goods::query()
            ->where('cate_id', $goods->cate_id)
            ->where('id', '!=', $goods->id)
            ->where('is_delete', Goods::NOT_DELETE)
            ->where('status', Goods::StatusMapping['yes'])
            ->limit(6)
            ->select(['cover', 'name', 'price', 'id'])
            ->get()->map(function ($value) {
                $value->cover = handle_url($value->cover);
                if (is_dev()) {
                    $value->cover = 'https://www.helloimg.com/images/2022/04/05/RmAfdo.jpg';
                }
                return $value;
            });

        $goods->nofans = UserFans::getFansStatus($user, $goods['user_id']);
        return $this->responseSuccess(compact('goods', 'recommends'));
    }

    /**
     * 分类映射
     * @return \Illuminate\Http\JsonResponse
     */
    public function cate(Request $request)
    {
        $parentId = $request->parent_id;

        $lists = GoodsCategory::query()
//            ->with('childrenCategory:id,name,parent_id')
            ->with(['childrenCategory' => function($q){
                return $q -> with(['childrenCategory' => function($q){
                    return $q -> with(['childrenCategory'])->orderByDesc('sort');
                }])->orderByDesc('sort');
            }])
            ->where(function ($query) use ($parentId) {
                if ($parentId > 0) {
                    $query->where('parent_id', $parentId);
                } else {
                    $query->where('parent_id', 0);
                }
            })
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->get(['id', 'name', 'parent_id'])
            ->toArray();

        array_unshift($lists, [
            'id' => 0,
            'level' => empty($parentId) > 0 ? 1 : 2,
            'name' => '全部',
            'parent_id' => empty($parentId) ? 0 : $parentId,
            'children_category' => []
        ]);

        return $this->responseSuccess($lists);
    }



}
