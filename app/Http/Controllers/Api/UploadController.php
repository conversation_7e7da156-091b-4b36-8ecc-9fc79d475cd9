<?php

namespace App\Http\Controllers\Api;

use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class UploadController extends Controller
{
    public function upload()
    {
        #实现自定义文件上传
        $file = request()->file('file');
        //获取文件的扩展名
        $name = $file->getClientOriginalExtension();
        //获取文件的绝对路径
        $path = $file->getRealPath();
        //定义新的文件名
        $filename = 'avatar/'.md5($name.time()).'.'.$name;
        Log::info(public_path($filename));

        Storage::disk('public')->put($filename, file_get_contents($path));

        return $this->responseSuccess(['filename'=>$filename]);
    }
}
