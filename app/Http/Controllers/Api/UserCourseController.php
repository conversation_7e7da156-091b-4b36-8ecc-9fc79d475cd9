<?php

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\Goods;
use App\Models\UserCourse;
use Illuminate\Http\Request;

class UserCourseController extends Controller
{
    /**
     * 用户观看课程列表信息
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function index(Request $request)
    {
        $user = $request->user('api');
        $type = $request->input('type');
        $offset = $request->input('offset', 0);
        $limit = $request->input('limit', 10);

        if (!in_array($type, UserCourse::TypeMappings)) {
            throw new ApiException('参数错误', ResponseCode::PARAM_ERR);
        }
        if($type=='material'){
            $lists = Goods::query()
                ->whereHas('userorder', function ($query) use($user){
                    return $query->where('user_id', $user->id) ->orderByDesc('created_at');
                })
                ->offset($offset * $limit)
                ->limit($limit)
                ->get();

            $total = Goods::query()
                ->whereHas('userorder', function ($query) use($user){
                    return $query->where('user_id', $user->id);
                })
                ->count();
        } else {
            $lists = UserCourse::query()
                ->with(['course:id,name,images'])
                ->where('user_id', $user->id)
                ->where('type', $type)
                ->orderByDesc('created_at')
                ->offset($offset * $limit)
                ->limit($limit)
                ->get();

            $total = UserCourse::query()
                ->where('user_id', $user->id)
                ->where('type', $type)
                ->count();
        }



        return $this->responseSuccess(compact('lists', 'total'));
    }

    /**
     * 课程收藏
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function collect(Request $request)
    {
        $user = $request->user('api');
        $courseId = $request->input('course_id');

        if (!$user || !$courseId) {
            throw new ApiException('参数错误', ResponseCode::PARAM_ERR);
        }

        $isExists = UserCourse::query()
            ->where('user_id', $user->id)
            ->where('course_id', $courseId)
            ->where('type', UserCourse::TypeMappings['collect'])
            ->exists();
        if ($isExists) {
            throw new ApiException('已在收藏或学习列表中', ResponseCode::PARAM_ERR);
        }

        $userCourse = new UserCourse();
        $userCourse->user_id = $user->id;
        $userCourse->course_id = $courseId;
        $userCourse->type = UserCourse::TypeMappings['collect'];
        if (!$userCourse->save()) {
            throw new ApiException('收藏失败，请重试', ResponseCode::PARAM_ERR);
        }

        return $this->responseSuccess(null, '收藏成功');
    }

    /**
     * 取消课程收藏
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function cancelCollect(Request $request)
    {
        $user = $request->user('api');
        $userCourseId = $request->input('user_course_id');

        if (!$user || !$userCourseId) {
            throw new ApiException('参数错误', ResponseCode::PARAM_ERR);
        }

        $userCourse = UserCourse::query()->where('id', $userCourseId)->first();
        if (!$userCourse) {
            throw new ApiException('课程已不在收藏中，请刷新页面重试', ResponseCode::NOT_FOUND);
        }

        if ($userCourse->user_id != $user->id) {
            throw new ApiException('无处理权限', ResponseCode::FORBIDDEN);
        }

        $userCourse->delete();

        return $this->responseSuccess(null, '取消成功');
    }
}
