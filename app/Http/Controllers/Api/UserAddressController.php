<?php

namespace App\Http\Controllers\Api;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Requests\Api\UserAddressRequest;
use App\Models\UserAddress;
use Illuminate\Http\Request;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class UserAddressController extends Controller
{
    /**
     * 用户收货地址列表
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $user = $request->user('api');
        $limit = (int)$request->query('limit', 20);
        $offset = (int)$request->query('offset', 0);
        $list = UserAddress::query()
            ->where('user_id',$user->id)
            ->limit($limit)
            ->offset($offset * $limit)
            ->get();
        return $this->responseSuccess($list);
    }

    /**
     * 创建
     * @param UserAddressRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function store(UserAddressRequest $request)
    {
        $user = $request->user('api');
        $name = $request->input('name');
        $phone = $request->input('phone');
        $address = $request->input('address');
        $post_code = $request->input('post_code');
        $status = $request->input('status',0);

        if (!is_phone($phone)){
            throw new ApiException('联系电话格式不正确', ResponseCode::FORBIDDEN);
        }

        try {
            DB::beginTransaction();
            if ($status === 1) {
                UserAddress::query()->where('user_id', $user->id)->update(['status'=>0]);
            }
            if (!UserAddress::query()->where('user_id', $user->id)->exists()){
                $status = 1;
            }

            $user_address = new UserAddress();
            $user_address->user_id = $user->id;
            $user_address->name = $name;
            $user_address->phone = $phone;
            $user_address->address = $address;
            $user_address->status = $status;
            if ($post_code) {
                $user_address->post_code = $post_code;
            }
            $user_address->save();
            DB::commit();

            return $this->responseSuccess($user_address, '添加成功');
        } catch (\Throwable $t) {
            DB::rollBack();
            Log::error($t);
            throw new ApiException('添加失败', ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 修改
     * @param UserAddressRequest $request
     * @param $address_id
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function update(UserAddressRequest $request, $address_id)
    {
        $user = $request->user('api');
        $data = $request->only(['name', 'phone', 'address', 'status', 'post_code']);
        if (!UserAddress::query()->where('user_id', $user->id)->where('id',$address_id)->exists()){
            throw new ApiException('地址不正确', ResponseCode::FORBIDDEN);
        }
        if (isset($data['phone']) && !is_phone($data['phone'])){
            throw new ApiException('联系电话格式不正确', ResponseCode::FORBIDDEN);
        }
        try {
            if (isset($data['status']) && $data['status'] == 1){
                UserAddress::query()->where('user_id', $user->id)->update(['status'=>0]);
            }
            UserAddress::query()
                ->where('id',$address_id)
                ->update($data);
            return $this->responseSuccess(null, '修改成功');
        } catch (\Throwable $t) {
            Log::error($t);
            throw new ApiException('地址修改失败', ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 删除
     * @param Request $request
     * @param $address_id
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Request $request, $address_id)
    {
        $user = $request->user('api');
        UserAddress::query()
            ->where('id',$address_id)
            ->delete();
        $address_list = UserAddress::query()->where('user_id',$user->id)->get();
        if ($address_list->isNotEmpty() && !$address_list->where('status',1)->first()){
            UserAddress::query()->where('user_id',$user->id)->first()->update(['status'=>1]);
        }
        return $this->responseSuccess(null, '删除成功');
    }
}
