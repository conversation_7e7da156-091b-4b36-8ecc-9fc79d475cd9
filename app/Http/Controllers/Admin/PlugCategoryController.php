<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CategoryRequest;
use App\Models\Base;
use App\Models\CourseCategory;
use App\Models\GoodsCategory;
use App\Models\Plug;
use App\Models\PlugCategory;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class PlugCategoryController extends Controller
{
    /**
     * 分类列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $limit = (int)$request->query('limit', 20);

        $category = PlugCategory::query()
//            ->with('childrenCategory')
            ->with(['childrenCategory' => function($q){
                return $q -> with(['childrenCategory' => function($q){
                    return $q -> with(['childrenCategory'])->orderByDesc('sort');
                }])->orderByDesc('sort');
            }])
            ->where('parent_id', 0)
            ->orderByDesc('sort')
            ->paginate($limit);

        return $this->responseSuccess($category);
    }

    public function getItem($category)
    {
        $list = [];
        foreach ($category as $k => $v){
            $list[] = $v;
            if($v['childrenCategory']){
                foreach ($v['childrenCategory'] as $k2 =>$v2){
                    $v2['name'] = '——  '.$v2['name'];
                    $list[] = $v2;
                    foreach ($v2['childrenCategory'] as $k3 =>$v3){
                        $v3['name'] = '————  '.$v3['name'];
                        $list[] = $v3;
//                        foreach ($v3['childrenCategory'] as $k4 =>$v4){
//                            $v4['name'] = '——————  '.$v4['name'];
//                            $list[] = $v4;
//                        }
                    }
                }
            }
        }
        return($list);;
    }
    /**
     * 添加商品分类
     *
     * @param CategoryRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function store(CategoryRequest $request)
    {

        $name = $request->input('name');
        if (mb_strlen($name) > 20){
            throw new ApiException('分类名称最长20个字符', ResponseCode::PARAM_ERR);
        }
        if (PlugCategory::query()->where('name',$name)->exists()){
//            throw new ApiException('分类名称已存在', ResponseCode::PARAM_ERR);
        }

        $parentId = $request->input('parent_id', 0);
        if ($parentId) {
            $parentCategory = PlugCategory::query()->find($parentId);
            if (!$parentCategory) {
                throw new ApiException('父级分类不存在', ResponseCode::PARAM_ERR);
            }
            if ($parentCategory->parent_id > 0) {
//                throw new ApiException('不能继续创建子分类', ResponseCode::PARAM_ERR);
            }
        }

        $cate = new PlugCategory();
        $cate->name = $request->input('name');
        $cate->sort = $request->input('sort', 0);
        $cate->parent_id = empty($parentId) ? 0 : $parentId;
        $cate->save();

        return $this->responseSuccess($cate, '添加成功');
    }

    /**
     * 修改商品分类
     *
     * @param CategoryRequest $request
     * @param CourseCategory $category
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function update(CategoryRequest $request, PlugCategory $category)
    {
        $name = $request->input('name');
        if (mb_strlen($name) > 20){
            throw new ApiException('分类名称最长20个字符', ResponseCode::PARAM_ERR);
        }

        $parentId = $request->input('parent_id', 0);
        $category->name = $name;
        $category->sort = $request->input('sort', 0);
        $category->parent_id = empty($parentId) ? 0 : $parentId;
        $category->save();

        return $this->responseSuccess($category, '编辑成功');
    }

    /**
     * 删除分类
     *
     * @param CourseCategory $category
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function destroy(PlugCategory $category)
    {
        if (Plug::query()->where('cate_id',$category->id)->where('is_delete',Base::NOT_DELETE)->exists()){
            throw new ApiException('分类下存在课程，无法删除', ResponseCode::PARAM_ERR);
        }

        $parentExist = PlugCategory::query()->where('parent_id', $category->id)->count();
        if ($parentExist) {
            throw new ApiException('分类下存在子分类，无法删除', ResponseCode::PARAM_ERR);
        }
        try {
            $category->delete();
            return $this->responseSuccess(null, '删除成功');
        } catch (\Exception $exception) {
            throw new ApiException('删除失败', ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 设置排序
     *
     * @param Request $request
     * @param CourseCategory $category
     * @return \Illuminate\Http\JsonResponse
     */
    public function settingSort(Request $request, PlugCategory $category)
    {
        $category->sort = $request->input('sort');
        $category->save();

        return $this->responseSuccess(null, '编辑成功');
    }


    /**
     * 分类映射
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function filter(Request $request)
    {
        $parentId = $request->input('parent_id', 0);

        if(!$parentId){
            $category = PlugCategory::query()
                ->with(['childrenCategory' => function($q){
                    return $q -> with(['childrenCategory' => function($q){
                        return $q->orderByDesc('sort');
//                        return $q -> with(['childrenCategory'])->orderByDesc('sort');
                    }])->orderByDesc('sort');
                }])
                ->where('parent_id', 0)
                ->orderByDesc('sort')
                ->get();
            $lists = $this->getItem($category);
            return $this->responseSuccess($lists);
        }

        $lists = PlugCategory::query()
            ->where(function ($query) use ($parentId) {
                if ($parentId) {
                    $query->where('parent_id', $parentId);
                } else {
//                    $query->where('parent_id', 0);
                }
            })
            ->get(['id', 'name']);

        return $this->responseSuccess($lists);
    }
}
