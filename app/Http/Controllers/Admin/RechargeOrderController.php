<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\RechargeOrder;
use App\Models\InvoiceRecord;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Rap2hpoutre\FastExcel\FastExcel;

/**
 * 充值订单
 */
class RechargeOrderController extends Controller
{
    public function index(Request $request)
    {
        $phone = $request->input('phone');
        $order_no = $request->input('order_no');
        $start_time = $request->input('start_time');
        $end_time = $request->input('end_time');
        $status = $request->input('status');
        $pay_method = $request->input('pay_method');
        $invoice_status = $request->input('invoice_status'); // 新增开票状态筛选
        $limit = $request->query('limit', 15);

        $list = RechargeOrder::query()
            ->when($status != '' && $status != 4, function ($query) use ($status) {
                return $query->where('status', (int)$status);
            })
            ->when($order_no != '', function ($query) use ($order_no) {
                return $query->where('order_no', $order_no);
            })
            ->when($pay_method != '', function ($query) use ($pay_method) {
                $query->where('pay_method', $pay_method);
            })
            ->when($invoice_status !== null && $invoice_status !== '', function ($query) use ($invoice_status) {
                $query->where('invoice_status', (int)$invoice_status);
            })
            ->when($start_time || $end_time, function ($query) use ($start_time, $end_time) {
                if ($start_time) {
                    $query->where('created_at', '>=', $start_time);
                }
                if ($end_time) {
                    $query->where('created_at', '<', $end_time);
                }
                return $query;
            })
            ->when($phone != '', function ($query) use ($phone) {
                $query->whereHas('user', function ($query) use ($phone) {
                    $query->where('phone', $phone);
                });
            })
            ->with(['user', 'latestInvoiceRecord.invoiceInfo']) // 增加开票记录关联
            ->orderByDesc('id')
            ->paginate($limit);

        $wait_count = RechargeOrder::query()
            ->where('status', RechargeOrder::StatusMappings['wait'])
            ->when($start_time || $end_time, function ($query) use ($start_time, $end_time) {
                if ($start_time) {
                    $query->where('created_at', '>=', $start_time);
                }
                if ($end_time) {
                    $query->where('created_at', '<', $end_time);
                }
                return $query;
            })
            ->when($order_no != '', function ($query) use ($order_no) {
                return $query->where('order_no', $order_no);
            })
            ->when($phone != '', function ($query) use ($phone) {
                $query->whereHas('user', function ($query) use ($phone) {
                    $query->where('phone', $phone);
                });
            })
            ->count();
        $data = [
            'data' => $list,
            'wait_count' => $wait_count
        ];
        return $this->responseSuccess($data);
    }

    /**
     * 订单导出
     * @return mixed
     * @throws ApiException
     */
    public function export(Request $request)
    {
        $orders = $this->generator($request);

        try {
            $methods = [
                0=> '无',
                1=> '微信',
                2=> '支付宝'
            ];
            $status = [
                0 => '未支付',
                1 => '已支付',
                2 => '已关闭'
            ];
            $fileName = sprintf('%s.xlsx', date('YmdHis').'-充值订单导出');
            (new FastExcel($orders))->export($fileName, function ($item) use ($methods, $status) {
                return [
                    'ID' => $item['id'],
                    '用户昵称' => $item['user']['name'] ?? '-',
                    '用户手机号' => $item['user']['phone'] ?? '-',
                    '订单号' => $item['order_no'],
                    '支付金额' => $item['order_amount'],
                    '总金额' => $item['total_amount'],
                    '支付时间' => $item['pay_time'],
                    '支付状态' => $status[$item['status']],
                    '支付方式' => $methods[$item['pay_method']],
                    '创建时间' => $item['created_at'],
                ];
            });
            return $this->responseSuccess(['filename' => $fileName]);
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
            throw new ApiException('导出失败', ResponseCode::SERVER_ERR);
        }
    }
    public function generator($request)
    {
        $phone = $request->input('phone');
        $order_no = $request->input('order_no');
        $start_time = $request->input('start_time');
        $end_time = $request->input('end_time');
        $status = $request->input('status');
        $pay_method = $request->input('pay_method');

        foreach (RechargeOrder::query()
                     ->when($status != '', function ($query) use ($status) {
                         return $query->where('status', (int)$status);
                     })
                     ->when($order_no != '', function ($query) use ($order_no) {
                         return $query->where('order_no', $order_no);
                     })
                     ->when($pay_method != '', function ($query) use ($pay_method) {
                         $query->where('pay_method', $pay_method);
                     })
                     ->when($start_time || $end_time, function ($query) use ($start_time, $end_time) {
                         if ($start_time) {
                             $query->where('created_at', '>=', $start_time);
                         }
                         if ($end_time) {
                             $query->where('created_at', '<', $end_time);
                         }
                         return $query;
                     })
                     ->when($phone != '', function ($query) use ($phone) {
                         $query->whereHas('user', function ($query) use ($phone) {
                             $query->where('phone', $phone);
                         });
                     })
                     ->with(['user'])
                     ->orderByDesc('id')
                     ->cursor() as $item) {
            yield $item;
        }
    }
}
