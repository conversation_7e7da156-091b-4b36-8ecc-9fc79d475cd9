<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\RechargeOrder;
use App\Models\InvoiceRecord;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Rap2hpoutre\FastExcel\FastExcel;

/**
 * 充值订单
 */
class RechargeOrderController extends Controller
{
    public function index(Request $request)
    {
        $phone = $request->input('phone');
        $order_no = $request->input('order_no');
        $start_time = $request->input('start_time');
        $end_time = $request->input('end_time');
        $status = $request->input('status');
        $pay_method = $request->input('pay_method');
        $invoice_status = $request->input('invoice_status'); // 新增开票状态筛选
        $limit = $request->query('limit', 15);

        $list = RechargeOrder::query()
            ->when($status != '' && $status != 4, function ($query) use ($status) {
                return $query->where('status', (int)$status);
            })
            ->when($order_no != '', function ($query) use ($order_no) {
                return $query->where('order_no', $order_no);
            })
            ->when($pay_method != '', function ($query) use ($pay_method) {
                $query->where('pay_method', $pay_method);
            })
            ->when($invoice_status !== null && $invoice_status !== '', function ($query) use ($invoice_status) {
                $query->where('invoice_status', (int)$invoice_status);
            })
            ->when($start_time || $end_time, function ($query) use ($start_time, $end_time) {
                if ($start_time) {
                    $query->where('created_at', '>=', $start_time);
                }
                if ($end_time) {
                    $query->where('created_at', '<', $end_time);
                }
                return $query;
            })
            ->when($phone != '', function ($query) use ($phone) {
                $query->whereHas('user', function ($query) use ($phone) {
                    $query->where('phone', $phone);
                });
            })
            ->with(['user', 'latestInvoiceRecord.invoiceInfo']) // 增加开票记录关联
            ->orderByDesc('id')
            ->paginate($limit);

        $wait_count = RechargeOrder::query()
            ->where('status', RechargeOrder::StatusMappings['wait'])
            ->when($start_time || $end_time, function ($query) use ($start_time, $end_time) {
                if ($start_time) {
                    $query->where('created_at', '>=', $start_time);
                }
                if ($end_time) {
                    $query->where('created_at', '<', $end_time);
                }
                return $query;
            })
            ->when($order_no != '', function ($query) use ($order_no) {
                return $query->where('order_no', $order_no);
            })
            ->when($phone != '', function ($query) use ($phone) {
                $query->whereHas('user', function ($query) use ($phone) {
                    $query->where('phone', $phone);
                });
            })
            ->count();

        // 统计待处理开票数量
        $pending_invoice_count = InvoiceRecord::query()
            ->where('status', InvoiceRecord::StatusMappings['pending'])
            ->count();

        $data = [
            'data' => $list,
            'wait_count' => $wait_count,
            'pending_invoice_count' => $pending_invoice_count
        ];
        return $this->responseSuccess($data);
    }

    /**
     * 订单导出
     * @return mixed
     * @throws ApiException
     */
    public function export(Request $request)
    {
        $orders = $this->generator($request);

        try {
            $methods = [
                0=> '无',
                1=> '微信',
                2=> '支付宝'
            ];
            $status = [
                0 => '未支付',
                1 => '已支付',
                2 => '已关闭'
            ];
            $invoiceStatus = [
                0 => '未开票',
                1 => '已申请',
                2 => '已完成'
            ];
            $fileName = sprintf('%s.xlsx', date('YmdHis').'-充值订单导出');
            (new FastExcel($orders))->export($fileName, function ($item) use ($methods, $status, $invoiceStatus) {
                return [
                    'ID' => $item['id'],
                    '用户昵称' => $item['user']['name'] ?? '-',
                    '用户手机号' => $item['user']['phone'] ?? '-',
                    '订单号' => $item['order_no'],
                    '支付金额' => $item['order_amount'],
                    '总金额' => $item['total_amount'],
                    '支付时间' => $item['pay_time'],
                    '支付状态' => $status[$item['status']],
                    '支付方式' => $methods[$item['pay_method']],
                    '开票次数' => $item['invoice_count'],
                    '开票状态' => $invoiceStatus[$item['invoice_status']],
                    '创建时间' => $item['created_at'],
                ];
            });
            return $this->responseSuccess(['filename' => $fileName]);
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
            throw new ApiException('导出失败', ResponseCode::SERVER_ERR);
        }
    }
    public function generator($request)
    {
        $phone = $request->input('phone');
        $order_no = $request->input('order_no');
        $start_time = $request->input('start_time');
        $end_time = $request->input('end_time');
        $status = $request->input('status');
        $pay_method = $request->input('pay_method');

        foreach (RechargeOrder::query()
                     ->when($status != '', function ($query) use ($status) {
                         return $query->where('status', (int)$status);
                     })
                     ->when($order_no != '', function ($query) use ($order_no) {
                         return $query->where('order_no', $order_no);
                     })
                     ->when($pay_method != '', function ($query) use ($pay_method) {
                         $query->where('pay_method', $pay_method);
                     })
                     ->when($start_time || $end_time, function ($query) use ($start_time, $end_time) {
                         if ($start_time) {
                             $query->where('created_at', '>=', $start_time);
                         }
                         if ($end_time) {
                             $query->where('created_at', '<', $end_time);
                         }
                         return $query;
                     })
                     ->when($phone != '', function ($query) use ($phone) {
                         $query->whereHas('user', function ($query) use ($phone) {
                             $query->where('phone', $phone);
                         });
                     })
                     ->with(['user'])
                     ->orderByDesc('id')
                     ->cursor() as $item) {
            yield $item;
        }
    }

    /**
     * 获取订单的开票记录
     */
    public function getInvoiceRecords(Request $request, $id)
    {
        $rechargeOrder = RechargeOrder::find($id);
        if (!$rechargeOrder) {
            throw new ApiException('充值订单不存在', ResponseCode::NOT_FOUND);
        }

        $records = InvoiceRecord::query()
            ->where('recharge_order_id', $id)
            ->with(['invoiceInfo', 'user'])
            ->orderByDesc('id')
            ->get();

        return $this->responseSuccess([
            'recharge_order' => $rechargeOrder->load('user'),
            'invoice_records' => $records
        ]);
    }

    /**
     * 上传发票文件
     */
    public function uploadInvoice(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'invoice_record_id' => 'required|exists:invoice_records,id',
            'invoice_file' => 'required|file|mimes:pdf|max:10240', // 最大10MB
            'invoice_no' => 'nullable|string|max:50',
            'remark' => 'nullable|string|max:500',
        ], [
            'invoice_record_id.required' => '开票记录ID不能为空',
            'invoice_record_id.exists' => '开票记录不存在',
            'invoice_file.required' => '发票文件不能为空',
            'invoice_file.file' => '发票文件格式错误',
            'invoice_file.mimes' => '发票文件必须为PDF格式',
            'invoice_file.max' => '发票文件大小不能超过10MB',
            'invoice_no.max' => '发票号码不能超过50个字符',
            'remark.max' => '备注不能超过500个字符',
        ]);

        if ($validator->fails()) {
            throw new ApiException($validator->errors()->first(), ResponseCode::PARAM_ERR);
        }

        try {
            $invoiceRecord = InvoiceRecord::find($request->input('invoice_record_id'));

            if (!$invoiceRecord) {
                throw new ApiException('开票记录不存在', ResponseCode::NOT_FOUND);
            }

            // 只有待处理或重开中状态才能上传
            if (!in_array($invoiceRecord->status, [InvoiceRecord::StatusMappings['pending'], InvoiceRecord::StatusMappings['reopen']])) {
                throw new ApiException('当前状态不允许上传发票', ResponseCode::PARAM_ERR);
            }

            // 上传文件
            $file = $request->file('invoice_file');
            $fileName = 'invoices/' . date('Y/m/d') . '/' . time() . '_' . $file->getClientOriginalName();
            $filePath = $file->storeAs('public', $fileName);

            // 删除旧文件
            if ($invoiceRecord->invoice_file) {
                Storage::delete('public/' . $invoiceRecord->invoice_file);
            }

            // 更新记录
            $invoiceRecord->update([
                'invoice_file' => $fileName,
                'invoice_no' => $request->input('invoice_no', ''),
                'status' => InvoiceRecord::StatusMappings['uploaded'],
                'upload_time' => now(),
                'remark' => $request->input('remark', $invoiceRecord->remark),
            ]);

            // 更新充值订单状态
            $invoiceRecord->rechargeOrder->update([
                'invoice_status' => RechargeOrder::InvoiceStatusMappings['completed']
            ]);

            return $this->responseSuccess($invoiceRecord->load(['invoiceInfo', 'rechargeOrder']), '上传成功');
        } catch (ApiException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('上传发票失败', ['error' => $e->getMessage()]);
            throw new ApiException('上传失败', ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 更新开票状态
     */
    public function updateInvoiceStatus(Request $request, $id)
    {
        $validator = Validator::make($request->all(), [
            'status' => 'required|in:1,2,3',
            'remark' => 'nullable|string|max:500',
        ], [
            'status.required' => '状态不能为空',
            'status.in' => '状态值无效',
            'remark.max' => '备注不能超过500个字符',
        ]);

        if ($validator->fails()) {
            throw new ApiException($validator->errors()->first(), ResponseCode::PARAM_ERR);
        }

        try {
            $invoiceRecord = InvoiceRecord::find($id);

            if (!$invoiceRecord) {
                throw new ApiException('开票记录不存在', ResponseCode::NOT_FOUND);
            }

            $status = $request->input('status');
            $remark = $request->input('remark', $invoiceRecord->remark);

            $invoiceRecord->update([
                'status' => $status,
                'remark' => $remark,
            ]);

            // 根据状态更新充值订单状态
            if ($status == InvoiceRecord::StatusMappings['uploaded']) {
                $invoiceRecord->rechargeOrder->update([
                    'invoice_status' => RechargeOrder::InvoiceStatusMappings['completed']
                ]);
            } elseif (in_array($status, [InvoiceRecord::StatusMappings['pending'], InvoiceRecord::StatusMappings['reopen']])) {
                $invoiceRecord->rechargeOrder->update([
                    'invoice_status' => RechargeOrder::InvoiceStatusMappings['applied']
                ]);
            }

            return $this->responseSuccess($invoiceRecord->load(['invoiceInfo', 'rechargeOrder']), '更新成功');
        } catch (ApiException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('更新开票状态失败', ['error' => $e->getMessage()]);
            throw new ApiException('更新失败', ResponseCode::SERVER_ERR);
        }
    }
}
