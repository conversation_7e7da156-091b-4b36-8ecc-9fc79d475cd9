<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CourseRequest;
use App\Jobs\PopularizeUserPushJob;
use App\Jobs\ShenzaoAuthorize;
use App\Models\Base;
use App\Models\Course;
use App\Models\CourseCategory;
use App\Models\CourseDetail;
use App\Models\TagList;
use App\Models\User;
use App\Models\UserCourse;
use App\Models\UserFile;
use App\Services\ShenzaoService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CourseController extends Controller
{
    public function index(Request $request)
    {
        $keywords = $request->query('keywords');
        $is_publish = $request->query('is_publish');
        $category_id = $request->query('category_id'); // 分类ID筛选
        $limit = $request->input('limit', 20);
        $lists = Course::query()
            ->with(['authorInfo', 'suggestUser:id,name',
                'tagListInfo',
                'cate'=>function($q){
                    $q->with('parentCategory');
                }
            ])
            ->when($keywords!='', function ($query) use ($keywords) {
                return $query->where('name', 'like', "%$keywords%");
            })
            ->when($is_publish!='', function ($query) use ($is_publish) {
                return $query->where('is_publish', $is_publish);
            })
            ->when($category_id!='', function ($query) use ($category_id) {
                // 获取当前分类及所有子分类ID
                $categoryIds = $this->getCategoryWithChildren($category_id, 'course');
                return $query->whereIn('cate_id', $categoryIds);
            })
            ->orderByDesc('id')
            ->paginate($limit);

        return $this->responseSuccess($lists);
    }

    public function info(Course $course)
    {
        $course->load('authorInfo');
        $tags = [];
        $course->load('tagList');
        $tagList = $course->tagList;
        if ($tagList) {
            foreach ($tagList as $item) {
                $tags[] = $item->tag;
            }
        }
        $course -> tag = $tags;
        return $this->responseSuccess($course);
    }

    public function store(CourseRequest $request)
    {
        $data = $request->only(['price','explain','give_integral','is_integral','integral_num','is_publish','market_price','name','num','sort', 'images','cate_id','intro', 'user_id']);
        $data['sz_course_id'] = $request->input('sz_course_id') ?: '';
        $course = Course::query()->create($data);


        $info = TagList::delTagInfo(['aid' => $course->id, 'tag' => $request->input('tag', []), 'type' => 'course']);
        $info = TagList::saveTagInfo(['aid' => $course->id, 'tag' => $request->input('tag', []), 'type' => 'course']);
        $send = (int)$request->input('send', 0);
        if ($send) {
            dispatch(new PopularizeUserPushJob($course->id ?? 0, 'course', [
                'commodity' => $data['name'].'|课程',
                'money' => $data['market_price'],
                'money2' => $data['price'],
            ]));
        }

        return $this->responseSuccess(null, '添加成功');
    }

    public function update(Request $request, Course $course)
    {
        $data = $request->only(['price','explain','give_integral','is_integral','integral_num','is_publish','market_price','name','num','sort', 'images','cate_id','intro', 'user_id']);
        $data['sz_course_id'] = $request->input('sz_course_id') ?: '';
        $course->update($data);
        $info = TagList::delTagInfo(['aid' => $course->id, 'tag' => $request->input('tag', []), 'type' => 'course']);
        $info = TagList::saveTagInfo(['aid' => $course->id, 'tag' => $request->input('tag', []), 'type' => 'course']);
        $send = (int)$request->input('send', 0);
        if ($send) {
            dispatch(new PopularizeUserPushJob($course->id ?? 0, 'course', [
                'commodity' => $course->name.'|课程',
                'money' => $course->market_price,
                'money2' => $course->price,
            ]));
        }
        return $this->responseSuccess(null, '编辑成功');
    }
    /**
     * 课程删除
     * @param Course $course
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Course $course)
    {
        $userCourse = UserFile::query()->where('vid', $course->id)->delete();
        $course->delete();
        return $this->responseSuccess(null, '删除成功');
    }


    /**
     * 用户课程列表
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function userCourseList(Request $request)
    {
        $user_id = $request->input('user_id');
        $phone = $request->input('phone');
        $keywords = $request->input('keywords');
        $type = $request->input('type');
        $limit = $request->input('limit', 20);
//        Log::info('user_id', $user_id);

        $lists = UserCourse::query()
            ->with('course:id,name,images','admin:id,name','user:id,name')
            ->when($user_id!='',function ($query) use ($user_id) {
                $query->where('user_id', $user_id);
            })
            ->when($phone!='',function ($query)use ($phone) {
                $query->whereHas('user', function ($query) use ($phone) {
                    $query->where('phone', $phone);
                });
            })
            ->when($keywords!='',function ($query)use($keywords) {
                $query->whereHas('course', function ($query) use ($keywords) {
                    $query->where('name', 'like', "%$keywords%");
                });
            })
            ->when($type!='',function ($query)use($type) {
                $query->where('type', $type);
            })
//            ->where('is_delete', Base::NOT_DELETE)
            ->orderByDesc('id')
            ->paginate($limit);

        return $this->responseSuccess($lists);
    }

    /**
     * 用户课程删除
     * @param UserCourse $userCourse
     * @return \Illuminate\Http\JsonResponse
     */
    public function userCourseDelete(UserCourse $userCourse)
    {
        $user = $userCourse->user;
        $userCourse->delete();

        // 课程取消授权深造绑定账号权限
        dispatch(new ShenzaoAuthorize($user->phone, $userCourse->course->sz_course_id, 0));

        return $this->responseSuccess(null, '删除成功');
    }

    /**
     * 用户课程添加
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function userCourseSotre(Request $request)
    {
        $phone = $request->input('phone');
        $course_id = $request->input('course_id');
        $admin = $request->user('admin');
        $user_id = User::query()->where('phone', $phone)->value('id');
        if (!$user_id) {
            throw new ApiException('用户不存在', ResponseCode::NOT_FOUND);
        }

        $exists = Course::query()->where('id', $course_id)->where('is_delete', Base::NOT_DELETE)->exists();
        if (!$exists) {
            throw new ApiException('课程不存在', ResponseCode::NOT_FOUND);
        }

        $userCourseExists = UserCourse::query()->where('course_id', $course_id)->where('user_id', $user_id)->exists();
        if ($userCourseExists) {
            throw new ApiException('用户课程已存在', ResponseCode::NOT_FOUND);
        }

        $userCourse = new UserCourse();
        $userCourse->user_id = $user_id;
        $userCourse->course_id = $course_id;
        $userCourse->admin_id = $admin->id;
        $userCourse->save();

        // 课程授权深造绑定账号权限
        dispatch(new ShenzaoAuthorize($phone, $userCourse->course->sz_course_id));

        return $this->responseSuccess(null, '添加成功');
    }

    public function cateFilter()
    {
//        $lists = CourseCategory::query()
//            ->with('parentCategory:id,name')
//            ->where('parent_id', '>', 0)
//            ->orderByDesc('sort')
//            ->orderByDesc('id')
//            ->get(['id', 'name', 'parent_id']);
//
//        $newList = [];
//        foreach ($lists as $list) {
//            $newList[] = [
//                'id' => $list->id,
//                'name' => ($list->parentCategory->name ?? '课程') . ' / ' . $list->name
//            ];
//        }
        $lists = CourseCategory::query()
            ->with(['childrenCategory' => function($q){
                return $q -> with(['childrenCategory' => function($q){
                    return $q -> with(['childrenCategory'])->orderByDesc('sort');
                }])->orderByDesc('sort');
            }])
            ->where(function ($query) {
                $query->where('parent_id', 0);
            })
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->get(['id', 'name', 'parent_id']);
        $newList = [];
        foreach ($lists as $list) {
            $newList[] = [
                'id' => $list->id,
                'name' =>  $list->name
            ];
            foreach ($list->childrenCategory as $child){
                $newList[] = [
                    'id' => $child->id,
                    'name' =>  $list->name . ' / ' . $child->name
                ];
                foreach ($child->childrenCategory as $child2){
                    $newList[] = [
                        'id' => $child2->id,
                        'name' =>  $list->name . ' / ' . $child->name. ' / ' . $child2->name
                    ];
                    foreach ($child2->childrenCategory as $child3){
                        $newList[] = [
                            'id' => $child3->id,
                            'name' =>  $list->name . ' / ' . $child->name. ' / ' . $child2->name . ' / ' . $child3->name
                        ];
                    }
                }
            }
        }
        return $this->responseSuccess($newList);
    }
    public function chapterFilter($course_id)
    {
        $filter = CourseDetail::query()
            ->where('course_id', $course_id)
            ->where('type', CourseDetail::TypeValueMappings['chapter'])
            ->get(['id', 'name']);

        return $this->responseSuccess($filter);
    }
    public function courseFilter()
    {
        $lists = Course::query()
            ->where('is_delete', Base::NOT_DELETE)
            ->where('price', '>', 0)
            ->orderByDesc('id')
            ->get(['id', 'name']);
        return $this->responseSuccess($lists);
    }

    /**
     * 深造加密视频数据
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function szCourse(Request $request)
    {
        $courseName = $request->input('courseName');
        $authType = $request->input('authType');
        $limit = (int)$request->input('limit', 20);
        $page = (int)$request->input('page', 1);
        $offset = ($page-1)*20;

        $data = (new ShenzaoService())->findByLicenseCode('', $courseName, $authType);
        $data = is_array($data) ? $data : [];
        $data = array_map(function ($value) {
            return [
                'authType' => $value['authType'],
                'courseName' => $value['courseName'],
                'courseId' => $value['courseId'],
            ];
        }, $data);

        $list = [
            'total' => count($data),
            'data' => array_slice($data, $offset, $limit),
            'current_page' => $page
        ];
        return $this->responseSuccess($list);
    }

    /**
     * 获取分类及其所有子分类ID
     *
     * @param int $categoryId 分类ID
     * @param string $type 分类类型 course/plug/goods
     * @return array
     */
    private function getCategoryWithChildren($categoryId, $type)
    {
        $categoryIds = [$categoryId];

        switch ($type) {
            case 'course':
                $children = CourseCategory::query()
                    ->where('parent_id', $categoryId)
                    ->pluck('id')
                    ->toArray();
                break;
            case 'plug':
                $children = \App\Models\PlugCategory::query()
                    ->where('parent_id', $categoryId)
                    ->pluck('id')
                    ->toArray();
                break;
            case 'goods':
                $children = \App\Models\GoodsCategory::query()
                    ->where('parent_id', $categoryId)
                    ->pluck('id')
                    ->toArray();
                break;
            default:
                $children = [];
        }

        // 递归获取所有子分类
        foreach ($children as $childId) {
            $categoryIds = array_merge($categoryIds, $this->getCategoryWithChildren($childId, $type));
        }

        return array_unique($categoryIds);
    }
}
