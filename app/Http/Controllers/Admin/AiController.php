<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;

use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\NewRequest;
use App\Http\Requests\Admin\CategoryRequest;
use App\Http\Requests\Admin\GalleryRequest;
use App\Http\Requests\Admin\HotRequest;
use App\Http\Requests\Admin\PlugDetailRequest;
use App\Models\Base;
use App\Models\AiNews;
use App\Models\AiGalleryCategory;
use App\Models\AiGallery;
use App\Models\AiHot;
use App\Models\AiRenderStyle;
use App\Models\AiPlug;
use App\Models\AiPlugDetails;
use App\Models\AiVip;
use App\Models\AiVipSetting;
use App\Models\User;
use Illuminate\Http\Request;

class AiController extends Controller
{
    public function news(Request $request)
    {
        $keywords = $request->query('keywords');
        $limit = $request->input('limit', 20);
        $lists = AiNews::query()
            ->when($keywords!='', function ($query) use ($keywords) {
                return $query->where('title', 'like', "%$keywords%");
            })
            ->orderByDesc('id')
            ->paginate($limit);

        return $this->responseSuccess($lists);
    }

    public function newsInfo(AiNews $news)
    {
        return $this->responseSuccess($news);
    }

    public function newsStore(NewRequest $request)
    {
        $news = new AiNews();
        $news->title = $request->input('title');
        $news->images = $request->input('images');
        $news->author = $request->input('author');
        $news->sort = $request->input('sort', 0);
        $news->content = $request->input('content');
        $news->cate_id = $request->input('cate_id');
        if ($news->save()) {

            return $this->responseSuccess(null, '添加成功');
        }
        return $this->responseError('添加失败', ResponseCode::SERVER_ERR);
    }

    public function newsUpdate(NewRequest $request, AiNews $news)
    {
        $data = $request->only(['title','author', 'sort', 'images','content','cate_id']);
        $news->update($data);

        return $this->responseSuccess(null, '编辑成功');
    }

    /**
     * 删除
     * @param News $news
     * @return \Illuminate\Http\JsonResponse
     */
    public function newsDelete(AiNews $news)
    {
        $news->delete();
        return $this->responseSuccess(null, '删除成功');
    }

    public function galleryCategory(Request $request)
    {
        $limit = (int)$request->query('limit', 20);

        $category = AiGalleryCategory::query()
            ->with(['childrenCategory' => function($q){
                return $q -> with(['childrenCategory' => function($q){
                    return $q -> with(['childrenCategory'])->orderByDesc('sort');
                }])->orderByDesc('sort');
            }])
            ->where('parent_id', 0)
            ->orderByDesc('sort')
            ->paginate($limit);

        return $this->responseSuccess($category);
    }

    public function galleryCategoryInfo(AiGalleryCategory $cate)
    {
        return $this->responseSuccess($cate);
    }

    public function galleryCategoryStore(CategoryRequest $request)
    {
        $name = $request->input('name');
        if (mb_strlen($name) > 20){
            throw new ApiException('分类名称最长20个字符', ResponseCode::PARAM_ERR);
        }
        if (AiGalleryCategory::query()->where('name',$name)->exists()){
           throw new ApiException('分类名称已存在', ResponseCode::PARAM_ERR);
        }
        $parentId = $request->input('parent_id', 0);
        if ($parentId) {
            $parentCategory = AiGalleryCategory::query()->find($parentId);
            if (!$parentCategory) {
                throw new ApiException('父级分类不存在', ResponseCode::PARAM_ERR);
            }
            if ($parentCategory->parent_id > 0) {
//                throw new ApiException('不能继续创建子分类', ResponseCode::PARAM_ERR);
            }
        }

        $cate = new AiGalleryCategory();
        $cate->name = $request->input('name');
        $cate->sort = $request->input('sort', 0);
        $cate->parent_id = empty($parentId) ? 0 : $parentId;
        $cate->save();

        return $this->responseSuccess($cate, '添加成功');
    }

    public function galleryCategoryUpdate(CategoryRequest $request, AiGalleryCategory $category)
    {
        $name = $request->input('name');
        if (mb_strlen($name) > 20){
            throw new ApiException('分类名称最长20个字符', ResponseCode::PARAM_ERR);
        }

        $parentId = $request->input('parent_id', 0);
        $category->name = $name;
        $category->sort = $request->input('sort', 0);
        $category->parent_id = empty($parentId) ? 0 : $parentId;
        $category->save();

        return $this->responseSuccess($category, '编辑成功');
    }

    public function galleryCategoryDelete(AiGalleryCategory $category)
    {
        $galleryExist = AiGallery::query()->where('cate_id', $category->id)->where('is_delete',Base::NOT_DELETE)->exists();
        if ($galleryExist){
            throw new ApiException('分类下存在作品，无法删除', ResponseCode::PARAM_ERR);
        }

        $parentExist = AiGalleryCategory::query()->where('parent_id', $category->id)->count();
        if ($parentExist) {
            throw new ApiException('分类下存在子分类，无法删除', ResponseCode::PARAM_ERR);
        }

        try {
            $category->delete();
            return $this->responseSuccess(null, '删除成功');
        } catch (\Exception $exception) {
            throw new ApiException('删除失败', ResponseCode::SERVER_ERR);
        }
    }

    public function gallery(Request $request)
    {
        $keywords = $request->query('keywords');
        $limit = $request->input('limit', 20);
        $lists = AiGallery::query()
            ->with([
                'cate'=>function($q){
                    $q->with('parentCategory');
                }
            ])
            ->when($keywords!='', function ($query) use ($keywords) {
                return $query->where('name', 'like', "%$keywords%");
            })
            ->orderByDesc('id')
            ->paginate($limit);

        return $this->responseSuccess($lists);
    }

    public function galleryInfo(AiGallery $gallery)
    {
        return $this->responseSuccess($gallery);
    }

    public function galleryStore(GalleryRequest $request)
    {
        $data = $request->only(['desc','name','sort', 'cover','cate_id','user_id','images','like_count']);
        $course = AiGallery::query()->create($data);

        return $this->responseSuccess(null, '添加成功');
    }

    public function galleryUpdate(Request $request, AiGallery $gallery)
    {
        $data = $request->only(['desc','name','sort', 'cover','cate_id','user_id','images','like_count']);
        $gallery->update($data);
        
        return $this->responseSuccess(null, '编辑成功');
    }
    
    public function galleryDelete(AiGallery $gallery)
    {
        $gallery->delete();
        return $this->responseSuccess(null, '删除成功');
    }

    public function hot(Request $request)
    {
        $keywords = $request->query('keywords');
        $limit = $request->input('limit', 20);
        $lists = AiHot::query()
            ->when($keywords!='', function ($query) use ($keywords) {
                return $query->where('title', 'like', "%$keywords%");
            })
            ->orderByDesc('id')
            ->paginate($limit);

        return $this->responseSuccess($lists);
    }

    public function hotInfo(AiHot $hot)
    {
        return $this->responseSuccess($hot);
    }

    public function hotStore(HotRequest $request)
    {
        $hot = new AiHot();
        $hot->title = $request->input('title');
        
        if ($hot->save()) {

            return $this->responseSuccess(null, '添加成功');
        }
        return $this->responseError('添加失败', ResponseCode::SERVER_ERR);
    }

    public function hotUpdate(HotRequest $request, AiHot $hot)
    {
        $data = $request->only(['title']);
        $hot->update($data);

        return $this->responseSuccess(null, '编辑成功');
    }

    public function hotDelete(AiHot $hot)
    {
        $hot->delete();
        return $this->responseSuccess(null, '删除成功');
    }

    public function plugInfo(AiPlug $plug)
    {
        return $this->responseSuccess($plug);
    }

    public function plugStore(Request $request)
    {
        $name = $request->input('name');
        if (mb_strlen($name) > 20){
            throw new ApiException('分类名称最长20个字符', ResponseCode::PARAM_ERR);
        }

        $plug = new AiPlug();
        $plug->name = $request->input('name');
        $plug->images = $request->input('images', '');
        $plug->link = $request->input('link', '');
        $plug->web_link = $request->input('web_link', '');
        $plug->save();

        return $this->responseSuccess($plug, '添加成功');
    }

    public function plugUpdate(Request $request, AiPlug $plug)
    {
        $name = $request->input('name');
        if (mb_strlen($name) > 20){
            throw new ApiException('分类名称最长20个字符', ResponseCode::PARAM_ERR);
        }
        $plug->name = $name;
        $plug->name = $request->input('name');
        $plug->images = $request->input('images', '');
        $plug->link = $request->input('link', '');
        $plug->web_link = $request->input('web_link', '');
        $plug->save();

        return $this->responseSuccess($plug, '编辑成功');
    }

    public function plugDelete(AiPlug $plug)
    {

        try {
            $plug->delete();
            return $this->responseSuccess(null, '删除成功');
        } catch (\Exception $exception) {
            throw new ApiException('删除失败', ResponseCode::SERVER_ERR);
        }
    }

    public function plugDetails(Request $request)
    {
        $plug_id = $request->query('plug_id', 1);
        $type = $request->query('type');
        $limit = $request->input('limit', 20);
        $lists = AiPlugDetails::query()
            ->when($plug_id!='', function ($query) use ($plug_id) {
                $query->where('plug_id', $plug_id);
            })
            ->when($type!='', function ($query) use ($type) {
                $query->where('type', $type);
            })
            ->orderByDesc('id')
            ->paginate($limit);

        return $this->responseSuccess($lists);
    }

    public function plugDetailsInfo(AiPlugDetails $detail)
    {
        return $this->responseSuccess($detail);
    }

    public function plugDetailsStore(PlugDetailRequest $request)
    {
        $admin = $request->user('admin');

        $plugDetail = new AiPlugDetails();
        $plugDetail->plug_id = $request->input('plug_id');
        $plugDetail->version_code = $request->input('version_code');
        $plugDetail->platform = $request->input('platform');
        $plugDetail->description = $request->input('description');
        $plugDetail->type = $request->input('type');
        $plugDetail->link = $request->input('link');
        $plugDetail->sort = $request->input('sort', 0);
        $plugDetail->admin_id = $admin->id;
        if ($plugDetail->save()) {
            return $this->responseSuccess(null, '添加成功');
        }
        return $this->responseError('添加失败', ResponseCode::SERVER_ERR);
    }

    public function plugDetailsUpdate(PlugDetailRequest $request, AiPlugDetails $detail)
    {
        $data = $request->only(['version_code','platform', 'description', 'type', 'link','sort']);
        $detail->update($data);
        return $this->responseSuccess(null, '编辑成功');
    }

    public function plugDetailsDelete(AiPlugDetails $detail)
    {
        $detail->delete();
        return $this->responseSuccess(null, '删除成功');
    }

    public function renderStyle(Request $request)
    {
        $limit = (int)$request->query('limit', 20);

        $category = AiRenderStyle::query()
            ->with(['childrenCategory' => function($q){
                return $q -> with(['childrenCategory' => function($q){
                    return $q -> with(['childrenCategory'])->orderByDesc('sort');
                }])->orderByDesc('sort');
            }])
            ->where('parent_id', 0)
            ->orderByDesc('sort')
            ->paginate($limit);

        return $this->responseSuccess($category);
    }

    public function renderStyleInfo(AiRenderStyle $category)
    {
        return $this->responseSuccess($category);
    }

    public function renderStyleStore(CategoryRequest $request)
    {
        $name = $request->input('name');
        if (mb_strlen($name) > 20){
            throw new ApiException('分类名称最长20个字符', ResponseCode::PARAM_ERR);
        }
        if (AiRenderStyle::query()->where('name',$name)->exists()){
           throw new ApiException('分类名称已存在', ResponseCode::PARAM_ERR);
        }
        $parentId = $request->input('parent_id', 0);
        if ($parentId) {
            $parentCategory = AiRenderStyle::query()->find($parentId);
            if (!$parentCategory) {
                throw new ApiException('父级分类不存在', ResponseCode::PARAM_ERR);
            }
            
        }

        $cate = new AiRenderStyle();
        $cate->name = $request->input('name');
        $cate->promt = $request->input('promt', '');
        $cate->fpromt = $request->input('fpromt', '');
        $cate->cover = $request->input('cover', '');
        $cate->sort = $request->input('sort', 0);
        $cate->parent_id = empty($parentId) ? 0 : $parentId;
        $cate->save();

        return $this->responseSuccess($cate, '添加成功');
    }

    public function renderStyleUpdate(CategoryRequest $request, AiRenderStyle $category)
    {
        $name = $request->input('name');
        if (mb_strlen($name) > 20){
            throw new ApiException('分类名称最长20个字符', ResponseCode::PARAM_ERR);
        }
        $parentId = $request->input('parent_id', 0);
        $category->name = $name;
        $category->promt = $request->input('promt', '');
        $category->fpromt = $request->input('fpromt', '');
        $category->cover = $request->input('cover', '');
        $category->sort = $request->input('sort', 0);
        $category->parent_id = empty($parentId) ? 0 : $parentId;
        $category->save();

        return $this->responseSuccess($category, '编辑成功');
    }

    public function renderStyleDelete(AiRenderStyle $category)
    {
        $parentExist = AiRenderStyle::query()->where('parent_id', $category->id)->count();
        if ($parentExist) {
            throw new ApiException('分类下存在子分类，无法删除', ResponseCode::PARAM_ERR);
        }

        try {
            $category->delete();
            return $this->responseSuccess(null, '删除成功');
        } catch (\Exception $exception) {
            throw new ApiException('删除失败', ResponseCode::SERVER_ERR);
        }
    }

    public function vipSettingInfo(AiVipSetting $setting)
    {
        return $this->responseSuccess($setting);
    }

    public function vipSettingStore(Request $request)
    {
        $title = $request->input('title_pre');
        if (mb_strlen($title) > 20){
            throw new ApiException('名称最长20个字符', ResponseCode::PARAM_ERR);
        }

        $setting = new AiVipSetting();
        $setting->title_pre = $request->input('title_pre');
        $setting->save();

        return $this->responseSuccess($setting, '添加成功');
    }

    public function vipSettingUpdate(Request $request, AiVipSetting $setting)
    {
        $data = $request->only([
            'title_pre', 'title_suff', 
            'title_sub1', 'title_sub2', 'title_sub3', 
            'vip_price1', 'vip_price2', 'vip_price3', 'vip_price4',
            'vip1_sub', 'vip2_sub', 'vip3_sub', 'vip4_sub',
            'vip1_suff1','vip1_suff2','vip1_suff3','vip1_suff4',
            'vip2_suff1','vip2_suff2','vip2_suff3','vip2_suff4',
            'vip3_suff1','vip3_suff2','vip3_suff3','vip3_suff4',
            'vip4_suff1','vip4_suff2','vip4_suff3','vip4_suff4',
            'bottom_title_pre','bottom_title_suff',
            'title_tag1','title_tag2','title_tag3','title_tag4','title_tag5','title_tag6',
        ]);
        $setting->update($data);

        return $this->responseSuccess($setting, '编辑成功');
    }

    public function vip(Request $request)
    {
        $keywords = $request->query('keywords');
        $limit = $request->input('limit', 20);
        $lists = AiVip::query()
            ->when($keywords!='', function ($query) use ($keywords) {
                $query->whereHas('user', function ($q) use ($keywords) {
                    $q->where('name', 'like', "%{$keywords}%");
                });
            })
            ->with('user')
            ->orderByDesc('id')
            ->paginate($limit);

        return $this->responseSuccess($lists);
    }

    public function vipInfo(AiVip $vip)
    {
        $vip->load('user');
        return $this->responseSuccess($vip);
    }

    public function vipStore(Request $request)
    {
        $data = $request->only(['user_id','permanent','endtime']);
        $vip = AiVip::query()->create($data);

        return $this->responseSuccess(null, '添加成功');
    }

    public function vipUpdate(Request $request, AiVip $vip)
    {
        $data = $request->only(['user_id','permanent','endtime']);
        $vip->update($data);
        
        return $this->responseSuccess(null, '编辑成功');
    }
    
    public function vipDelete(AiVip $vip)
    {
        $vip->delete();
        return $this->responseSuccess(null, '删除成功');
    }

    public function gcFilter(Request $request)
    {
        $parentId = $request->input('parent_id', 0);

        if(!$parentId){
            $category = AiGalleryCategory::query()
                ->with(['childrenCategory' => function($q){
                    return $q -> with(['childrenCategory' => function($q){
                        return $q->orderByDesc('sort');
//                        return $q -> with(['childrenCategory'])->orderByDesc('sort');
                    }])->orderByDesc('sort');
                }])
                ->where('parent_id', 0)
                ->orderByDesc('sort')
                ->get();
            $lists = $this->getItem($category);
            return $this->responseSuccess($lists);
        }

        $lists = AiGalleryCategory::query()
            ->where(function ($query) use ($parentId) {
                if ($parentId) {
                    $query->where('parent_id', $parentId);
                } else {
//                    $query->where('parent_id', 0);
                }
            })
            ->get(['id', 'name']);

        return $this->responseSuccess($lists);
    }

    public function rsFilter(Request $request)
    {
        $parentId = $request->input('parent_id', 0);

        if(!$parentId){
            $category = AiRenderStyle::query()
                ->with(['childrenCategory' => function($q){
                    return $q -> with(['childrenCategory' => function($q){
                        return $q->orderByDesc('sort');
//                        return $q -> with(['childrenCategory'])->orderByDesc('sort');
                    }])->orderByDesc('sort');
                }])
                ->where('parent_id', 0)
                ->orderByDesc('sort')
                ->get();
            $lists = $this->getItem($category);
            return $this->responseSuccess($lists);
        }

        $lists = AiRenderStyle::query()
            ->where(function ($query) use ($parentId) {
                if ($parentId) {
                    $query->where('parent_id', $parentId);
                } else {
//                    $query->where('parent_id', 0);
                }
            })
            ->get(['id', 'name']);

        return $this->responseSuccess($lists);
    }

    public function getItem($category)
    {
        $list = [];
        foreach ($category as $k => $v){
            $list[] = $v;
            if($v['childrenCategory']){
                foreach ($v['childrenCategory'] as $k2 =>$v2){
                    $v2['name'] = '——  '.$v2['name'];
                    $list[] = $v2;
                    foreach ($v2['childrenCategory'] as $k3 =>$v3){
                        $v3['name'] = '————  '.$v3['name'];
                        $list[] = $v3;
//                        foreach ($v3['childrenCategory'] as $k4 =>$v4){
//                            $v4['name'] = '——————  '.$v4['name'];
//                            $list[] = $v4;
//                        }
                    }
                }
            }
        }
        return($list);;
    }
}
