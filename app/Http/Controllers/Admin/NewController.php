<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\NewRequest;
use App\Models\News;
use App\Models\TagList;
use Illuminate\Http\Request;

class NewController extends Controller
{
    public function index(Request $request)
    {
        $keywords = $request->query('keywords');
        $limit = $request->input('limit', 20);
        $lists = News::query()
            ->when($keywords!='', function ($query) use ($keywords) {
                return $query->where('title', 'like', "%$keywords%");
            })
            ->with(['tagListInfo'])
            ->orderByDesc('id')
            ->paginate($limit);
        $lists->getCollection()->transform(function ($item) {
            $item->tag = $item->tagListInfo->pluck('tag');
            return $item;
        });

        return $this->responseSuccess($lists);
    }

    public function info(News $news)
    {
        $news->load('tagList');
        $tagList = $news->tagList;
        $tags = [];
        if ($tagList) {
            foreach ($tagList as $item) {
                $tags[] = $item->tag;
            }
        }
        $news -> tag = $tags;
        return $this->responseSuccess($news);
    }

    public function store(NewRequest $request)
    {
        $news = new News();
        $news->title = $request->input('title');
        $news->images = $request->input('images');
        $news->author = $request->input('author');
        $news->sort = $request->input('sort', 0);
        $news->content = $request->input('content');
        if ($news->save()) {
            $info = TagList::delTagInfo(['aid' => $news->id, 'tag' => $request->input('tag', []), 'type' => 'news']);
            $info = TagList::saveTagInfo(['aid' => $news->id, 'tag' => $request->input('tag', []), 'type' => 'news']);

            return $this->responseSuccess(null, '添加成功');
        }
        return $this->responseError('添加失败', ResponseCode::SERVER_ERR);
    }

    public function update(NewRequest $request, News $news)
    {
        $data = $request->only(['title','author', 'sort', 'images','content']);
        $news->update($data);

        $info = TagList::delTagInfo(['aid' => $news->id, 'tag' => $request->input('tag', []), 'type' => 'news']);
        $info = TagList::saveTagInfo(['aid' => $news->id, 'tag' => $request->input('tag', []), 'type' => 'news']);

        return $this->responseSuccess(null, '编辑成功');
    }

    /**
     * 删除
     * @param News $news
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(News $news)
    {
        $news->delete();
        return $this->responseSuccess(null, '删除成功');
    }
}
