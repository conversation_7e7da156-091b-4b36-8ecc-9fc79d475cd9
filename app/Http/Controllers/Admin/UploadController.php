<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Services\Oss;
use Illuminate\Http\Request;

class UploadController extends Controller
{
    public function token()
    {
        $token = Oss::getInstance()->getTempAccess();

        return $this->responseSuccess($token);
    }
    public function upload(Request $request)
    {
        if (!$request->hasFile('file'))
        {
            throw new ApiException('请选择文件', ResponseCode::PARAM_ERR);
        }
        $file= $request->file('file');
        if (!$file->isValid()) {
            throw new ApiException('无效的文件', ResponseCode::PARAM_ERR);
        }
        $extension = $file->getClientOriginalExtension();
        if (!in_array($extension, ['xls','xlsx','json'])) {
            throw new ApiException('上传文件类型不正确，请上传正确文件', ResponseCode::PARAM_ERR);
        }
        // 文件名
        $fileName = $file->getClientOriginalName();
        // 生成新的统一格式的文件名
        $newFileName = date('Y-m-d').'/'.md5(time()) . '.' . $extension;
        // 文件保存路径
        $savePath = 'import/'. $newFileName;
        // 否则执行保存操作，保存成功将访问路径返回给调用方
        if ($file->storePubliclyAs('import', $newFileName, ['disk' => 'public'])) {
            return $this->responseSuccess([
                'path'=>'/storage/' . $savePath,
                'name'=>$fileName,
                'extension'=>$extension
            ]);
        }
    }
}
