<?php

namespace App\Http\Controllers\Admin;

use Illuminate\Http\Request;
use App\Models\OperationLogs;
use App\Http\Controllers\Controller;

class OperationLogsController extends Controller
{
    /**
     * 系统日志
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $limit = (int) $request->query('limit', 20);

        $items = OperationLogs::query()
            ->orderByDesc('id')
            ->paginate($limit);

        return $this->responseSuccess($items);
    }
}
