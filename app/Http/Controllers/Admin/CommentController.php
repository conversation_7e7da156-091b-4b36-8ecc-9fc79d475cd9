<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\Base;
use App\Models\Comment;
use App\Models\CommentReply;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class CommentController extends Controller
{
    /**
     * 评论列表
     *
     * @param Request $request
     * @param $topic
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $target_id = $request->input('target_id');
        $target_type = $request->input('target_type');
        $limit = $request->input('limit', 15);

        $comments = Comment::query()
            ->with(['reply:id,user_id,comment_id', 'user:id,name,avatar', 'reply.user:id,name,avatar'])
            ->where('target_id', $target_id)
            ->where('target_type', $target_type)
            ->notDelete()
            ->orderByDesc('created_at')
            ->select(['content','created_at', 'id', 'target_id', 'target_type', 'user_id'])
            ->paginate($limit);

        foreach ($comments as $comment) {
            $comment->replys_count = count($comment->reply);
        }

        return $this->responseSuccess($comments);
    }

    /**
     * 回复列表
     * @param Request $request
     * @param $comment
     * @return \Illuminate\Http\JsonResponse
     */
    public function reply(Request $request, $comment)
    {
        $limit = $request->input('limit', 15);

        $comments = CommentReply::query()
            ->with(['user:id,name,phone,avatar', 'parent:id,user_id', 'parent.user:id,name,phone,avatar'])
            ->where('comment_id', $comment)
            ->orderBy('id')
            ->notDelete()
            ->paginate($limit);

        return $this->responseSuccess($comments);
    }

    /**
     * 评论删除
     * @param Comment $comment
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function commentDestroy(Comment $comment)
    {
        try {
            DB::beginTransaction();

            CommentReply::query()->where('comment_id', $comment->id)->update([
                'is_delete' => Base::HAS_DELETE
            ]);

            $comment->is_delete = Base::HAS_DELETE;
            $comment->save();

            DB::commit();
            return $this->responseSuccess(null, '删除成功');
        } catch (\Exception $exception) {
            DB::rollBack();
            Log::error($exception);
            throw new ApiException('删除失败', ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 回复删除
     * @param CommentReply $reply
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function replyDestroy(CommentReply $reply)
    {
        $reply->is_delete = Base::HAS_DELETE;
        if (!$reply->save()) {
            throw new ApiException('删除失败', ResponseCode::SERVER_ERR);
        }

        return $this->responseSuccess(null, '删除成功');
    }
}
