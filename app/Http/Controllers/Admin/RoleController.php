<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Role;
use Illuminate\Http\Request;
use App\Http\Requests\Admin\RoleRequest;

class RoleController extends Controller
{
    /**
     * 角色列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $name = $request->query('name');
        $alias = $request->query('alias');

        $roles = Role::query()
            ->with('permissions')
            ->when($name, function ($query) use ($name) {
                return $query->where('name', 'like', '%' . $name . '%');
            })
            ->when($alias, function ($query) use ($alias) {
                return $query->where('alias', 'like', '%' . $alias . '%');
            })
            ->get();

        return $this->responseSuccess($roles);
    }

    /**
     * 创建角色
     *
     * @param RoleRequest $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(RoleRequest $request)
    {
        Role::create($request->all());

        return $this->responseSuccess(null, '创建角色成功');
    }

    /**
     * 修改角色
     *
     * @param RoleRequest $request
     * @param Role $role
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(RoleRequest $request, Role $role)
    {
        $data = $request->except('permissions');
        $role->update($data);

        $permissionIds = $request->input('permissions');
        $role->permissions()->sync($permissionIds);

        return $this->responseSuccess(null, '修改角色成功');
    }

    /**
     * 角色详情
     *
     * @param Role $role
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Role $role)
    {
        $role->load('permissions');

        return $this->responseSuccess($role);
    }

    /**
     * 删除角色
     *
     * @param Role $role
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function destroy(Role $role)
    {
        foreach ($role->admins as $admin) {
            $admin->roles()->detach($role->id);
        }
        $role->permissions()->detach($role->id);
        $role->delete();

        return $this->responseSuccess(null, '删除角色成功');
    }
}
