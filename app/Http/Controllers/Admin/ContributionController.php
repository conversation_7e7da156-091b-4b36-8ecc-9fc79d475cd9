<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CourseRequest;
use App\Jobs\PopularizeUserPushJob;
use App\Jobs\ShenzaoAuthorize;
use App\Models\Base;
use App\Models\Course;
use App\Models\CourseCategory;
use App\Models\CourseDetail;
use App\Models\Goods;
use App\Models\Plug;
use App\Models\PlugDetail;
use App\Models\PlugIntro;
use App\Models\TagList;
use App\Models\User;
use App\Models\UserCourse;
use App\Models\UserFile;
use App\Models\UserGoods;
use App\Models\UserPlug;
use App\Models\Wallet;
use App\Models\WalletLog;
use App\Services\ShenzaoService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ContributionController extends Controller
{
    public function getList($type, Request $request)
    {
        $keywords = $request->query('keywords');
        $is_check = $request->query('is_check');
        $limit = $request->input('limit', 20);
        //课件
        if($type == 'course'){
            $model = UserFile::query();
        } else if($type == 'plug'){ //插件
            $model = UserPlug::query();
        } else if($type == 'goods'){ //周边
            $model = UserGoods::query();
        }
        $lists = $model
            -> with([ 'tagListInfo'])
            -> when($type, function ($q) use ($type){
                if($type == 'course'){
                    return $q -> with(['cate' => function($q1){
                        return $q1 -> with('parentCategory');
                    }, 'suggestUser']);
                } else if($type == 'plug'){ //插件
                    return $q -> with(['cate' => function($q1){
                        return $q1 -> with('parentCategory');
                    }, 'suggestUser']);
                } else if($type == 'goods'){ //周边
                    return $q -> with(['cate' => function($q1){
                        return $q1 -> with('parentCategory');
                    }, 'suggestUser']);
                }
            })
            ->when($keywords!='', function ($query) use ($keywords) {
                return $query->where('name', 'like', "%$keywords%");
            })
            ->when($is_check, function ($query) use ($is_check) {
                return $query->where('is_check', $is_check);
            })
            -> where('status', '<>', 0)
            ->orderByDesc('id')
            ->paginate($limit);
        foreach ($lists as $k => $v){
            if($type == 'goods'){ //周边
                $v['images'] = json_decode($v['images'], true);
            }

        }

        return $this->responseSuccess($lists);
    }



    /**
     * 课程删除
     * @param Course $course
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete($type, $id)
    {
        if($type == 'course'){
            $model = UserFile::query();
        } else if($type == 'plug'){ //插件
            $model = UserPlug::query();
        } else if($type == 'goods'){ //周边
            $model = UserGoods::query();
        }
        $data = $model -> where('id', $id)->delete();
        return $this->responseSuccess(null, '删除成功');
    }



    public function update($type, $id , Request $request)
    {
        $data = $request->only(['is_check','errors']);
        $model = null;
        if($type == 'course'){
            $model = UserFile::query();
        } else if($type == 'plug'){ //插件
            $model = UserPlug::query();
        } else if($type == 'goods'){ //周边
            $model = UserGoods::query();
        }
        $model = $model -> find($id);
        if($type=='wallet'){
            return $this->userwallet($model);
        }
        $model -> is_check = $request -> is_check;
        if($request -> is_check==2){
            if($type == 'course'){
                $vid = $this->saveCourse($model);
            } else if($type == 'plug'){ //插件
                $vid = $this->saveplug($model);
            } else if($type == 'goods'){ //周边
                $vid = $this->savegoods($model);
            }
            $model -> vid = $vid;
            $model -> save();
            TagList::query()
                -> where('uid', $model['id'])
                -> where('type', $type)
                -> update(['aid'=>$vid]);
//            return $this->responseSuccess(['model'=>$model, 'vid' => $vid], '审核成功');
        } else {
            $model -> errors = $request -> errors;
        }
        $model -> save();

        return $this->responseSuccess($model, '审核成功');
    }
    public function userwallet($type, $id , Request $request){
        $data = $request->only(['wallet']);
        $model = null;
        if($type == 'course'){
            $model = UserFile::query();
        } else if($type == 'plug'){ //插件
            $model = UserPlug::query();
        } else if($type == 'goods'){ //周边
            $model = UserGoods::query();
        }
        $model = $model -> find($id);
        $user = User::query() -> find($model['user_id']);
        $wallet = Wallet::query() -> where('user_id', $user['id']) -> first();
        $currBalance = $wallet -> balance + $data['wallet'];

        $walletLog = new WalletLog();
        $walletLog -> title = '投稿打赏';
        $walletLog -> user_id = $user['id'];
        $walletLog -> target_id = $model['id'];
        $walletLog -> target_type = 'wallet';
        $walletLog -> amount = $data['wallet'];
        $walletLog -> action = 1;
        $walletLog -> status = 1;
        $walletLog -> currency = 'integral';
        $walletLog -> extend = ['balance' => $currBalance];
        $walletLog -> type = 'add';
        $walletLog -> save();

        if(!is_null($wallet)){
            $wallet -> balance = $wallet -> balance + $data['wallet'];
            $wallet -> income = $wallet -> income+ $data['wallet'];
            $wallet -> save();
        } else {
            $wallet = new Wallet();
            $wallet -> user_id = $user['id'];
            $wallet -> income = $data['wallet'];
            $wallet -> balance = $data['wallet'];
            $wallet -> save();
        }
        $model -> dashang = $data['wallet'];
        $model -> save();
        return $this->responseSuccess($data, '打赏成功');
    }
    public function saveCourse($data){
        $course = new Course();
        $course -> name = $data['name'];
        $course -> images = $data['images'];
        $course -> intro = $data['intro'];
        $course -> explain = $data['explain'];
        $course -> cate_id = $data['cate_id'];
        $course -> price = $data['price']?:0;
        $course -> author = $data['author'];
        $course -> market_price = 0;
        $course -> author_id = $data['user_id'];
        $course -> user_id = $data['user_id'];
        $course -> is_publish = 1;
        $course -> sort = 0;
        $course -> save();
        if(!is_null($data['detail'])){
            $detail = json_decode($data['detail'], true);
            $arr = [];
            foreach ($detail as $k => $v){
                $plugDetail = new CourseDetail();
                $plugDetail -> course_id = $course -> id;
                $plugDetail -> name = $v['name'];
                $plugDetail -> images = $v['images'];
                $plugDetail -> sort = $k;
                $plugDetail -> type = $v['type'];
                $plugDetail -> link = $v['fileList']&&count($v['fileList'])>0?$v['fileList'][0]['name']:'';
                $plugDetail -> chapter_id = $v['parent']?$arr[$v['parent']-1]['id']:0;
                $plugDetail -> save();
                $arr[$k] = $plugDetail;
            }
        }
        return $course['id'];
    }
    public function saveplug($data){
        $plug = new Plug();
        $plug -> name = $data['name'];
        $plug -> images = $data['images'];
        $plug -> intro = $data['intro'];
        $plug -> price = $data['price']?:0;
        $plug -> sort = 0;
        $plug -> admin_id = 0;
        $plug -> author = $data['author'];
        $plug -> cate_id = $data['cate_id'];
        $plug -> state = 1;
        $plug -> external_link = $data['external_link'];
        $plug -> web_link = $data['web_link'];
        $plug -> user_id = $data['user_id'];

        $plug -> save();
        if(!is_null($data['detail'])){
            $detail = json_decode($data['detail'], true);
            $arr = [];
            foreach ($detail as $k => $v){
                $plugDetail = new PlugDetail();
                $plugDetail ->plug_id = $plug['id'];
                $plugDetail ->version_name = $v['version_name']??'';
                $plugDetail ->version_code = $v['version_code']??'';
                $plugDetail ->platform = $v['platform']??'';
                $plugDetail ->description = $v['description']??'';
                $plugDetail ->type = $v['type']??'';
                $plugDetail ->link = isset($v['fileList'])&&$v['fileList']&&(count($v['fileList'])>0)?$v['fileList'][0]['name']:'';
                $plugDetail ->user_id = $data['user_id']??'';
                $plugDetail -> save();
            }
        }
        if(!is_null($data['intros'])){
            $intros = json_decode($data['intros'], true);
            $arr = [];
            foreach ($intros as $k => $v){
                $plugDetail = new PlugIntro();
                $plugDetail ->plug_id = $plug['id'];
                $plugDetail ->content = '';
                $plugDetail ->images = $v['name'];
                $plugDetail ->sort = 0;
                $plugDetail ->admin_id = 0;
                $plugDetail -> save();
            }
        }
        return ($plug['id']);
    }
    public function savegoods($data){
        $goods = new Goods();
        $goods -> name = $data['name'];
        $goods -> cover = $data['cover'];
        $goods -> images = json_decode($data['images'], true);
        $goods -> intro = $data['intro']?:'';
        $goods -> desc = $data['desc'];
        $goods -> price = $data['price']?:0;
        $goods -> market_price = 0;
        $goods -> status = 1;
        $goods -> cate_id = $data['cate_id'];
        $goods -> user_id = $data['user_id'];
        $goods -> material = $data['material'];
        $goods -> sort = 0;
        $goods -> save();
        return ($goods->id);
    }


}
