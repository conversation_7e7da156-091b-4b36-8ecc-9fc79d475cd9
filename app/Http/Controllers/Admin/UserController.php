<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\UserRequest;
use App\Models\Base;
use App\Models\CourseCategory;
use App\Models\GoodsCategory;
use App\Models\PlugCategory;
use App\Models\User;
use App\Models\UserRoleCate;
use App\Models\Wallet;
use App\Models\WalletLog;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Rap2hpoutre\FastExcel\FastExcel;

class UserController extends Controller
{
    /**
     * 用户列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $keywords = $request->query('keywords');
        $state = $request->query('state');
        $start = $request->query('start_time');
        $end = $request->query('end_time');
        $limit = $request->query('limit', 20);
        $sortType = $request->query('type'); // 排序类型：income、balance、expend

        $query = User::query()
            ->where('is_delete', Base::NOT_DELETE)
            ->when($keywords, function ($query) use ($keywords) {
                if (validateChinaPhoneNumber($keywords)) {
                    return $query->where('phone', (string)$keywords);
                }
                return $query->where('name', 'like', sprintf('%%%s%%', $keywords))->orWhere('id', (int)$keywords);
            })
            ->when($start, function ($query) use ($start) {
                $query->where('created_at', '>=', $start);
            })
            ->when($end, function ($query) use ($end) {
                $query->where('created_at', '<', $end);
            })
            ->when($state != '', function ($query) use ($state) {
                $query->where('state', $state);
            })
            ->where('is_delete', Base::NOT_DELETE);

        // 根据排序类型添加排序逻辑
        if ($sortType && in_array($sortType, ['income', 'balance', 'expend'])) {
            // 关联钱包表进行排序
            $query->leftJoin('wallets', 'users.id', '=', 'wallets.user_id')
                  ->orderByDesc("wallets.{$sortType}")
                  ->select('users.*');
        } else {
            // 默认按ID倒序
            $query->orderByDesc('id');
        }

        $users = $query->paginate($limit)->toArray();

        // 用户资产处理
        $userIds = collect($users['data'])->pluck('id')->toArray();
        $wallets = Wallet::query()->whereIn('user_id', $userIds)->get();
        foreach ($users['data'] as &$user) {
            $wallet = $wallets->where('user_id', $user['id'])->first();
            $user['income'] = $wallet->income ?? '0.00';
            $user['balance'] = $wallet->balance ?? '0.00';
            $user['expend'] = $wallet->expend ?? '0.00';
            $user['plug'] = UserRoleCate::query() -> where('type', 'plug')->where('user_id', $user['id']) ->pluck('cate_id') -> toArray();
            $user['goods'] = UserRoleCate::query() -> where('type', 'goods')->where('user_id', $user['id']) ->pluck('cate_id') -> toArray();
            $user['course'] = UserRoleCate::query() -> where('type', 'course')->where('user_id', $user['id']) ->pluck('cate_id') -> toArray();
        }

        return $this->responseSuccess($users);
    }

    public function cateTree()
    {
        $list = PlugCategory::query() ->get();
        $list2 = CourseCategory::query() ->get();
        $list3 = GoodsCategory::query() ->get();
        $data = [
            'plug' => $list,
            'course' => $list2,
            'goods' => $list3,
        ];
        return $this->responseSuccess($data, '请求成功');
        dd($list->toArray(), $list2->toArray(), $list3 ->toArray());
    }

    /**
     * 修改用户信息
     *
     * @param UserRequest $request
     * @param User $user
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function update(UserRequest $request, User $user)
    {
        try {
            $data = $request->only(['state', 'is_lecturer']);
            $roles = [];
            $roles['goods'] = $request -> goods;
            $roles['plug'] = $request -> plug;
            $roles['course'] = $request -> course;
//            $roles = $request->only(['goods', 'plug', 'course']);
            $userid = $request->id;
            $user->update($data);
            UserRoleCate::query() -> where('user_id', $userid) -> delete();
            $info = [];
            if($roles['goods']){
                foreach ($roles['goods'] as $k => $v){
                    $info[] = [
                        'user_id' => $userid,
                        'cate_id' => $v,
                        'type' => 'goods',
                    ];
                }
            }
            if($roles['plug']) {
                foreach ($roles['plug'] as $k => $v){
                    $info[] = [
                        'user_id' => $userid,
                        'cate_id' => $v,
                        'type' => 'plug',
                    ];
                }
            }
            if($roles['course']) {
                foreach ($roles['course'] as $k => $v){
                    $info[] = [
                        'user_id' => $userid,
                        'cate_id' => $v,
                        'type' => 'course',
                    ];
                }
            }
            foreach ($info as $k => $v){
                $usrole = new UserRoleCate();
                $usrole -> user_id = $v['user_id'];
                $usrole -> cate_id = $v['cate_id'];
                $usrole -> type = $v['type'];
                $usrole -> save();
            }
//            UserRoleCate::query() -> insert($info);
            return $this->responseSuccess(null, '修改成功');
        } catch (\Throwable $e) {
            Log::error("修改失败:" . $e->getMessage());
            throw new ApiException('修改失败'.$e->getMessage(), ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 删除用户
     *
     * @param User $user
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function closeAccount(User $user)
    {
        if (!$user) {
            throw new ApiException('用户信息错误', ResponseCode::NOT_FOUND);
        }
        if ($user->is_delete == Base::HAS_DELETE) {
            throw new ApiException('该用户已被删除', ResponseCode::NOT_FOUND);
        }
        try {
            DB::beginTransaction();
            $user->phone = time() . 'DEL' . $user->phone;
            $user->is_delete = Base::HAS_DELETE;
            $user->api_token = '';
            $user->save();
            DB::commit();
            return $this->responseSuccess(null, '删除成功');
        } catch (\Throwable $e) {
            DB::rollBack();
            Log::error("删除失败:" . $e->getMessage());

            throw new ApiException('删除失败', ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 用户信息表格导出
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function exportUser(Request $request)
    {
        $users = $this->generator($request);

        try {
            $fileName = 'export/' . sprintf('%s.xlsx', date('YmdHis') . '-用户列表导出');
            $stateMap = [
                0 => '正常', 1 => '禁用'
            ];
            (new FastExcel($users))->export($fileName, function ($user) use ($stateMap) {
                return [
                    '用户ID' => $user['id'],
                    '用户名' => $user['name'],
                    '账号' => $user['phone'],
                    '资产累计' => $user['wallet']['income'] ?? '0.00',
                    '资产余额' => $user['wallet']['balance'] ?? '0.00',
                    '资产支出' => $user['wallet']['expend'] ?? '0.00',
                    '注册时间' => Carbon::parse($user['created_at'])->format('Y-m-d H:i:s'),
                    '账号状态' => $stateMap[$user['state']],
                ];
            });
            return $this->responseSuccess(['filename' => $fileName]);
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
            throw new ApiException('导出失败', ResponseCode::SERVER_ERR);
        }
    }

    public function generator($request)
    {
        $keywords = $request->query('keywords');
        $state = $request->query('state');
        $start = $request->query('start_time');
        $end = $request->query('end_time');
        $sortType = $request->query('type'); // 排序类型

        $query = User::query()
            ->with('wallet')
            ->where('is_delete', Base::NOT_DELETE)
            ->when($keywords, function ($query) use ($keywords) {
                if (validateChinaPhoneNumber($keywords)) {
                    return $query->where('phone', (string)$keywords);
                }
                return $query->where('name', 'like', sprintf('%%%s%%', $keywords))->orWhere('id', (int)$keywords);
            })
            ->when($start, function ($query) use ($start) {
                $query->where('created_at', '>=', $start);
            })
            ->when($end, function ($query) use ($end) {
                $query->where('created_at', '<', $end);
            })
            ->when($state != '', function ($query) use ($state) {
                $query->where('state', $state);
            })
            ->where('is_delete', Base::NOT_DELETE);

        // 根据排序类型添加排序逻辑
        if ($sortType && in_array($sortType, ['income', 'balance', 'expend'])) {
            $query->leftJoin('wallets', 'users.id', '=', 'wallets.user_id')
                  ->orderByDesc("wallets.{$sortType}")
                  ->select('users.*');
        } else {
            $query->orderByDesc('id');
        }

        foreach ($query->cursor() as $item) {
            yield $item;
        }
    }

    public function authorFilter()
    {
        $lists = User::query()
            ->where('is_delete', Base::NOT_DELETE)
            ->where('is_lecturer', 1)
            ->orderByDesc('id')
            ->get(['id', 'name', 'phone']);
        return $this->responseSuccess($lists);
    }

    public function userFilter()
    {
        $lists = User::query()
            ->where('is_delete', Base::NOT_DELETE)
            ->whereDoesntHave('vip', function ($query) {
            })
            ->orderByDesc('id')
            ->get(['id', 'name', 'phone']);
        return $this->responseSuccess($lists);
    }

    /**
     * 用户资产余额清除
     * @param $userId
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function clearWallet($userId)
    {
        $wallet = Wallet::query()->where('user_id', $userId)->first();
        if (!$wallet || $wallet->balance <= 0) {
            throw new ApiException('用户无资产余额，无需清除', ResponseCode::SERVER_ERR);
        }
        $balanceAmount = $wallet->balance;

        try {
            DB::beginTransaction();
            $wallet->update([
                'balance' => DB::raw('balance - ' . $balanceAmount),
                'expend' => DB::raw('expend + ' . $balanceAmount),
            ]);

            WalletLog::query()->create([
                'title' => '系统扣除',
                'target_type' => '',
                'target_id' => 0,
                'amount' => $balanceAmount,
                'user_id' => $userId,
                'currency' => 'integral',
                'action' => WalletLog::ActionMappings['expend'],
                'status' => WalletLog::StatusMappings['success'],
                'type' => WalletLog::TypeMappings['system'],
                'extend' => ['balance' => 0],
            ]);

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();
            throw new ApiException('资产扣除失败', ResponseCode::SERVER_ERR);
        }

        return $this->responseSuccess(null, '扣除成功');
    }
}
