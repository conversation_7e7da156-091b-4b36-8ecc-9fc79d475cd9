<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\PlugRequest;
use App\Jobs\PopularizeUserPushJob;
use App\Models\Plug;
use App\Models\PlugCategory;
use App\Models\TagList;
use App\Models\UserPlug;
use Illuminate\Http\Request;

class PlugController extends Controller
{
    public function index(Request $request)
    {
        $admin = $request->user('admin');
        $keywords = $request->query('keywords');
        $state = $request->query('state');
        $category_id = $request->query('category_id'); // 分类ID筛选
        $limit = $request->input('limit', 20);
        $lists = Plug::query()
            ->with(['cate:id,name', 'suggestUser:id,name', 'tagListInfo',
                'cate' => function ($q) {
                    $q->with('parentCategory');
                }])
            ->when($keywords!='', function ($query) use ($keywords) {
                return $query->where('name', 'like', "%$keywords%");
            })
            ->when($state!='', function ($query) use ($state) {
                return $query->where('state', $state);
            })
            ->when($category_id!='', function ($query) use ($category_id) {
                // 获取当前分类及所有子分类ID
                $categoryIds = $this->getCategoryWithChildren($category_id, 'plug');
                return $query->whereIn('cate_id', $categoryIds);
            })
            ->when($admin->user_id > 0 ,function ($query) use ($admin) {
                $query->where('admin_id', $admin->id);
            })
            ->orderByDesc('id')
            ->paginate($limit);

        return $this->responseSuccess($lists);
    }

    public function info(Plug $plug)
    {
        $plug->load('tagList');
        $tagList = $plug->tagList;
        $tags = [];
        if ($tagList) {
            foreach ($tagList as $item) {
                $tags[] = $item->tag;
            }
        }
        $plug -> tag = $tags;
        return $this->responseSuccess($plug);
    }

    public function store(PlugRequest $request)
    {
        $admin = $request->user('admin');


        // 是否支持重复购买：1-支持，0-不支持
        $repeatBuy = $request->input('repeat_buy', 0);
        $repeatBuy = $repeatBuy == 1 ? 1 : 0;

        $plug = new Plug();
        $plug->name = $request->input('name');
        $plug->images = $request->input('images');
        $plug->cate_id = $request->input('cate_id');
        $plug->price = $request->input('price');
        $plug->author = '';
        $plug->state = $request->input('state', 0);
        $plug->sort = $request->input('sort', 0);
        $plug->give_integral = $request->input('give_integral', 0);
        $plug->is_integral = $request->input('is_integral', 0);
        $plug->integral_num = $request->input('integral_num', 0);
        $plug->download_package = $request->input('download_package', 0);
        $plug->download_manual = $request->input('download_manual', 0);
        $plug->download_case = $request->input('download_case', 0);
        $plug->intro = $request->input('intro');
        $plug->web_link = $request->input('web_link', '');
        $plug->web_link_price = $request->input('web_link_price', 0);
        $plug->repeat_buy = $repeatBuy;
        $plug->user_id = $request->input('user_id', 0);
        $plug->admin_id = $admin->id;
        if ($plug->save()) {
            $info = TagList::delTagInfo(['aid' => $plug->id, 'tag' => $request->input('tag', []), 'type' => 'plug']);
            $info = TagList::saveTagInfo(['aid' => $plug->id, 'tag' => $request->input('tag', []), 'type' => 'plug']);

            $send = (int)$request->input('send', 0);
            if ($send && $admin->user_id==0) {
                dispatch(new PopularizeUserPushJob($plug->id, 'plug', [
                    'commodity' => $plug->name . '|插件',
                    'money' => $plug->price,
                    'money2' => $plug->price,
                ]));
            }

            return $this->responseSuccess(null, '添加成功');
        }
        return $this->responseError('添加失败', ResponseCode::SERVER_ERR);
    }

    public function update(PlugRequest $request, Plug $plug)
    {
        $admin = $request->user('admin');
        if ($admin->user_id > 0 && $admin->id != $plug->admin_id) {
            throw new ApiException('无操作权限', ResponseCode::NOT_FOUND);
        }
        $data = $request->only([
            'price',
            'name',
            'give_integral',
            'is_integral',
            'integral_num',
            'download_package',
            'download_manual',
            'download_case',
            'sort',
            'images',
            'cate_id',
            'intro',
            'web_link',
            'web_link_price',
            'repeat_buy',
            'user_id',
            'state',
        ]);
        if (empty($data['web_link'])) {
            $data['web_link'] = '';
        }
        // 是否支持重复购买：1-支持，0-不支持
        $data['repeat_buy'] = !empty($data['repeat_buy']) && $data['repeat_buy'] == 1 ? 1 : 0;

        $plug->update($data);
        $info = TagList::delTagInfo(['aid' => $plug->id, 'tag' => $request->input('tag', []), 'type' => 'plug']);
        $info = TagList::saveTagInfo(['aid' => $plug->id, 'tag' => $request->input('tag', []), 'type' => 'plug']);

        return $this->responseSuccess($data, '编辑成功');

        $send = (int)$request->input('send', 0);
        if ($send && $admin->user_id==0) {
            dispatch(new PopularizeUserPushJob($plug->id, 'plug', [
                'commodity' => $plug->name . '|插件',
                'money' => $plug->price,
                'money2' => $plug->price,
            ]));
        }

        return $this->responseSuccess(null, '编辑成功');
    }

    public function delete(Plug $plug)
    {
        UserPlug::query()->where('vid', $plug->id)->delete();
        $plug->delete();
        return $this->responseSuccess(null, '删除成功');
    }

    public function cateFilter()
    {
        $lists = PlugCategory::query()
            ->with(['childrenCategory' => function($q){
                return $q -> with(['childrenCategory' => function($q){
                    return $q -> with(['childrenCategory'])->orderByDesc('sort');
                }])->orderByDesc('sort');
            }])
            ->where(function ($query) {
                $query->where('parent_id', 0);
            })
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->get(['id', 'name', 'parent_id']);
        $newList = [];
        foreach ($lists as $list) {
            $newList[] = [
                'id' => $list->id,
                'name' =>  $list->name
            ];
            foreach ($list->childrenCategory as $child){
                $newList[] = [
                    'id' => $child->id,
                    'name' => $list->name . ' / ' . $child->name
                ];
                foreach ($child->childrenCategory as $child2){
                    $newList[] = [
                        'id' => $child2->id,
                        'name' => $list->name . ' / ' . $child->name. ' / ' . $child2->name
                    ];
                    foreach ($child2->childrenCategory as $child3){
                        $newList[] = [
                            'id' => $child3->id,
                            'name' => $list->name . ' / ' . $child->name. ' / ' . $child2->name . ' / ' . $child3->name
                        ];
                    }
                }
            }
        }

        return $this->responseSuccess($newList);
    }

    /**
     * 获取分类及其所有子分类ID
     *
     * @param int $categoryId 分类ID
     * @param string $type 分类类型 course/plug/goods
     * @return array
     */
    private function getCategoryWithChildren($categoryId, $type)
    {
        $categoryIds = [$categoryId];

        switch ($type) {
            case 'course':
                $children = \App\Models\CourseCategory::query()
                    ->where('parent_id', $categoryId)
                    ->pluck('id')
                    ->toArray();
                break;
            case 'plug':
                $children = \App\Models\PlugCategory::query()
                    ->where('parent_id', $categoryId)
                    ->pluck('id')
                    ->toArray();
                break;
            case 'goods':
                $children = \App\Models\GoodsCategory::query()
                    ->where('parent_id', $categoryId)
                    ->pluck('id')
                    ->toArray();
                break;
            default:
                $children = [];
        }

        // 递归获取所有子分类
        foreach ($children as $childId) {
            $categoryIds = array_merge($categoryIds, $this->getCategoryWithChildren($childId, $type));
        }

        return array_unique($categoryIds);
    }
}
