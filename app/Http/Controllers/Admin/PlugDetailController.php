<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\PlugDetailRequest;
use App\Models\Plug;
use App\Models\PlugCategory;
use App\Models\PlugDetail;
use Illuminate\Http\Request;

class PlugDetailController extends Controller
{
    public function index(Request $request)
    {
        $plug_id = $request->query('plug_id');
        $type = $request->query('type');
        $limit = $request->input('limit', 20);
        $lists = PlugDetail::query()
            ->when($plug_id!='', function ($query) use ($plug_id) {
                $query->where('plug_id', $plug_id);
            })
            ->when($type!='', function ($query) use ($type) {
                $query->where('type', $type);
            })
            ->orderByDesc('id')
            ->paginate($limit);

        return $this->responseSuccess($lists);
    }

    public function info(PlugDetail $plugDetail)
    {
        return $this->responseSuccess($plugDetail);
    }

    public function store(PlugDetailRequest $request)
    {
        $admin = $request->user('admin');

        $plugDetail = new PlugDetail();
        $plugDetail->plug_id = $request->input('plug_id');
        $plugDetail->version_code = $request->input('version_code');
        $plugDetail->platform = $request->input('platform');
        $plugDetail->description = $request->input('description');
        $plugDetail->type = $request->input('type');
        $plugDetail->link = $request->input('link');
        $plugDetail->sort = $request->input('sort', 0);
        $plugDetail->admin_id = $admin->id;
        if ($plugDetail->save()) {
            return $this->responseSuccess(null, '添加成功');
        }
        return $this->responseError('添加失败', ResponseCode::SERVER_ERR);
    }

    public function update(PlugDetailRequest $request, PlugDetail $plugDetail)
    {
        $data = $request->only(['version_code','platform', 'description', 'type', 'link','sort']);
        $plugDetail->update($data);
        return $this->responseSuccess(null, '编辑成功');
    }

    public function delete(PlugDetail $plugDetail)
    {
        $plugDetail->delete();
        return $this->responseSuccess(null, '删除成功');
    }


    public function cateFilter()
    {
        $lists = PlugCategory::query()
            ->orderByDesc('id')
            ->get(['id', 'name']);
        return $this->responseSuccess($lists);
    }
}
