<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Order;
use App\Models\User;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    /**
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $admin = $request->user('admin');
        if ($admin->user_id > 0) {
            $data = [];
        } else {
            // 2024-03-08后下单支付改为统一光子支付
            $time = '2024-03-08';

            $userCount = User::query()->count();
            $userTodayCount = User::query()->whereDate('created_at', now()->format('Y-m-d'))->count();
            $orderCount = Order::query()->where('status', Order::StatusMappings['paid'])->count();
            $todayCount = Order::query()->whereDate('created_at', now()->format('Y-m-d'))->where('status', Order::StatusMappings['paid'])->count();
            $orderAmount = Order::query()->where('status', Order::StatusMappings['paid'])->where('created_at', '>', $time)->sum('order_amount');
            $todayAmount = Order::query()
                ->whereDate('created_at', now()->format('Y-m-d'))
                ->where('status', Order::StatusMappings['paid'])
                ->where('created_at', '>', $time)
                ->sum('order_amount');

            $data = [
                'user' => [
                    'total' => $userCount,
                    'today' => $userTodayCount,
                ],
                'order_count' => [
                    'total' => $orderCount,
                    'today' => $todayCount,
                ],
                'order_amount' => [
                    'total' => $orderAmount,
                    'today' => $todayAmount,
                ],
            ];
        }

        return $this->responseSuccess($data);
    }
}
