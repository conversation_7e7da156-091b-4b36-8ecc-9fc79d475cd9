<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\CouponCode;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class CouponController extends Controller
{
    public function index(Request $request)
    {
        $code = $request->input('code');
        $limit = $request->input('limit', 20);
        $lists = CouponCode::query()
            ->with(['user:id,name', 'useUser:id,name'])
            ->when($code!='', function ($query) use ($code) {
                $query->where('code', $code);
            })
            ->orderByDesc('id')
            ->paginate($limit);

        return $this->responseSuccess($lists);
    }

    public function info(CouponCode $couponCode)
    {
        return $this->responseSuccess($couponCode);
    }

    public function store(Request $request)
    {
        $phone =  $request->input('phone');
        if ($phone) {
            $user_id = User::query()
                ->where('phone', $phone)
                ->orWhere('email', $phone)
                ->value('id');
        }
        $type = $request->input('type');
        if ($type && !in_array($type, CouponCode::TypeMappings)) {
            throw new ApiException('类型错误', ResponseCode::NOT_FOUND);
        }
        $target_id = $request->input('target_id', 0);
        if ($type != CouponCode::TypeMappings['course']) {
            $target_id = 0;
        }
        try {
            $couponCode = new CouponCode();
            $couponCode->price = $request->input('price');
            $couponCode->code = $this->getCode();
            $couponCode->user_id = $user_id ?? 0;
            $couponCode->end_time = $request->input('end_time');
            $couponCode->type = $request->input('type');
            $couponCode->target_id = $target_id;
            $couponCode->save();

            return $this->responseSuccess(null, '添加成功');
        } catch (\Exception $exception) {
            Log::error($exception);
            return $this->responseError('添加失败，请重试', ResponseCode::SERVER_ERR);
        }
    }
    protected function getCode()
    {
        $code = coupon_code();
        if (CouponCode::query()->where('code', $code)->exists()) {
            return $this->getCode();
        }
        return $code;
    }

    /**
     * 删除
     * @param CouponCode $couponCode
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(CouponCode $couponCode)
    {
        $couponCode->delete();
        return $this->responseSuccess(null, '删除成功');
    }
}
