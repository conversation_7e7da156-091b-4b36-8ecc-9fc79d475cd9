<?php

namespace App\Http\Controllers\Admin;

use App\Models\Admin;
use App\Services\Oss;
use Illuminate\Http\Request;
use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;

class AuthController extends Controller
{
    /**
     * 管理员登录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function login(Request $request)
    {
        $account = $request->input('account');
        $pass = $request->input('password');

        $admin = $this->loginCheckAdmin($account,$pass);

        $admin->admin_token = md5($admin->toJson().time());
        $admin->save();
        $oss = Oss::getInstance()->getTempAccess();
        $return_data = [
            'admin' => $admin,
            'oss' => $oss,
        ];

        return $this->responseSuccess($return_data, '登录成功');
    }

    /**
     * 校验是否绑定谷歌验证码
     *
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function checkBind()
    {
        return $this->responseSuccess(['is_bind'=>1], 'success');
    }

    /**
     * 绑定谷歌验证码
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function bind(Request $request)
    {
        $account = $request->input('account');
        $pass = $request->input('password');

        $admin = $this->loginCheckAdmin($account,$pass);
        $admin->is_bind = 1;
        return $this->responseSuccess($admin, '绑定成功');
    }

    /**
     * 登录信息校验
     *
     * @param $account
     * @param $pass
     * @param $verification_code
     * @return Admin|\Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     * @throws ApiException
     */
    protected function loginCheckAdmin($account,$pass)
    {
        if (!$account) {
            throw new ApiException('登录账号不能为空', ResponseCode::PARAM_ERR);
        }
        if (!$pass){
            throw new ApiException('账号密码不能为空', ResponseCode::PARAM_ERR);
        }

        $admin = Admin::query()
            ->select(['id', 'name', 'state', 'admin_token', 'password', 'user_id'])
            ->where(function ($query)use($account){
                if (validateChinaPhoneNumber($account)) {
                    return $query->where('phone', $account);
                }
                if (filter_var($account, FILTER_VALIDATE_EMAIL)) {
                    return $query->where('email', $account);
                }
            })
            ->first();
        if (!$admin) {
            throw new ApiException('账号信息不存在', ResponseCode::NOT_FOUND);
        }
        if ($admin->state != Admin::StateMapping['normal']) {
            throw new ApiException('该账号已被冻结', ResponseCode::FORBIDDEN);
        }
        if ($pass && !Hash::check($pass, $admin->password)) {
            throw new ApiException('账号或者密码错误', ResponseCode::PARAM_ERR);
        }

        return $admin;
    }

    /**
     * 退出登录
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logout(Request $request)
    {
        $admin = $request->user('admin');
        $admin->admin_token = '';
        $admin->save();

        return $this->responseSuccess(null, '退出登录成功');
    }


    public function user(Request $request)
    {
        return response()->json($request->user('admin'));
    }

    /**
     * 刷新oss
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function ossToken()
    {
        $oss = Oss::getInstance()->getTempAccess();
        return $this->responseSuccess($oss);
    }
}
