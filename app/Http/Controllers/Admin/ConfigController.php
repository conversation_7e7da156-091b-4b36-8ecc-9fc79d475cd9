<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\Config;
use App\Models\UserUploadConfig;
use App\Services\DiscountService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;

class ConfigController extends Controller
{

    public function myuploadconfig(Request $request)
    {
        $config = UserUploadConfig::query()->pluck('value', 'key');
        if (strtoupper($request->method()) == 'GET') {
            return $this->responseSuccess($config);
        }

        foreach ($request -> input() as $k => $v){
            $config = UserUploadConfig::query()->where('key', $k)->first();
            $config -> value = $v;
            $config -> save();
        }


        return $this->responseSuccess(null, '修改成功');
    }


    /**
     * 邀请配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function invite(Request $request)
    {
        $config = Config::query()->where('key', Config::INVITE)->first();
        if (strtoupper($request->method()) == 'GET') {
            return $this->responseSuccess($config);
        }

        $validator = Validator::make($request->all(),
            ['rule' => 'required', 'url_rule' => 'required'],
            ['url_rule.required' => '邀请链接不能为空', 'rule.required' => '邀请说明不能为空']
        );
        if ($validator->fails()) {
            throw new ApiException($validator->errors()->first(), ResponseCode::PARAM_ERR);
        }

        $config->value = $request->only(['rule', 'url_rule']);
        $config->save();
        return $this->responseSuccess(null, '修改成功');
    }

    /**
     * 隐私协议
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function private(Request $request)
    {
        $config = Config::query()->where('key', Config::KeyMappings['private'])->first();
        if (strtoupper($request->method()) == 'GET') {
            return $this->responseSuccess($config);
        }

        $config->value = $request->input('private');
        $config->save();
        return $this->responseSuccess(null, '修改成功');
    }

    /**
     * 服务条款
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function service(Request $request)
    {
        $config = Config::query()->where('key', Config::KeyMappings['service'])->first();
        if (strtoupper($request->method()) == 'GET') {
            return $this->responseSuccess($config);
        }

        $config->value = $request->input('service');
        $config->save();
        return $this->responseSuccess(null, '修改成功');
    }

    /**
     * 常见问题
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function question(Request $request)
    {
        $config = Config::query()->where('key', Config::KeyMappings['question'])->first();
        if (strtoupper($request->method()) == 'GET') {
            return $this->responseSuccess($config);
        }

        $config->value = $request->input('value');
        $config->save();
        return $this->responseSuccess(null, '修改成功');
    }

    /**
     * 注册协议
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function register(Request $request)
    {
        $config = Config::query()->where('key', Config::KeyMappings['register'])->first();
        if (strtoupper($request->method()) == 'GET') {
            return $this->responseSuccess($config);
        }

        $config->value = $request->input('value');
        $config->save();
        return $this->responseSuccess(null, '修改成功');
    }
    /**
     * 关于我们
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function about(Request $request)
    {
        $config = Config::query()->where('key', Config::KeyMappings['about'])->first();
        if (strtoupper($request->method()) == 'GET') {
            return $this->responseSuccess($config);
        }

        $config->value = $request->input('value');
        $config->save();
        return $this->responseSuccess(null, '修改成功');
    }
    /**
     * 联系我们
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function contact(Request $request)
    {
        $config = Config::query()->where('key', Config::KeyMappings['contact'])->first();
        if ($request->method() === "GET") {
            return $this->responseSuccess($config);
        } else {
            $config->value = $request->only('email', 'qq', 'wechat', 'phone');
            $config->save();
            return $this->responseSuccess($config, '修改成功');
        }
    }
    /**
     * 广告
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function ad(Request $request)
    {
        $config = Config::query()->where('key', Config::KeyMappings['ad'])->first();
        if ($request->method() === "GET") {
            return $this->responseSuccess($config);
        } else {
            $config->value = $request->only('file', 'link');
            $config->save();
            return $this->responseSuccess($config, '修改成功');
        }
    }

    /**
     * 上传声明
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function uploadStatement(Request $request)
    {
        $config = Config::query()->where('key', Config::KeyMappings['upload_statement'])->first();
        if (!$config) {
            $config = new Config();
            $config->key = Config::KeyMappings['upload_statement'];
            $config->value = '上传声明';
            $config->save();
        }
        if (strtoupper($request->method()) == 'GET') {
            return $this->responseSuccess($config);
        }
        $config->value = $request->input('upload_statement', '');
        $config->save();
        return $this->responseSuccess(null, '修改成功');
    }

    /**
     * 版权声明
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function copyrightStatement(Request $request)
    {
        $config = Config::query()->where('key', Config::KeyMappings['copyright_statement'])->first();
        if (!$config) {
            $config = new Config();
            $config->key = Config::KeyMappings['copyright_statement'];
            $config->value = '版权声明';
            $config->save();
        }

        if (strtoupper($request->method()) == 'GET') {
            return $this->responseSuccess($config);
        }

        $config->value = $request->input('copyright_statement', '');
        $config->save();
        return $this->responseSuccess(null, '修改成功');
    }

    /**
     * 版权声明
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function downloadStatement(Request $request)
    {
        $config = Config::query()->where('key', Config::KeyMappings['download_statement'])->first();
        if (!$config) {
            $config = new Config();
            $config->key = Config::KeyMappings['download_statement'];
            $config->value = '下载声明';
            $config->save();
        }

        if (strtoupper($request->method()) == 'GET') {
            return $this->responseSuccess($config);
        }

        $config->value = $request->input('download_statement', '');
        $config->save();
        return $this->responseSuccess(null, '修改成功');
    }

    /**
     * AI后端服务器地址
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function aiServer(Request $request)
    {
        $config = Config::query()->where('key', Config::KeyMappings['ai_server'])->first();
        if (!$config) {
            $config = new Config();
            $config->key = Config::KeyMappings['ai_server'];
            $config->value = '';
            $config->save();
        }

        if (strtoupper($request->method()) == 'GET') {
            return $this->responseSuccess($config);
        }

        $config->value = $request->input('ai_server', '');
        $config->save();
        return $this->responseSuccess(null, '修改成功');
    }

    /**
     * 折扣配置
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function discountConfig(Request $request)
    {
        $type = $request->input('type');

        // 验证产品类型
        if (!$type || !in_array($type, array_keys(DiscountService::getProductTypes()))) {
            throw new ApiException('产品类型参数错误', ResponseCode::PARAM_ERR);
        }

        if (strtoupper($request->method()) == 'GET') {
            // 获取指定类型的折扣配置
            $config = DiscountService::getDiscountConfig($type);
            if (!$config) {
                // 返回默认配置
                $config = [
                    'discount' => 100, // 无折扣
                    'start_time' => '',
                    'end_time' => '',
                    'enabled' => false
                ];
            }
            return $this->responseSuccess($config);
        }

        // 验证请求参数
        $validator = Validator::make($request->all(), [
            'discount' => 'required|integer|min:1|max:100',
            'start_time' => 'required|date',
            'end_time' => 'required|date|after:start_time',
            'enabled' => 'boolean'
        ], [
            'discount.required' => '折扣数不能为空',
            'discount.integer' => '折扣数必须为整数',
            'discount.min' => '折扣数不能小于1',
            'discount.max' => '折扣数不能大于100',
            'start_time.required' => '开始时间不能为空',
            'start_time.date' => '开始时间格式错误',
            'end_time.required' => '结束时间不能为空',
            'end_time.date' => '结束时间格式错误',
            'end_time.after' => '结束时间必须晚于开始时间',
            'enabled.boolean' => '启用状态参数错误'
        ]);

        if ($validator->fails()) {
            throw new ApiException($validator->errors()->first(), ResponseCode::PARAM_ERR);
        }

        // 设置折扣配置
        $data = $request->only(['discount', 'start_time', 'end_time', 'enabled']);
        $result = DiscountService::setDiscountConfig($type, $data);

        if ($result) {
            return $this->responseSuccess(null, '折扣配置保存成功');
        } else {
            throw new ApiException('折扣配置保存失败', ResponseCode::SERVER_ERR);
        }
    }
}
