<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Jobs\SendMessageJob;
use App\Models\Order;
use App\Models\OrderLogistic;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Rap2hpoutre\FastExcel\FastExcel;

class OrderController extends Controller
{
    public function index(Request $request)
    {
        $phone = $request->input('phone');
        $order_no = $request->input('order_no');
        $start_time = $request->input('start_time');
        $end_time = $request->input('end_time');
        $status = $request->input('status');
        $product_type = $request->input('product_type');
        $pay_method = $request->input('pay_method');
        $limit = $request->query('limit', 15);

        $list = Order::query()
            ->when($status != '' && $status != 4, function ($query) use ($status) {
                return $query->where('status', (int)$status);
            })
            ->when($product_type != '', function ($query) use ($product_type) {
                return $query->where('product_type', $product_type);
            })
            ->when($order_no != '', function ($query) use ($order_no) {
                return $query->where('order_no', $order_no);
            })
            ->when($pay_method != '', function ($query) use ($pay_method) {
                $query->where('pay_method', $pay_method);
            })
            ->when($start_time || $end_time, function ($query) use ($start_time, $end_time) {
                if ($start_time) {
                    $query->where('created_at', '>=', $start_time);
                }
                if ($end_time) {
                    $query->where('created_at', '<', $end_time);
                }
                return $query;
            })
            ->when($phone != '', function ($query) use ($phone) {
                $query->whereHas('user', function ($query) use ($phone) {
                    $query->where('phone', $phone);
                });
            })
            ->with(['user'])
            ->orderByDesc('id')
            ->paginate($limit);

        $wait_count = Order::query()
            ->where('status', Order::StatusMappings['wait'])
            ->when($product_type != '', function ($query) use ($product_type) {
                return $query->where('product_type', $product_type);
            })
            ->when($start_time || $end_time, function ($query) use ($start_time, $end_time) {
                if ($start_time) {
                    $query->where('created_at', '>=', $start_time);
                }
                if ($end_time) {
                    $query->where('created_at', '<', $end_time);
                }
                return $query;
            })
            ->when($order_no != '', function ($query) use ($order_no) {
                return $query->where('order_no', $order_no);
            })
            ->when($phone != '', function ($query) use ($phone) {
                $query->whereHas('user', function ($query) use ($phone) {
                    $query->where('phone', $phone);
                });
            })
            ->count();
        $data = [
            'data' => $list,
            'wait_count' => $wait_count
        ];
        return $this->responseSuccess($data);
    }

    /**
     * 订单导出
     * @return mixed
     * @throws ApiException
     */
    public function export(Request $request)
    {
        $orders = $this->generator($request);

        try {
            $methods = [
                0=> '无',
                1=> '微信',
                2=> '支付宝'
            ];

            $productType = [
                1=> '课程',
                2=> '插件',
                3=> '周边',
            ];

            $status = [
                0 => '未支付',
                1 => '已支付',
                2 => '已关闭'
            ];
            $fileName = sprintf('%s.xlsx', date('YmdHis').'-订单导出');
            (new FastExcel($orders))->export($fileName, function ($item) use ($methods, $status, $productType) {
                return [
                    'ID' => $item['id'],
                    '用户昵称' => $item['user']['name'] ?? '-',
                    '用户手机号' => $item['user']['phone'] ?? '-',
                    '订单号' => $item['order_no'],
                    '产品名称' => $item['product_name'],
                    '产品类型' => $productType[$item['product_type']],
                    '支付金额' => $item['order_amount'],
                    '优惠码' => $item['coupon_code'],
                    '抵扣价格' => $item['deduct_price'],
                    '总金额' => $item['total_amount'],
                    '支付时间' => $item['pay_time'],
                    '支付状态' => $status[$item['status']],
                    '支付方式' => $methods[$item['pay_method']],
                    '创建时间' => $item['created_at'],
                ];
            });
            return $this->responseSuccess(['filename' => $fileName]);
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
            throw new ApiException('导出失败', ResponseCode::SERVER_ERR);
        }
    }
    public function generator($request)
    {
        $phone = $request->input('phone');
        $order_no = $request->input('order_no');
        $start_time = $request->input('start_time');
        $end_time = $request->input('end_time');
        $status = $request->input('status');
        $product_type = $request->input('product_type');
        $pay_method = $request->input('pay_method');

        foreach (Order::query()
                     ->when($status != '', function ($query) use ($status) {
                         return $query->where('status', (int)$status);
                     })
                     ->when($product_type != '', function ($query) use ($product_type) {
                         return $query->where('product_type', $product_type);
                     })
                     ->when($order_no != '', function ($query) use ($order_no) {
                         return $query->where('order_no', $order_no);
                     })
                     ->when($pay_method != '', function ($query) use ($pay_method) {
                         $query->where('pay_method', $pay_method);
                     })
                     ->when($start_time || $end_time, function ($query) use ($start_time, $end_time) {
                         if ($start_time) {
                             $query->where('created_at', '>=', $start_time);
                         }
                         if ($end_time) {
                             $query->where('created_at', '<', $end_time);
                         }
                         return $query;
                     })
                     ->when($phone != '', function ($query) use ($phone) {
                         $query->whereHas('user', function ($query) use ($phone) {
                             $query->where('phone', $phone);
                         });
                     })
                     ->where('product_type', '!=', 3)
                     ->with(['user'])
                     ->orderByDesc('id')
                     ->cursor() as $item) {
            yield $item;
        }
    }

    /**
     * 发货
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function deliver(Request $request)
    {
        $order_no = $request->input('order_no');
        $logistics_name = $request->input('logistics_name');
        $logistics_no = $request->input('logistics_no');

        if (!$order_no) {
            throw new ApiException('订单号不能为空', ResponseCode::PARAM_ERR);
        }

        $order = Order::query()->where('order_no', $order_no)->first();
        if (!$order || $order->product_type!=Order::ProductTypeMappings['goods']) {
            throw new ApiException('订单号异常，请检查', ResponseCode::PARAM_ERR);
        }

        if (!$logistics_name) {
            throw new ApiException('物流公司不能为空', ResponseCode::PARAM_ERR);
        }

        if (!$logistics_no) {
            throw new ApiException('快递单号不能为空', ResponseCode::PARAM_ERR);
        }

        $logistics = new OrderLogistic();
        $logistics->order_id = $order->id;
        $logistics->user_id = $order->user_id;
        $logistics->logistics_name = $logistics_name;
        $logistics->logistics_no = $logistics_no;
        $logistics->logistics_code = '10086';
        $logistics->delivery_status = 1;
        $logistics->delivery_time = now()->toDateTimeString();
        if ($logistics->save()) {
            $order->logistic_status = 1;
            $order->save();

            dispatch(new SendMessageJob($order->user->phone, 'logistics', $order->product_id, array_flip(Order::ProductTypeMappings)[$order->product_type], [
                'name' => $order->user->name,
                'number' => sprintf('%s(%s)',$logistics_no, $logistics_name),
                'commodity' => $order->product_name,
            ]));

            return $this->responseSuccess(null, '发货成功');
        }

        return $this->responseError(null, '发货失败');
    }


    /**
     * 周边订单导出
     * @return mixed
     * @throws ApiException
     */
    public function goodExport(Request $request)
    {
        $orders = $this->goodGenerator($request);

        try {
            $logistic_status = [
                1=> '已发货',
                0=> '未发货'
            ];
            // 支付方式
            $methods = [
                0=> '无',
                1=> '微信',
                2=> '支付宝'
            ];

            $status = [
                0 => '未支付',
                1 => '已支付',
                2 => '已关闭'
            ];
            $fileName = sprintf('%s.xlsx', date('YmdHis').'-周边订单导出');
            (new FastExcel($orders))->export($fileName, function ($item) use ($logistic_status, $status, $methods) {
                $address = '';
                if ($item['consignee']) {
                    $address = $item['consignee']['address'];
                }

                return [
                    'ID' => $item['id'],
                    '用户昵称' => $item['user']['name'] ?? '-',
                    '订单号' => $item['order_no'],
                    '产品名称' => $item['product_name'],
                    '购买数量' => $item['num'],
                    '价格' => $item['order_amount'],
                    '支付类型' => $methods[$item['pay_method']],
                    '支付时间' => $item['pay_time'],
                    '支付状态' => $status[$item['status']],
                    '收货人姓名' => $item['consignee'] ? $item['consignee']['name'] : '',
                    '收货人电话' => $item['consignee'] ? $item['consignee']['phone'] : '',
                    '收货人地址' => $address,
                    '发货状态' => $logistic_status[$item['logistic_status']],
                    '物流公司' => $item['logistic'] ? $item['logistic']['logistics_name'] : '',
                    '物流单号' => $item['logistic'] ? $item['logistic']['logistics_no'] : '',
                    '发货时间' => $item['logistic'] ? $item['logistic']['delivery_time'] : '',
                    '下单时间' => $item['created_at'],
                ];
            });
            return $this->responseSuccess(['filename' => $fileName]);
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
            throw new ApiException('导出失败', ResponseCode::SERVER_ERR);
        }
    }
    public function goodGenerator($request)
    {
        $phone = $request->input('phone');
        $order_no = $request->input('order_no');
        $start_time = $request->input('start_time');
        $end_time = $request->input('end_time');
        $status = $request->input('status');
        $product_type = $request->input('product_type');
        $pay_method = $request->input('pay_method');

        foreach (Order::query()
                     ->when($status != '' && $status != 4, function ($query) use ($status) {
                         return $query->where('status', (int)$status);
                     })
                     ->when($product_type != '', function ($query) use ($product_type) {
                         return $query->where('product_type', $product_type);
                     })
                     ->when($order_no != '', function ($query) use ($order_no) {
                         return $query->where('order_no', $order_no);
                     })
                     ->when($pay_method != '', function ($query) use ($pay_method) {
                         $query->where('pay_method', $pay_method);
                     })
                     ->when($start_time || $end_time, function ($query) use ($start_time, $end_time) {
                         if ($start_time) {
                             $query->where('created_at', '>=', $start_time);
                         }
                         if ($end_time) {
                             $query->where('created_at', '<', $end_time);
                         }
                         return $query;
                     })
                     ->when($phone != '', function ($query) use ($phone) {
                         $query->whereHas('user', function ($query) use ($phone) {
                             $query->where('phone', $phone);
                         });
                     })
                     ->with(['logistic', 'consignee'])
                     ->with(['user'])
                     ->orderByDesc('id')
                     ->cursor() as $item) {
            yield $item;
        }
    }
}
