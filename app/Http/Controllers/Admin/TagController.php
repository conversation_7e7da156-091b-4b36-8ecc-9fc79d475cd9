<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\PlugRequest;
use App\Jobs\PopularizeUserPushJob;
use App\Models\Plug;
use App\Models\PlugCategory;
use App\Models\Tag;
use App\Models\UserPlug;
use Illuminate\Http\Request;

class TagController extends Controller
{
    public function getList(Request $request)
    {

        $keywords = $request->query('keywords');
        if($request->type&&$request ->type=='all'){
            $lists = Tag::query()
                ->orderByDesc('id')
                ->get();
            return $this->responseSuccess($lists);
        }
        $limit = $request->input('limit', 20);
        $lists = Tag::query()
            ->when($keywords!='', function ($query) use ($keywords) {
                return $query->where('tag', 'like', "%$keywords%");
            })
            ->orderByDesc('id')
            ->paginate($limit);
        return $this->responseSuccess($lists);
    }

    public function store(Request $request)
    {
        $id = $request->input('id');
        if($id){
            $tag = Tag::find($id);
            if (!$tag){
                $tag = new Tag();
            }
        } else {
            $tag = new Tag();
        }
        $tag->tag = $request->input('tag');

        if ($tag->save()) {
            return $this->responseSuccess(null, '添加成功');
        }
        return $this->responseError('添加失败', ResponseCode::SERVER_ERR);
    }


    public function delete($id)
    {
        $res = Tag::query()->where('id', $id)->delete();
        return $this->responseSuccess($res, '删除成功');
    }


}
