<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Models\Role;
use App\Models\Admin;
use App\Models\Authority;
use App\Models\User;
use Illuminate\Http\Request;
use App\Http\Requests\Admin\AdminRequest;

class AdminController extends Controller
{
    /**
     * 管理员列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $name = $request->query('name');
        $phone = $request->query('phone');
        $email = $request->query('email');
        $state = $request->query('state', -1);
        $bind = $request->query('bind', 0);
        $limit = $request->query('limit', 15);

        $admin = $request->user('admin');

        $items = Admin::query()
            ->with(['roles', 'user:id,name,phone,avatar'])
            ->when($admin->id != 1, function ($query) {
                return $query->where('id', '!=', 1);
            })
            ->when($bind!='-1', function ($query) use ($bind) {
                if ($bind) {
                    $query->where('user_id', '>', 0);
                } else {
                    $query->where('user_id', 0);
                }
            })
            ->when($name, function ($query) use ($name) {
                return $query->where('name', 'like', '%' . $name . '%');
            })
            ->when($phone, function ($query) use ($phone) {
                return $query->where('phone', 'like', '%' . $phone . '%');
            })
            ->when($email, function ($query) use ($email) {
                return $query->where('email', 'like', '%' . $email . '%');
            })
            ->when($state != '' && in_array($state, Admin::StateMapping), function ($query) use ($state) {
                return $query->where('state', $state);
            })
            ->where('is_delete', Admin::DeleteMapping['no'])
            ->paginate($limit);
        foreach ($items as $item) {
            $item->account = $item->user->phone ?? '';
        }

        return $this->responseSuccess($items);

    }

    /**
     * 创建管理员
     * @param AdminRequest $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function store(AdminRequest $request)
    {
        $data = $request->only(['name', 'phone', 'email', 'password', 'state']);
        $account = $request->input('account');
        if ($account) {
            $user = User::query()->where('phone', $account)->first();
            if (!$user || $user->state==1) {
                throw new ApiException('用户不存在或已被禁用', ResponseCode::FORBIDDEN);
            }
            $data['user_id'] = $user->id;

            $user->bind = 1;
            $user->api_token = '';
            $user->save();
        }
        $admin = Admin::query()->create($data);

        $roles = $request->input('roles');
        $admin->roles()->sync($roles);

        return $this->responseSuccess(null, '创建管理员成功');
    }

    /**
     * 管理员详情
     *
     * @param Admin $admin
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Admin $admin)
    {

        $admin->load('roles');

        return $this->responseSuccess($admin);
    }

    /**
     * 修改管理员信息
     *
     * @param AdminRequest $request
     * @param Admin $admin
     * @return \Illuminate\Http\JsonResponse
     */
    public function update(AdminRequest $request, Admin $admin)
    {
        $data = $request->except('roles');
        if (isset($data['state']) && $data['state'] == Admin::StateMapping['disable']) {
            $data['admin_token'] = null;
        }
        $account = $request->input('account');
        if ($account) {
            $user = User::query()->where('phone', $account)->first();
            if (!$user || $user->state==1) {
                throw new ApiException('用户不存在或已被禁用', ResponseCode::FORBIDDEN);
            }
            $data['user_id'] = $user->id;

            $user->bind = 1;
            $user->api_token = '';
            $user->save();
        } elseif ($admin->user_id>0) {
            $user = User::query()->find($admin->user_id);
            $user->bind = 0;
            $user->api_token = '';
            $user->save();
            $data['user_id'] = 0;
        }
        $admin->update($data);

        $role_ids = $request->input('roles');
        $admin->roles()->sync($role_ids);

        return $this->responseSuccess(null, '修改管理员成功');
    }

    /**
     * 删除管理员
     *
     * @param Admin $admin
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function destroy(Admin $admin)
    {
        $admin->roles()->detach();
        $admin->delete();

        return $this->responseSuccess(null, '删除管理员成功');
    }

    /**
     * 获取菜单与权限节点
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function nav(Request $request)
    {
        $admin = $request->user('admin');

        $role = $admin->roles;
        $roleIds = $role->pluck('id')->toArray();

        $menus = [];
        $perms = [];
        foreach ($roleIds as $roleId) {
            $role = Role::find($roleId);
            $permissions = $role->permissions
                ->sortBy('sort')
                ->all();
            foreach ($permissions as $permission) {
                //  筛选非按钮状态的菜单
                if ($permission->type != Authority::BUTTON_TYPE) {
                    if (!collect($menus)->contains('id', $permission['id'])) {
                        array_push($menus, $permission);
                    }
                }
                //  注入所有节点
                if (!in_array($permission['alias'], $perms)) {
                    array_push($perms, $permission['alias']);
                }
            }
        }
        $menuList = $this->AuthorityFormat(array_values($menus));

        return $this->responseSuccess(['menus' => $menuList, 'perms' => $perms]);
    }

    /**
     * @param $items
     * @param int $pid
     * @return array
     */
    protected function AuthorityFormat($items, $pid = 0)
    {
        $list = [];
        foreach ($items as $index => $item) {
            if ($item['pid'] == $pid) {
                $child = $this->AuthorityFormat($items, $item['id']);
                $item['list'] = $child ? $child : null;
                $list[] = $item;
            }
        }

        return $list;
    }
}
