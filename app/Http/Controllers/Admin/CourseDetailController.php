<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\CourseDetailRequest;
use App\Models\Base;
use App\Models\Course;
use App\Models\CourseDetail;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CourseDetailController extends Controller
{
    public function index(Request $request)
    {
        $keywords = $request->query('keywords');
        $course_id = $request->query('course_id');
        $type = $request->query('type');
        $limit = $request->input('limit', 20);
        $lists = CourseDetail::query()
            ->with('chapter')
            ->when($keywords!='', function ($query) use ($keywords) {
                return $query->where('name', 'like', "%$keywords%");
            })
            ->when($course_id!='', function ($query) use ($course_id) {
                return $query->where('course_id', $course_id);
            })
            ->when($type!='', function ($query) use ($type) {
                return $query->where('type', $type);
            })
            ->orderByDesc('id')
            ->paginate($limit);

        return $this->responseSuccess($lists);
    }

    public function info(CourseDetail $courseDetail)
    {
        return $this->responseSuccess($courseDetail);
    }

    public function store(CourseDetailRequest $request)
    {
        $admin = $request->user('admin');
        $data = $request->only(['name','type','images', 'link','views', 'state', 'sort', 'chapter_id', 'course_id', 'lock_status']);
        $exists = Course::query()->where('id', $data['course_id'])->where('is_delete', Base::NOT_DELETE)->exists();
        if (!$exists) {
            throw new ApiException('课程不存在', ResponseCode::PARAM_ERR);
        }
        if (!isset($data['type']) || !in_array($data['type'], CourseDetail::TypeValueMappings)) {
            throw new ApiException('类型错误', ResponseCode::PARAM_ERR);
        }

        if ($data['type']!='chapter' && $data['lock_status']!=1 &&  (!isset($data['link']) || !$data['link'])){
            throw new ApiException('请选添加课件', ResponseCode::PARAM_ERR);
        }
        if (!isset($data['link'])) {
            $data['link'] = '';
        }
        $data['admin_id']=$admin->id;

        $courseDetail = CourseDetail::query()->create($data);
        if ($data['type']=='chapter') {
            $courseDetail->chapter_id = $courseDetail->id;
            $courseDetail->save();
        }
        return $this->responseSuccess(null, '添加成功');
    }

    public function update(Request $request, CourseDetail $courseDetail)
    {
        $data = $request->only(['name','type','images', 'link','views', 'state', 'sort', 'chapter_id', 'course_id', 'lock_status']);
        if (!isset($data['type']) || !in_array($data['type'], CourseDetail::TypeValueMappings)) {
            throw new ApiException('类型错误', ResponseCode::PARAM_ERR);
        }

        if ($data['type']!='chapter' && $data['lock_status']!=1 && (!isset($data['link']) || !$data['link'])){
            throw new ApiException('请选添加课件', ResponseCode::PARAM_ERR);
        }
        if (!isset($data['link'])) {
            $data['link'] = '';
        }
        try {
            DB::beginTransaction();
            $courseDetail->update($data);

            if ($data['type']=='chapter' && isset($data['lock_status'])) {
                CourseDetail::query()
                    ->where('chapter_id', $courseDetail->id)
                    ->where('type', '!=',CourseDetail::TypeValueMappings['annex'])
                    ->update(['lock_status'=>$data['lock_status']]);
            }

            DB::commit();
        } catch (\Exception $exception) {
            DB::rollBack();

        }

        return $this->responseSuccess(null, '编辑成功');
    }

    public function delete(CourseDetail $courseDetail)
    {
        $courseDetail->delete();
        return $this->responseSuccess(null, '删除成功');
    }
}
