<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Services\RechargeService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\Log;
use Rap2hpoutre\FastExcel\FastExcel;

class RechargeController extends Controller
{
    /**
     * 管理员充值光子
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function adminRecharge(Request $request)
    {
        // 验证请求参数
        $validator = Validator::make($request->all(), [
            'phone' => 'required|string|regex:/^1[3-9]\d{9}$/',
            'amount' => 'required|numeric|min:0.01|max:999999.99',
            'remark' => 'nullable|string|max:255',
        ], [
            'phone.required' => '手机号不能为空',
            'phone.regex' => '手机号格式不正确',
            'amount.required' => '充值金额不能为空',
            'amount.numeric' => '充值金额必须为数字',
            'amount.min' => '充值金额不能小于0.01',
            'amount.max' => '充值金额不能超过999999.99',
            'remark.max' => '备注不能超过255个字符',
        ]);

        if ($validator->fails()) {
            throw new ApiException($validator->errors()->first(), ResponseCode::PARAM_ERR);
        }

        $phone = $request->input('phone');
        $amount = $request->input('amount');
        $remark = $request->input('remark', '');
        $admin = $request->user('admin');

        try {
            $result = RechargeService::adminRecharge($phone, $amount, $admin->id, $remark);
            
            // 记录操作日志
            Log::info('管理员充值操作', [
                'admin_id' => $admin->id,
                'admin_name' => $admin->name,
                'user_phone' => $phone,
                'amount' => $amount,
                'order_no' => $result['order_no'],
                'remark' => $remark,
            ]);

            return $this->responseSuccess($result, '充值成功');
        } catch (ApiException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('管理员充值失败', [
                'admin_id' => $admin->id,
                'phone' => $phone,
                'amount' => $amount,
                'error' => $e->getMessage(),
            ]);
            throw new ApiException('充值失败，请稍后重试', ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 获取充值记录列表
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function rechargeList(Request $request)
    {
        $params = [
            'phone' => $request->input('phone'),
            'order_no' => $request->input('order_no'),
            'start_time' => $request->input('start_time'),
            'end_time' => $request->input('end_time'),
            'limit' => $request->input('limit', 20),
        ];

        try {
            $list = RechargeService::getRechargeList($params);
            $stats = RechargeService::getRechargeStats($params);

            return $this->responseSuccess([
                'list' => $list,
                'stats' => $stats,
            ]);
        } catch (\Exception $e) {
            Log::error('获取充值记录失败', ['error' => $e->getMessage()]);
            throw new ApiException('获取充值记录失败', ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 导出充值记录
     * 
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     * @throws ApiException
     */
    public function exportRechargeList(Request $request)
    {
        try {
            $params = [
                'phone' => $request->input('phone'),
                'order_no' => $request->input('order_no'),
                'start_time' => $request->input('start_time'),
                'end_time' => $request->input('end_time'),
                'limit' => 10000, // 导出限制
            ];

            $list = RechargeService::getRechargeList($params);
            
            if ($list->isEmpty()) {
                throw new ApiException('没有数据可导出', ResponseCode::PARAM_ERR);
            }

            $fileName = 'export/' . sprintf('%s.xlsx', date('YmdHis') . '-光子充值记录导出');
            
            (new FastExcel($list->items()))->export($fileName, function ($item) {
                return [
                    'ID' => $item->id,
                    '订单号' => $item->order_no,
                    '用户姓名' => $item->user->name ?? '-',
                    '用户手机号' => $item->user->phone ?? '-',
                    '充值金额' => $item->order_amount,
                    '光子数量' => $item->num,
                    '充值时间' => $item->pay_time,
                    '备注' => $item->remark,
                    '创建时间' => $item->created_at->format('Y-m-d H:i:s'),
                ];
            });

            return $this->responseSuccess(['filename' => $fileName], '导出成功');
        } catch (ApiException $e) {
            throw $e;
        } catch (\Exception $e) {
            Log::error('导出充值记录失败', ['error' => $e->getMessage()]);
            throw new ApiException('导出失败', ResponseCode::SERVER_ERR);
        }
    }
}
