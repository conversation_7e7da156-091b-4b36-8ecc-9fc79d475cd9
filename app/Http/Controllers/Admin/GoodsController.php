<?php

namespace App\Http\Controllers\Admin;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Http\Controllers\Controller;
use App\Http\Requests\Admin\GoodsRequest;
use App\Jobs\PopularizeUserPushJob;
use App\Models\Goods;
use App\Models\GoodsCategory;
use App\Models\PlugCategory;
use App\Models\TagList;
use App\Models\UserFile;
use App\Models\UserGoods;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class GoodsController extends Controller
{
    /**
     * @param Request $request
     * @return JsonResponse
     */
    public function index(Request $request)
    {
        $name = $request->query('name');
        $status = $request->query('status');
        $category_id = $request->query('category_id'); // 分类ID筛选
        $limit = $request->query('limit', 15);

        $goods = Goods::query()
            ->with(['suggestUser:id,name', 'tagListInfo', 'tagListInfo',
                'cate' => function ($q) {
                    $q->with('parentCategory');
                }
            ])
            ->when($name, function ($query) use ($name) {
                return $query->where('name', 'like', sprintf('%%%s%%', $name));
            })
            ->when($status != '', function ($query) use ($status) {
                return $query->where('status', $status);
            })
            ->when($category_id != '', function ($query) use ($category_id) {
                // 获取当前分类及所有子分类ID
                $categoryIds = $this->getCategoryWithChildren($category_id, 'goods');
                return $query->whereIn('cate_id', $categoryIds);
            })
            ->notDelete()
            ->orderByDesc('id')
            ->paginate($limit);

        return $this->responseSuccess($goods);
    }

    /**
     * 添加商品
     *
     * @param GoodsRequest $request
     * @return JsonResponse
     * @throws ApiException
     */
    public function store(GoodsRequest $request)
    {
        try {
            $stock = 99999;

            $images = $request->input('images');

            $goods = new Goods();
            $goods->name = $request->input('name');
            $goods->sort = $request->input('sort', 0);
            $goods->total_stock = $stock;
            $goods->stock = $stock;
            $goods->desc = $request->input('desc');
            $goods->price = $request->input('price', 0);
            $goods->market_price = $request->input('market_price', 0);
            $goods->is_integral = $request->input('is_integral', 1);
            $goods->integral_num = $request->input('integral_num', 0);
            $goods->give_integral = $request->input('give_integral', 0);
            $goods->postage = $request->input('postage', 0);
            $goods->cover = $images[0] ?? '';
            $goods->images = $images;
            $goods->status = $request->input('status', 0);
            $goods->cate_id = $request->input('cate_id', 0);
            $goods->sort = $request->input('sort', 0);
            $goods->material = $request->input('material', '');
            $goods->user_id = $request->input('user_id', 0);
            $goods->is_delete = 0;
            $goods->save();

            $info = TagList::delTagInfo(['aid' => $goods->id, 'tag' => $request->input('tag', []), 'type' => 'goods']);
            $info = TagList::saveTagInfo(['aid' => $goods->id, 'tag' => $request->input('tag', []), 'type' => 'goods']);
            $send = (int)$request->input('send', 0);
            if ($send) {
                dispatch(new PopularizeUserPushJob($goods->id, 'goods', [
                    'commodity' => $goods->name . '|物品',
                    'money' => $goods->market_price,
                    'money2' => $goods->price,
                ]));
            }

            return $this->responseSuccess(['id' => $goods->id], '添加成功');
        } catch (\Exception $e) {
            Log::error($e);
            throw new ApiException($e->getMessage(), ResponseCode::SERVER_ERR);
        }
    }

    /**
     *  详情
     *
     * @param Goods $goods
     * @return JsonResponse
     */
    public function info(Goods $goods)
    {
        $tags = [];
        $goods->load('tagList');
        $tagList = $goods->tagList;
        if ($tagList) {
            foreach ($tagList as $item) {
                $tags[] = $item->tag;
            }
        }
        $goods->tag = $tags;
        return $this->responseSuccess($goods);
    }

    /**
     * 编辑
     * @param GoodsRequest $request
     * @param Goods $goods
     * @return JsonResponse
     * @throws ApiException
     */
    public function update(GoodsRequest $request, Goods $goods)
    {
        try {
//            $stock = $request->input('stock');
//            $totalstock = $goods->total_stock + $stock-$goods->stock;
//            if ($totalstock <= 0) {
//                $totalstock = 0;
//            }

            $data = $request->only([
                'name', 'sort', 'desc', 'price', 'market_price', 'is_integral',
                'integral_num', 'give_integral', 'postage', 'images', 'status', 'cate_id', 'sort', 'material', 'user_id'
            ]);
            if (isset($data['images'])) {
                $data['cover'] = $data['images'][0] ?? '';
            }
//            $data['stock'] = $stock;
//            $data['total_stock'] = $totalstock;
            $goods->update($data);

            $info = TagList::delTagInfo(['aid' => $goods->id, 'tag' => $request->input('tag', []), 'type' => 'goods']);
            $info = TagList::saveTagInfo(['aid' => $goods->id, 'tag' => $request->input('tag', []), 'type' => 'goods']);
            $send = (int)$request->input('send', 0);
            if ($send) {
                dispatch(new PopularizeUserPushJob($goods->id, 'goods', [
                    'commodity' => $goods->name . '|物品',
                    'money' => $goods->market_price,
                    'money2' => $goods->price,
                ]));
            }

            return $this->responseSuccess(['id' => $goods->id], '编辑成功');
        } catch (\Exception $e) {
            Log::error($e);
            throw new ApiException($e->getMessage(), ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 删除
     *
     * @param Goods $goods
     * @return \Illuminate\Http\JsonResponse
     */
    public function delete(Goods $goods)
    {
        $goods->is_delete = 1;
        UserGoods::query()->where('vid', $goods->id)->delete();
        $goods->save();

        return $this->responseSuccess(null, '删除成功');
    }

    /**
     * 修改商品上下架
     *
     * @param Request $request
     * @param Goods $goods
     * @return JsonResponse
     * @throws ApiException
     */
    public function status(Request $request, Goods $goods)
    {
        $status = $request->input('status');

        if (!in_array($status, [1, 0])) {
            throw new ApiException('状态参数错误', ResponseCode::FORBIDDEN);
        }

        $goods->status = $status;
        $goods->save();

        return $this->responseSuccess(null, '更新成功');
    }

    public function cateFilter()
    {
//        $lists = GoodsCategory::query()
//            ->with('parentCategory:id,name')
//            ->where('parent_id', '>', 0)
//            ->orderByDesc('sort')
//            ->orderByDesc('id')
//            ->get(['id', 'name', 'parent_id']);
//        $newList = [];
//        foreach ($lists as $list) {
//            $newList[] = [
//                'id' => $list->id,
//                'name' => ($list->parentCategory->name ?? '周边') . ' / ' . $list->name
//            ];
//        }
        $lists = GoodsCategory::query()
            ->with(['childrenCategory' => function ($q) {
                return $q->with(['childrenCategory' => function ($q) {
                    return $q->with(['childrenCategory'])->orderByDesc('sort');
                }])->orderByDesc('sort');
            }])
            ->where(function ($query) {
                $query->where('parent_id', 0);
            })
            ->orderByDesc('sort')
            ->orderByDesc('id')
            ->get(['id', 'name', 'parent_id']);
        $newList = [];
        foreach ($lists as $list) {

            $newList[] = [
                'id' => $list->id,
                'name' =>  $list->name
            ];
            foreach ($list->childrenCategory as $child) {
                $newList[] = [
                    'id' => $child->id,
                    'name' =>  $list->name . ' / ' . $child->name
                ];
                foreach ($child->childrenCategory as $child2) {
                    $newList[] = [
                        'id' => $child2->id,
                        'name' =>  $list->name . ' / ' . $child->name . ' / ' . $child2->name
                    ];
                    foreach ($child2->childrenCategory as $child3) {
                        $newList[] = [
                            'id' => $child3->id,
                            'name' =>  $list->name . ' / ' . $child->name . ' / ' . $child2->name . ' / ' . $child3->name
                        ];
                    }
                }
            }
        }

        return $this->responseSuccess($newList);
    }

    /**
     * 获取分类及其所有子分类ID
     *
     * @param int $categoryId 分类ID
     * @param string $type 分类类型 course/plug/goods
     * @return array
     */
    private function getCategoryWithChildren($categoryId, $type)
    {
        $categoryIds = [$categoryId];

        switch ($type) {
            case 'course':
                $children = \App\Models\CourseCategory::query()
                    ->where('parent_id', $categoryId)
                    ->pluck('id')
                    ->toArray();
                break;
            case 'plug':
                $children = \App\Models\PlugCategory::query()
                    ->where('parent_id', $categoryId)
                    ->pluck('id')
                    ->toArray();
                break;
            case 'goods':
                $children = \App\Models\GoodsCategory::query()
                    ->where('parent_id', $categoryId)
                    ->pluck('id')
                    ->toArray();
                break;
            default:
                $children = [];
        }

        // 递归获取所有子分类
        foreach ($children as $childId) {
            $categoryIds = array_merge($categoryIds, $this->getCategoryWithChildren($childId, $type));
        }

        return array_unique($categoryIds);
    }
}
