<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use App\Models\VerificationCode;

class VerificationCodeController extends Controller
{
    /**
     * 验证码列表
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request)
    {
        $scene = $request->input('scene');
        $type = $request->input('type');
        $state = $request->input('state');
        $used = $request->input('used');
        $number = $request->input('number');
        $limit = $request->query('limit', 15);

        $items = VerificationCode::query()
            ->when($scene, function ($query) use ($scene) {
                return $query->where('scene', $scene);
            })
            ->when($type, function ($query) use ($type) {
                return $query->where('type', $type);
            })
            ->when($state, function ($query) use ($state) {
                return $query->where('state', $state);
            })
            ->when($used, function ($query) use ($used) {
                return $query->where('used', $used);
            })
            ->when($number, function ($query) use ($number) {
                return $query->where('number', $number);
            })
            ->orderBy('id', 'desc')
            ->paginate($limit);

        return $this->responseSuccess($items);
    }
}
