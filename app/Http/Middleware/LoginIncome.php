<?php

namespace App\Http\Middleware;

use App\Services\IntegralService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class LoginIncome
{
    /**
     * @param Request $request
     * @param Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        // 20240229 取消每日登录赠送
        return $next($request);

        $user = $request->user('api');
        if ($user) {
            $cacheKey = sprintf('t_%s_user_%s', date('Ymd'), $user->id);
            // 登录赠送积分，每天一次
            if (!Cache::has($cacheKey)) {
                $lock = Cache::lock('login_income_'.$user->id, 60);
                if ($lock->get()) {
                    IntegralService::getInstance()->income($user->id, 3, 'login_give', '登录赠送');
                    Cache::put($cacheKey, 1, 24*3600);
                }
            }
        }

        return $next($request);
    }
}
