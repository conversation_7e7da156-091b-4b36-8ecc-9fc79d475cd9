<?php

namespace App\Http\Middleware;

use Closure;
use App\Traits\ResponseTrait;
use App\Constants\ResponseCode;
use Illuminate\Support\Facades\Auth;

class CheckUserDisable
{
    use ResponseTrait;

    /**
     * Handle an incoming request.
     *
     * @param $request
     * @param Closure $next
     * @return \Illuminate\Http\JsonResponse|mixed
     */
    public function handle($request, Closure $next)
    {
        $auth = Auth::guard('api');
        if ($auth->user() && $auth->user()->state === 1) {
            return $this->responseError('用户已被禁用', ResponseCode::FORBIDDEN);
        } elseif ($auth->user() && $auth->user()->is_delete === 1) {
            return $this->responseError('用户不存在', ResponseCode::FORBIDDEN);
        } else {
            return $next($request);
        }
    }
}
