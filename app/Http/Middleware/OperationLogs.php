<?php

namespace App\Http\Middleware;

use Closure;
use Illuminate\Http\Request;
use App\Models\OperationLogs as Op;

class OperationLogs
{
    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure $next
     * @return mixed
     */
    public function handle(Request $request, Closure $next)
    {
        $data=[];

        $data['ip'] = $request->ip();
        $data['module'] = $request->route()->getAction()['prefix'];
        $data['class'] = $request->route()->getAction()['controller'];
        $data['request_method'] = $request->method();
        $data['param'] = $request->all();

        if (array_key_exists('password', $data['param'])){
            $data['param']['password'] = md5( $data['param']['password']);
        }

        $response = $next($request);

        $admin = $request->user('admin');
        if ($admin){
            $data['admin_id'] = $request->user('admin')->id;
            $data['admin_name'] = $request->user('admin')->name;
        }
        $responseData = $response->original;

        $data['code'] = isset($responseData['code']) ? $responseData['code'] : 0;
        $data['message'] = isset($responseData['msg']) ? $responseData['msg'] : '';

        Op::create($data);

        return $response;
    }
}
