<?php

namespace App\Http\Middleware;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use Closure;
use Illuminate\Http\Request;

class PermissionAuth
{
    /**
     * @param Request $request
     * @param Closure $next
     * @param $param
     * @return mixed
     * @throws ApiException
     */
    public function handle(Request $request, Closure $next, $param)
    {
        $admin = $request->user('admin');
        //  id = 1 , 超级管理员不验证权限
        if ($admin->id != 1) {
            // 当前用户不拥有这个权限的名字
            if (!$admin->hasPermissions($param)) {
                throw new ApiException('无权操作', ResponseCode::FORBIDDEN);
            }
        }

        return $next($request);
    }
}