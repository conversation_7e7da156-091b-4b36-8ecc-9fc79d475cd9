<?php

namespace App\Http\Middleware;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use Illuminate\Auth\Middleware\Authenticate as Middleware;

class Authenticate extends Middleware
{
    /**
     * Get the path the user should be redirected to when they are not authenticated.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return string|null
     */
    protected function redirectTo($request)
    {
//        if (! $request->expectsJson()) {
//            return route('login');
//        }
    }

    /**
     * @param \Illuminate\Http\Request $request
     * @param array $guards
     * @throws ApiException
     * @throws \Illuminate\Auth\AuthenticationException
     */
    public function unauthenticated($request, array $guards)
    {
        if ($request->expectsJson() || in_array('api', $guards) || in_array('admin', $guards)) {
            throw new ApiException('请登录后操作',ResponseCode::UNAUTH);
        }

        if ($request->user('api')->state) {
            throw new ApiException('账户已被禁用',ResponseCode::UNAUTH);
        }

        parent::unauthenticated($request, $guards); // TODO: Change the autogenerated stub
    }
}
