<?php

namespace App\Constants;

/**
 * Api 响应业务状态码
 */
class ResponseCode
{
    // 请求成功
    const SUCCESS = 200;
    // 成功
    const SUCCEED = 201;
    // 用户未授权
    const UNAUTH = 4001;
    // 请求方法允许通过
    const METHOD_NOT_ALLOWED = 4005;
    // 拒绝访问
    const FORBIDDEN = 4403;
    // 未找到相关资源
    const NOT_FOUND = 4404;
    // 客户端请求参数错误
    const PARAM_ERR = 4422;
    // 余额不足
    const BALANCE_NOT_ENOUGH = 4423;
    // 服务器内部错误
    const SERVER_ERR = 5000;
}
