<?php

namespace App\Providers;

use App\Events\Forget;
use App\Events\SetEmail;
use App\Listeners\EmailVerify;
use App\Listeners\PasswordReset;
use Illuminate\Auth\Events\Registered;
use Illuminate\Auth\Listeners\SendEmailVerificationNotification;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        // 邮箱验证
        SetEmail::class => [
            EmailVerify::class,
        ],

        // 密码重置
        Forget::class => [
            PasswordReset::class,
        ],

        // 注册
        Registered::class => [
            SendEmailVerificationNotification::class,
        ],

        /*
         * Socialite
         */
        \SocialiteProviders\Manager\SocialiteWasCalled::class => [
            // ... other providers
            'SocialiteProviders\\WeChatWeb\\WeChatWebExtendSocialite@handle',
            'SocialiteProviders\\GitHub\\GitHubExtendSocialite@handle',
            'SocialiteProviders\\Weixin\\WeixinExtendSocialite@handle',
            'SocialiteProviders\\WeixinWeb\\WeixinWebExtendSocialite@handle',
            'SocialiteProviders\\QQ\\QqExtendSocialite@handle',
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        //
    }
}
