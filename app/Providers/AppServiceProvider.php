<?php

namespace App\Providers;

use EasyWeChat\Factory;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\ServiceProvider;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        $this->app->singleton('wxPayApp', function () {
            $payConfig = config('web.wechat_pay');
            $app       = Factory::payment($payConfig);
            return $app;
        });

    }

    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        Schema::defaultStringLength(191);

        //
        Validator::extend('cn_phone', function (...$parameters) {
            return is_phone($parameters[1]);
        });
    }
}
