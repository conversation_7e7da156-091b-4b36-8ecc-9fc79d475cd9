<?php

namespace App\Exceptions;

use Throwable;

class ApiException extends \Exception
{
    public function __construct($msg, $code, Throwable $previous = null)
    {
        parent::__construct($msg, $code, $previous);
    }

    /**
     * Report the exception.
     */
    public function report(): void
    {
        logger()->error($this->getMessage() . ' in ' . $this->getFile() . ':' . $this->getLine());
    }
}
