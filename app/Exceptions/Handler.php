<?php

namespace App\Exceptions;

use App\Constants\ResponseCode;
use App\Traits\ResponseTrait;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Foundation\Exceptions\Handler as ExceptionHandler;
use Illuminate\Support\Facades\Log;
use Illuminate\Validation\ValidationException;
use Symfony\Component\HttpKernel\Exception\MethodNotAllowedHttpException;
use Symfony\Component\HttpKernel\Exception\NotFoundHttpException;
use Throwable;

class Handler extends ExceptionHandler
{
    use ResponseTrait;

    /**
     * A list of the exception types that are not reported.
     *
     * @var array
     */
    protected $dontReport = [
        //
    ];

    /**
     * A list of the inputs that are never flashed for validation exceptions.
     *
     * @var array
     */
    protected $dontFlash = [
        'current_password',
        'password',
        'password_confirmation',
    ];

    /**
     * Register the exception handling callbacks for the application.
     *
     * @return void
     */
    public function register()
    {
        $this->reportable(function (Throwable $e) {
            //
        });
    }

    /**
     * Render an exception into an HTTP response.
     *
     * @param  \Illuminate\Http\Request  $request
     * @param  \Throwable  $exception
     * @return \Symfony\Component\HttpFoundation\Response
     *
     * @throws \Throwable
     */
    public function render($request, Throwable $exception)
    {
        if ($exception instanceof ApiException) {
            return $this->responseError($exception->getMessage(), $exception->getCode());
        } elseif ($exception instanceof NotFoundHttpException) {
            return $this->responseError('Not Found', ResponseCode::NOT_FOUND);
        } elseif ($exception instanceof MethodNotAllowedHttpException) {
            return $this->responseError('Method not allowed', ResponseCode::METHOD_NOT_ALLOWED);
        } elseif ($exception instanceof AuthenticationException) {
            return $this->responseError($exception->getMessage(), ResponseCode::UNAUTH);
        } elseif ($exception instanceof  ValidationException) {
            return $this->responseError($exception->validator->errors()->first(), ResponseCode::PARAM_ERR);
        } elseif ($exception instanceof ModelNotFoundException) {
            return $this->responseError('Not Fount Target', ResponseCode::NOT_FOUND);
        }

        return parent::render($request, $exception);
    }
}
