<?php
require_once 'AopSdk.php';
// require_once 'AlipaySign.php';
header ( "Content-type: text/html; charset=utf-8" );
?>
<!DOCTYPE html PUBLIC "-//WAPFORUM//DTD XHTML Mobile 1.0//EN" "http://www.wapforum.org/DTD/xhtml-mobile10.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
<head>
<title>支付宝当面付2.0</title>
<meta name="viewport"
	content="width=device-width, initial-scale=1.0, user-scalable=yes" />
<meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
</head>
<body>
	
			<br>
			<br><a target="_blank" href="f2fpay/barpay_test.php">当面付2.0  条码支付</a>
			<br><a target="_blank" href="f2fpay/qrpay_test.php">当面付2.0 二维码支付</a>
			<br><a target="_blank" href="f2fpay/query_test.php">当面付2.0  订单查询</a>
			<br><a target="_blank" href="f2fpay/refund_test.php">当面付2.0  订单退款</a>


</body>
</html>
