<?php

namespace App\Models;

class UserFans extends Base
{
    protected $table = 'user_fans';

    public static $TypeMappings  = [
        'course', 'plug', 'goods'
    ];

    public static function getFansStatus($user, $uid)
    {
        if(is_null($user)){
            return true;
        }
        if(!$user){
            return false;
        }
        if($user['id']==$uid){
            return false;
        }
        return !(UserFans::query()
            ->where('fans_id', $user['id'])
            ->where('user_id', $uid)
            ->exists());


        return UserFans::query()->where('user_id', $uid)->count();
    }
    public static function getFansNum($uid, $showfans=true)
    {
        if(!$showfans){
            return $showfans;
        }
        return UserFans::query()->where('user_id', $uid)->count();
    }



    //关注数
    public static function getAttentionNum($uid, $showfans=true)
    {
        if(!$showfans){
            return 0;
        }
        return UserFans::query()->where('fans_id', $uid)->count();
    }

    //粉丝信息
    public  function fansInfo()
    {
        return $this->belongsTo('App\Models\User', 'fans_id', 'id');
    }


    //关注信息
    public  function attentionInfo()
    {
        return $this->belongsTo('App\Models\User', 'user_id', 'id');
    }
}
