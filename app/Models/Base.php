<?php

namespace App\Models;

use DateTimeInterface;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;

/**
 * App\Models\Base
 *
 * @method static Builder|Base hasDelete()
 * @method static Builder|Base newQuery()
 * @method static Builder|Base notDelete()
 * @method static Builder|Base query()
 * @mixin \Eloquent
 */
class Base extends Model
{
    const HAS_DELETE = 1;

    const NOT_DELETE = 0;

    const HIDE = 1;

    const NOT_HIDE = 0;

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeNotDelete(Builder $query)
    {
        return $query->where('is_delete', Base::NOT_DELETE);
    }


    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeHasDelete(Builder $query)
    {
        return $query->where('is_delete', Base::HAS_DELETE);
    }

    /***
     * 格式化时间
     *
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format($this->getDateFormat());
    }
}
