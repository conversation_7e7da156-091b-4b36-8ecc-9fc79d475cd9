<?php

namespace App\Models;

/**
 * App\Models\Certification
 *
 * @property int $id
 * @property int $user_id 关联的用户ID
 * @property string $name 真实姓名
 * @property string $number 证件号
 * @property string $fpic 证件正面照片
 * @property string $bpic 证件反面照片
 * @property int $admin_id 审核管理员ID
 * @property int $status 状态:0-待审核1-成功2-失败
 * @property string|null $reason 审核失败原因
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $user
 */
class Certification extends Base
{
    /**
     * 认证状态映射
     */
    const WAIT_STATUS = 0;
    const SUCC_STATUS = 1;
    const FAIL_STATUS = 2;

    /**
     * 是否进行认证
     *
     * @param integer $userId
     * @return boolean
     */
    public static function isCertificated(int $userId)
    {
        $certificated = static::query()->where('user_id', $userId)->first();
        if ($certificated){
            return  $certificated->status;
        }
        return -1;
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
