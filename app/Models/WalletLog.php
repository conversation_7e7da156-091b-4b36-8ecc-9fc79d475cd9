<?php

namespace App\Models;

class WalletLog extends Base
{
    protected $guarded = [];

    /**
     * 映射的表名
     *
     * @var string
     */
    protected $table = 'wallet_logs';

    /**
     * casts
     * @var array
     */
    protected $casts = ['extend' => 'array'];

    /**
     * 状态类型映射
     *
     * 说明：0-待确认，1-成功，2-失败，3-撤销
     */
    const StatusMappings = [
        'wait' => 0,
        'success' => 1,
        'fail' => 2,
        'repeal' => 3,
    ];

    /**
     * 流水类型
     */
    const TypeMappings = [
        // 商品
        'goods' => 'goods',
        // 插件
        'plug' => 'plug',
        // 课程
        'course' => 'course',
        // 登录
        'login_give' => 'login_give',
        // 兑换
        'exchange' => 'exchange',
        // 售卖
        'sale' => 'sale',
        // 系统扣除
        'system' => 'system',
        //图片
        'image' => 'image',
        //AI Vip
        'ai_vip' => 'ai_vip',
        //chat
        'chat' => 'chat',
        // 管理员充值
        'admin_recharge' => 'admin_recharge'
    ];

    /**
     * 货币类型映射
     */
    const CurrencyMappings = [
        // 默认
        'integral' => 'integral',
    ];

    /**
     * 收入动作映射
     */
    const ActionMappings = [
        // 收入
        'income' => 1,
        // 支出
        'expend' => 2,
    ];

    /**
     * target_type类型映射
     */
    const TargetTypeMappings = [
        'user' => 'user',
        'order' => 'order',
        'recharge_order' => 'recharge_order',
    ];

    /**
     * 目标用户
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function targetUser()
    {
        return $this
            ->belongsTo(User::class, 'target_id', 'id')->where('is_delete', 0);
    }

    /**
     * 自身用户
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id')->where('is_delete', 0);
    }
}
