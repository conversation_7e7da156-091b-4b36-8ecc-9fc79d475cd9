<?php

namespace App\Models;

class UserCourse extends Base
{
    protected $guarded = [];

    /**
     * @var string[]
     */
    protected $appends = ['finish_num', 'progress'];

    /**
     * 类型映射
     */
    const TypeMappings = [
        'collect' => 'collect',
        'learn' => 'learn',
        'material' => 'material',
    ];

    /**
     * 类型名称映射
     */
    const TypeNameMappings = [
        'collect' => '收藏',
        'learn' => '学习',
        'material' => '素材',
    ];

    /**
     * 已观看课程内视频数量
     * @return int
     */
    public function getFinishNumAttribute()
    {
        $user = request()->user('api');
        if ($this->course_id && $user) {
            return Watch::query()
                ->where('user_id', $user->id)
                ->where('course_id', $this->course_id)
                ->where('status', Watch::StatusMappings['finish'])
                ->count();
        }

        return 0;
    }

    /**
     * 观看进度
     * @return float|int
     */
    public function getProgressAttribute()
    {
        $user = request()->user('api');
        if ($this->course_id && $user) {
            $watchTotal = Watch::query()
                ->where('user_id', $user->id)
                ->where('course_id', $this->course_id)
                ->where('status', Watch::StatusMappings['finish'])
                ->count();

            $total = CourseDetail::query()
                ->where('course_id', $this->course_id)
                ->where('type', CourseDetail::TypeValueMappings['task'])
                ->count();

            return $total == 0 ? 0 : bcdiv($watchTotal, $total, 2) * 100;
        }

        return 0;
    }

    /**
     * 课程信息
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function course()
    {
        return $this->belongsTo(Course::class, 'course_id', 'id');
    }

    /**
     * 用户信息
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 管理员
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }
}
