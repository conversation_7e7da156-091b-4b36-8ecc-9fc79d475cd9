<?php

namespace App\Models;

class GoodsCategory extends Base
{
    protected $table = 'goods_category';

    protected $appends = ['level'];

    /**
     * 模型生命周期事件
     */
    protected static function booted()
    {
        static::deleting(function ($_it) {
            if (Goods::query()->where('cate_id', $_it->id)->exists()) {
                throw new \Exception("分类下存在商品，无法删除");
            }
            return true;
        });
    }

    public function getLevelAttribute()
    {
        return $this->parent_id > 0 ? 3 : 2;
    }

    /**
     * 商品
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function goods()
    {
        return $this->hasMany(Goods::class, 'cate_id', 'id');
    }

    /**
     * 父级分类
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function parentCategory()
    {
        return $this->hasOne(GoodsCategory::class, 'id', 'parent_id');
    }

    /**
     * 所有子集分类
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function childrenCategory()
    {
        return $this->hasMany(self::class, 'parent_id', 'id')
            ->orderByDesc('sort')
            ->orderByDesc('id');
    }
}
