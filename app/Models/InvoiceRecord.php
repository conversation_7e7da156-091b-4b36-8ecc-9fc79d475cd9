<?php

namespace App\Models;

class InvoiceRecord extends Base
{
    protected $guarded = [];

    protected $dates = [
        'apply_time',
        'upload_time',
    ];

    /**
     * 开票状态映射
     */
    const StatusMappings = [
        'pending' => 1, // 待处理
        'uploaded' => 2, // 已上传
        'reopen' => 3, // 重开中
    ];

    protected $appends = [
        'status_text',
        'invoice_file_url',
    ];

    /**
     * 用户关联
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 充值订单关联
     */
    public function rechargeOrder()
    {
        return $this->belongsTo(RechargeOrder::class, 'recharge_order_id', 'id');
    }

    /**
     * 发票信息关联
     */
    public function invoiceInfo()
    {
        return $this->belongsTo(InvoiceInfo::class, 'invoice_info_id', 'id');
    }

    /**
     * 获取状态文本
     */
    public function getStatusTextAttribute()
    {
        $statuses = [
            1 => '待处理',
            2 => '已上传',
            3 => '重开中'
        ];
        return $statuses[$this->status] ?? '未知';
    }

    /**
     * 获取发票文件URL
     */
    public function getInvoiceFileUrlAttribute()
    {
        if (empty($this->invoice_file)) {
            return '';
        }
        
        // 如果是完整URL，直接返回
        if (filter_var($this->invoice_file, FILTER_VALIDATE_URL)) {
            return $this->invoice_file;
        }
        
        // 否则拼接应用URL
        return config('app.url') . '/storage/' . $this->invoice_file;
    }

    /**
     * 验证规则
     */
    public static function getValidationRules()
    {
        return [
            'recharge_order_id' => 'required|exists:recharge_orders,id',
            'invoice_info_id' => 'required|exists:invoice_infos,id',
            'invoice_amount' => 'required|numeric|min:0.01',
            'remark' => 'nullable|string|max:500',
        ];
    }

    /**
     * 验证消息
     */
    public static function getValidationMessages()
    {
        return [
            'recharge_order_id.required' => '充值订单不能为空',
            'recharge_order_id.exists' => '充值订单不存在',
            'invoice_info_id.required' => '发票信息不能为空',
            'invoice_info_id.exists' => '发票信息不存在',
            'invoice_amount.required' => '开票金额不能为空',
            'invoice_amount.numeric' => '开票金额必须为数字',
            'invoice_amount.min' => '开票金额不能小于0.01',
            'remark.max' => '备注不能超过500个字符',
        ];
    }
}
