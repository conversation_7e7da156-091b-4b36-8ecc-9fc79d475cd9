<?php

namespace App\Models;

class CommentReply extends Base
{
    protected $table = 'comment_replys';

    /**
     * 主评论
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function comment()
    {
        return $this->hasOne(Comment::class, 'id', 'comment_id');
    }

    /**
     * 上级回复
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function parent()
    {
        return $this->hasOne(CommentReply::class, 'id', 'parent_id');
    }

    /**
     * 用户
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
