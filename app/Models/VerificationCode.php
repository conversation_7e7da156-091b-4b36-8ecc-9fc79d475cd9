<?php

namespace App\Models;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use Carbon\Carbon;

/**
 * App\Models\VerificationCode
 *
 * @property int $id
 * @property string $code 验证码
 * @property string $scene 发送场景
 * @property string $type 验证类型: sms-手机短信 email-邮件类型
 * @property int $status 发送状态: 0-发送失败 1-发送成功
 * @property int $used 使用状态:0-未使用 1-已使用
 * @property string|null $used_at 使用时间
 * @property string $account 发送账号
 * @property string|null $message sms发送后 第三方返回的信息
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
class VerificationCode extends Base
{
    /**
     * 检查验证码是否有效
     * @param string $phone
     * @param string $scene
     * @param string $code
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object
     * @throws ApiException
     */
    public static function check(string $phone, string $scene, string $code)
    {
        if (is_dev()) {
            $verCode = VerificationCode::query()->orderByDesc('id')->first();
        } else {
            $verCode = VerificationCode::query()
                ->where('account', $phone)
                ->where('scene', $scene)
                ->where('used', 0)
                ->orderByDesc('id')
                ->first();
            if (!$verCode || $verCode->code !== $code) {
                throw new ApiException('验证码错误', ResponseCode::PARAM_ERR);
            }
            if (Carbon::parse($verCode->created_at)->diffInSeconds(now()) > 60 * 10) {
                throw new ApiException('验证码已过期', ResponseCode::PARAM_ERR);
            }
        }

        return $verCode;
    }

    /**
     * 使用验证码
     * @return void
     */
    public function used()
    {
        $this->used = 1;
        $this->used_at = now();
        $this->save();
    }
}
