<?php

namespace App\Models;

class PlugDetail extends Base
{
    /**
     * @var string[]
     */
    protected $appends = ['download_link'];

    /**
     * @var string[]
     */
    protected $casts = [
        'platform' => 'array'
    ];
    protected $guarded = [];

    /**
     * 类型映射
     */
    const TypeMappings = [
        // 包
        'package' => 'package',
        // 使用手册
        'manual' => 'manual',
        // 案例
        'case' => 'case'
    ];

    /**
     * 名称映射
     */
    const NameMappings = [
        // 包
        'package' => '下载',
        // 使用手册
        'manual' => '使用手册',
        // 案例
        'case' => '案例'
    ];

    /**
     * 详情
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function plug()
    {
        return $this->belongsTo(Plug::class);
    }

    /**
     * 管理员
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }

    /**
     * 插件介绍
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function plugintro()
    {
        return $this->hasMany(PlugIntro::class, 'plug_id', 'plug_id');
    }

    /**
     * 获取下载链接
     * @return string
     */
    public function getDownloadLinkAttribute()
    {
        return request()->user('api') ? config('app.download_url').'/'.$this->link : '';
    }
}
