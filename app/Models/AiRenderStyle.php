<?php

namespace App\Models;

class AiRenderStyle extends Base
{
    /**
     * @var string
     */
    protected $table = 'ai_render_category';

    /**
     * 父级分类
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function parentCategory()
    {
        return $this->hasOne(AiRenderStyle::class, 'id', 'parent_id');
    }

    /**
     * 所有子集分类
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function childrenCategory()
    {
        return $this->hasMany(AiRenderStyle::class, 'parent_id', 'id')->orderByDesc('sort');
    }
}
