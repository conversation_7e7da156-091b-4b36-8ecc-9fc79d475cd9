<?php

namespace App\Models;

class AiRenderCategory extends Base
{
    protected $table = 'ai_render_category';

    protected $appends = ['level'];

    public function getLevelAttribute()
    {
        return $this->parent_id > 0 ? 3 : 2;
    }

    /**
     * 所有子集分类
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function childrenCategory()
    {
        return $this->hasMany(self::class, 'parent_id', 'id')
            ->orderByDesc('sort')
            ->orderByDesc('id');
    }
}
