<?php

namespace App\Models;

class TagList extends Base
{
    /**
     * @var mixed
     */

    protected $table = 'tag_lists';


    public static  function delTagInfo($params)
    {
        self::query()
            -> where('aid',$params['aid'])
            ->whereNotIn('tag',$params['tag'])
            ->where('type',$params['type'])
            ->delete();

        return $params;
    }

    public static  function saveTagInfo($params)
    {
        foreach ($params['tag'] as $tag){
            $isa = self::query()
                ->where('aid',$params['aid'])
                ->where('tag',$tag)
                ->where('type',$params['type'])
                ->first();
            if ($isa){
                continue;
            }
            self::query()->insert([
                'aid'=>$params['aid'],
                'tag'=>$tag,
                'uid' => 0,
                'type'=>$params['type'],
                'created_at'=>date('Y-m-d H:i:s'),
                'updated_at'=>date('Y-m-d H:i:s')
            ]);
        }
    }


}
