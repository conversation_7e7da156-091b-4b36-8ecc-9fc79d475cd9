<?php

namespace App\Models;

use App\Services\DiscountService;

class Goods extends Base
{
    // 上架状态
    const StatusMapping = [
        'yes'=>1,
        'no'=>0,
    ];
    protected $guarded=[];

    protected $casts = [
        'images' => 'json'
    ];

    /**
     * @var string[]
     */
    protected $with = ['cate:id,name', 'admin:id,name'];

    /**
     * @var string[]
     */
    protected $appends = ['discount_price', 'discount_rate', 'has_discount'];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function cate()
    {
        return $this->belongsTo(GoodsCategory::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function admin()
    {
        return $this->hasOne(Admin::class, 'id', 'admin_id');
    }

    /**
     * 推荐人
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function suggestUser()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    /**
     * 是否购买
     * @return bool
     */
    public function getPurchaseAttribute()
    {
        $user = request()->user('api');
        if ($user) {
            return Order::query()
                ->where('product_id', $this->id)
                ->where('user_id', $user->id)
                ->where('product_type', Order::ProductTypeMappings['goods'])
                ->where('status', Order::StatusMappings['paid'])
                ->exists() ? 1 : 0;
        }
        return 0;
    }


    public function tagList()
    {
        return $this->hasMany(TagList::class, 'aid', 'id')
            ->where('type', 'goods');
    }
    public function tagListInfo()
    {
        return $this->belongsToMany(Tag::class, 'tag_lists', 'aid', 'tag')
            ->where('type', 'goods');
    }



    //用户订单
    public function userorder()
    {
        return $this->belongsTo(Order::class, 'id', 'product_id')
            ->where('status', 1)
            ->where('product_type', 3);
    }

    /**
     * 折扣后价格
     * @return float
     */
    public function getDiscountPriceAttribute()
    {
        $discountInfo = DiscountService::calculateDiscountPrice('goods', $this->price);
        return $discountInfo['discounted_price'];
    }

    /**
     * 折扣率
     * @return float
     */
    public function getDiscountRateAttribute()
    {
        $discountInfo = DiscountService::calculateDiscountPrice('goods', $this->price);
        return $discountInfo['discount_rate'];
    }

    /**
     * 是否有折扣
     * @return bool
     */
    public function getHasDiscountAttribute()
    {
        $discountInfo = DiscountService::calculateDiscountPrice('goods', $this->price);
        return $discountInfo['has_discount'];
    }
}
