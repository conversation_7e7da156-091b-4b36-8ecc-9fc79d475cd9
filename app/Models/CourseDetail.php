<?php

namespace App\Models;

class CourseDetail extends Base
{

    protected $guarded=[];
    // 显示
    const SHOW = 0;
    // 隐藏
    const HIDDEN = 1;

    /**
     * 类型名称映射
     */
    const TypeNameMappings = [
        'task' => '任务',
        'chapter' => '章节',
        'annex' => '附件'
    ];

    /**
     * 类型值映射
     */
    const TypeValueMappings = [
        'task' => 'task',
        'chapter' => 'chapter',
        'annex' => 'annex',
    ];

    /**
     * @var string[]
     */
    protected $appends = ['watch'];

    /**
     * 分类
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function cate()
    {
        return $this->belongsTo(CourseCategory::class, 'cate_id', 'id');
    }

    /**
     * 章节
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function chapter()
    {
        return $this->hasOne(CourseDetail::class, 'id', 'chapter_id')
            ->where('type', CourseDetail::TypeValueMappings['chapter'])
            ->orderBy('sort')
            ->orderBy('name')
            ->orderBy('created_at');
    }

    /**
     * 任务
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function task()
    {
        return $this->hasMany(CourseDetail::class, 'chapter_id', 'id')
            ->where('type', CourseDetail::TypeValueMappings['task'])
            ->where('state',self::SHOW)
            ->selectRaw('chapter_id,course_id,link,name,type,id,created_at,images,type,views,lock_status')
            ->orderBy('sort')
            ->orderBy('name')
            ->orderBy('created_at');
    }

    /**
     * 附件
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function annex()
    {
        return $this->hasMany(CourseDetail::class, 'chapter_id', 'id')
            ->where('type', CourseDetail::TypeValueMappings['annex'])
            ->where('state',self::SHOW)
            ->selectRaw('chapter_id,link,name,type,id')
            ->orderBy('sort')
            ->orderBy('name')
            ->orderBy('created_at');
    }

    /**
     * 课程
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function course()
    {
        return $this->hasOne(Course::class, 'id', 'course_id');
    }

    /**
     * 管理员
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function admin()
    {
        return $this->belongsTo(Admin::class);
    }

    /**
     * 观看时长
     * @return int|mixed
     */
    public function getWatchAttribute()
    {
        $user = request()->user('api');
        if ($user && $this->type == self::TypeValueMappings['task']) {
            return Watch::query()
                ->where('user_id', $user->id)
                ->where('course_detail_id', $this->id)
                ->value('last_time_out') ?: 0;
        }

        return 0;
    }
}
