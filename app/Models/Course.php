<?php

namespace App\Models;

use App\Constants\Common;
use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Services\DiscountService;

class Course extends Base
{
    protected $guarded=[];
    /**
     * @var string[]
     */
    protected $appends = ['sales_num', 'images_src', 'view_num', 'purchase', 'collect', 'is_annex', 'discount_price', 'discount_rate', 'has_discount'];

    /**
     * 课程层级
     */
    const LevelMappings = [
        '0' => '基础',
        '1' => '进阶',
    ];

    /**
     * @var string[]
     */
    protected $with = ['cate:id,name', 'admin:id,name'];

    /**
     * 分类
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function cate()
    {
        return $this->belongsTo(CourseCategory::class, 'cate_id', 'id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function admin()
    {
        return $this->hasOne(Admin::class, 'id', 'admin_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function authorInfo()
    {
        return $this->hasOne(User::class, 'id', 'author_id')
            ->selectRaw('id,name,avatar,sign');
    }


    /**
     * 推荐人
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function suggestUser()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    /**
     * 模型生命周期事件
     */
    protected static function booted()
    {
        static::deleting(function ($_it) {
            if (CourseDetail::query()->where('course_id', $_it->id)->exists()) {
                throw new ApiException("课程下存在详情信息，无法删除", ResponseCode::FORBIDDEN);
            }
            return true;
        });
    }

    /**
     * 图片路径
     * @return string
     */
    public function getImagesSrcAttribute()
    {
        return handle_url($this->images);
    }

    /**
     * 销售数量
     * @return int
     */
    public function getSalesNumAttribute()
    {
        return UserCourse::query()->where('course_id', $this->id)->count();
    }

    /**
     * 观看次数
     * @return int|mixed
     */
    public function getViewNumAttribute()
    {
        return CourseDetail::query()->where('course_id', $this->id)->sum('views');
    }

    /**
     * 是否存在插件
     * @return bool
     */
    public function getIsAnnexAttribute()
    {
        return CourseDetail::query()
            ->where('course_id', $this->id)
            ->where('type', CourseDetail::TypeValueMappings['annex'])
            ->exists() ? 1 : 0;
    }

    /**
     * 是否购买
     * @return bool
     */
    public function getPurchaseAttribute()
    {
        $user = request()->user('api');
        if ($user) {
            return UserCourse::query()
                ->where('course_id', $this->id)
                ->where('user_id', $user->id)
                ->where('type', UserCourse::TypeMappings['learn'])
                ->exists() ? 1 : 0;
        }
        return 0;
    }

    /**
     * 是否收藏
     * @return bool
     */
    public function getCollectAttribute()
    {
        $user = request()->user('api');
        if ($user) {
            return UserCourse::query()
                ->where('course_id', $this->id)
                ->where('user_id', $user->id)
                ->where('type', UserCourse::TypeMappings['collect'])
                ->exists() ? 1 : 0;
        }
        return 0;
    }


    /**
     * 获取上一课程id
     * @param $id
     * @return int
     */
    public function getPrevId($id)
    {
        $id = $this->where('id', "<", $id)
            ->where('is_delete', Course::NOT_DELETE)
            ->where('is_publish', 1)
            ->orderByDesc('sort')
            ->orderByDesc('created_at')
            ->max('id');
        return $id ?: 0;
    }

    /**
     * 获取下一课程id
     * @param $id
     * @return int
     */
    public function getNextId($id)
    {
        $id = $this->where('id', ">", $id)
            ->where('is_delete', Course::NOT_DELETE)
            ->where('is_publish', 1)
            ->orderByDesc('sort')
            ->orderByDesc('created_at')
            ->min('id');
        return $id ?: 0;
    }



    public function tagList()
    {
        return $this->hasMany(TagList::class, 'aid', 'id')
            ->where('type', 'course');
    }
    public function tagListInfo()
    {
        return $this->belongsToMany(Tag::class, 'tag_lists', 'aid', 'tag')
            ->where('type', 'course');
    }

    /**
     * 折扣后价格
     * @return float
     */
    public function getDiscountPriceAttribute()
    {
        $discountInfo = DiscountService::calculateDiscountPrice('course', $this->price);
        return $discountInfo['discounted_price'];
    }

    /**
     * 折扣率
     * @return float
     */
    public function getDiscountRateAttribute()
    {
        $discountInfo = DiscountService::calculateDiscountPrice('course', $this->price);
        return $discountInfo['discount_rate'];
    }

    /**
     * 是否有折扣
     * @return bool
     */
    public function getHasDiscountAttribute()
    {
        $discountInfo = DiscountService::calculateDiscountPrice('course', $this->price);
        return $discountInfo['has_discount'];
    }
}
