<?php

namespace App\Models;

use Carbon\Carbon;

class Order extends Base
{
    /**
     * @var mixed
     */
    private $order_no;

    /**
     * 自动关闭时间
     */
    const expiredMinutes = 30;

    /**
     * 产品类型
     */
    const ProductTypeMappings = [
        'course' => 1,
        'plug' => 2,
        'goods' => 3,
    ];

    /**
     * 订单支付状态映射
     */
    const StatusMappings = [
        'wait'  => 0, // 待支付
        'paid'  => 1, // 已支付
        'close' => 2, // 已关闭
//        'delivery' => 3, // 已发货
//        'receipt' => 4, // 已收货
//        'success' => 5, // 已完成
    ];

    protected $guarded = [];

    protected $casts = [
        'extend_info' => 'json'
    ];

    protected $with = ['consignee', 'logistic'];

    /**
     * 用户
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 下单地址
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function consignee()
    {
        return $this->hasOne(OrderConsignee::class);
    }

    /**
     * 物流公司
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function logistic()
    {
        return $this->hasOne(OrderLogistic::class,'order_id','id');
    }

    /**
     * 生成订单号
     * @return string
     */
    public static function generateOrderNo()
    {
        return date('YmdHi') . \Str::random(9);
    }
}
