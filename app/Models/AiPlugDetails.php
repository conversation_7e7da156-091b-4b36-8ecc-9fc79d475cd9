<?php

namespace App\Models;

class AiPlugDetails extends Base
{
    protected $table = 'ai_plug_details';
    /**
     * @var string[]
     */
    protected $casts = [
        'platform' => 'array'
    ];
    protected $guarded = [];

    /**
     * 类型映射
     */
    const TypeMappings = [
        // 包
        'package' => 'package',
        // 使用手册
        'manual' => 'manual',
        // 案例
        'case' => 'case'
    ];

    /**
     * 名称映射
     */
    const NameMappings = [
        // 包
        'package' => '下载',
        // 使用手册
        'manual' => '使用手册',
        // 案例
        'case' => '案例'
    ];

    /**
     * 详情
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function plug()
    {
        return $this->belongsTo(Plug::class, 'plug_id', 'id');
    }

}
