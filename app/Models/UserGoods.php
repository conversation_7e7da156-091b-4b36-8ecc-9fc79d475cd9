<?php

namespace App\Models;

class UserGoods extends Base
{
    protected $table = 'users_goods';

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function cate()
    {
        return $this->belongsTo(GoodsCategory::class);
    }
    /**
     * 推荐人
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function suggestUser()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
    public function tagList()
    {
        return $this->hasMany(TagList::class, 'uid', 'id')->where('type', 'goods');
    }

    public function tagListInfo()
    {
        return $this->belongsToMany(Tag::class, 'tag_lists', 'uid', 'tag')
            ->where('type', 'goods');
    }
}
