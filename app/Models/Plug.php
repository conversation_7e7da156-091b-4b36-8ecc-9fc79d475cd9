<?php

namespace App\Models;

use App\Services\DiscountService;

class Plug extends Base
{
    /**
     * @var string[]
     */
    protected $with = ['admin:id,name'];

    protected $guarded=[];

    /**
     * @var string[]
     */
    protected $appends = ['discount_price', 'discount_rate', 'has_discount', 'web_link_discount_price'];

    /**
     * 详情
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function detail()
    {
        return $this->hasMany(PlugDetail::class)->orderByDesc('sort');
    }

    /**
     * 介绍
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function intros()
    {
        return $this->hasMany(PlugIntro::class)->orderByDesc('sort');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function admin()
    {
        return $this->hasOne(Admin::class, 'id', 'admin_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function cate()
    {
        return $this->belongsTo(PlugCategory::class);
    }

    /**
     * 推荐人
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function suggestUser()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }

    /**
     * 销售数量
     * @return int
     */
    public function getSalesNumAttribute()
    {
        return Order::query()
            ->where('product_id', $this->id)
            ->where('product_type', Order::ProductTypeMappings['plug'])
            ->where(function ($query) {
                $query->where('extend_info->product_extend_type', '!=', 'web_link')
                    ->orWhereNull('extend_info->product_extend_type');
            })
            ->where('status', Order::StatusMappings['paid'])
            ->count();
    }

    /**
     * 是否购买
     * @return bool
     */
    public function getPurchaseAttribute()
    {
        $user = request()->user('api');
        if ($user) {
            return Order::query()
                ->where('product_id', $this->id)
                ->where('user_id', $user->id)
                ->where('product_type', Order::ProductTypeMappings['plug'])
                ->where(function ($query) {
                    $query->where('extend_info->product_extend_type', '!=', 'web_link')
                        ->orWhereNull('extend_info->product_extend_type');
                })
                ->where('status', Order::StatusMappings['paid'])
                ->exists() ? 1 : 0;
        }
        return 0;
    }

    /**
     * 是否购买功能介绍
     * @return bool
     */
    public function getWebLinkPurchaseAttribute()
    {
        $user = request()->user('api');
        if ($user) {
            return Order::query()
                ->where('product_id', $this->id)
                ->where('user_id', $user->id)
                ->where('product_type', Order::ProductTypeMappings['plug'])
                ->where('extend_info->product_extend_type', 'web_link')
                ->where('status', Order::StatusMappings['paid'])
                ->exists() ? 1 : 0;
        }
        return 0;
    }



    public function tagList()
    {
        return $this->hasMany(TagList::class, 'aid', 'id')
            ->where('type', 'plug');
    }
    public function tagListInfo()
    {
        return $this->belongsToMany(Tag::class, 'tag_lists', 'aid', 'tag')
            ->where('type', 'plug');
    }

    /**
     * 折扣后价格
     * @return float
     */
    public function getDiscountPriceAttribute()
    {
        $discountInfo = DiscountService::calculateDiscountPrice('plug', $this->price);
        return $discountInfo['discounted_price'];
    }

    /**
     * Web链接折扣后价格
     * @return float
     */
    public function getWebLinkDiscountPriceAttribute()
    {
        $discountInfo = DiscountService::calculateDiscountPrice('plug', $this->web_link_price);
        return $discountInfo['discounted_price'];
    }

    /**
     * 折扣率
     * @return float
     */
    public function getDiscountRateAttribute()
    {
        $discountInfo = DiscountService::calculateDiscountPrice('plug', $this->price);
        return $discountInfo['discount_rate'];
    }

    /**
     * 是否有折扣
     * @return bool
     */
    public function getHasDiscountAttribute()
    {
        $discountInfo = DiscountService::calculateDiscountPrice('plug', $this->price);
        return $discountInfo['has_discount'];
    }
}
