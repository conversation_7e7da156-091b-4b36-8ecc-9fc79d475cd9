<?php

namespace App\Models;

use App\Constants\Common;
use App\Constants\ResponseCode;
use App\Exceptions\ApiException;

class AiGallery extends Base
{
    protected $table = 'ai_gallery';
    protected $guarded=[];
    // 上架状态
    const StatusMapping = [
        'yes'=>1,
        'no'=>0,
    ];

    protected $casts = [
        'images' => 'json'
    ];

    /**
     * @var string[]
     */
    protected $with = ['cate:id,name'];

    /**
     * 分类
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function cate()
    {
        return $this->belongsTo(AiGalleryCategory::class, 'cate_id', 'id');
    }

    public function author()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
}
