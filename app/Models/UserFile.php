<?php

namespace App\Models;

class UserFile extends Base
{
    protected $table = 'users_files';

    /**
     * 分类
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function cate()
    {
        return $this->belongsTo(CourseCategory::class, 'cate_id', 'id');
    }
    /**
     * 推荐人
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function suggestUser()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }


    public function tagList()
    {
        return $this->hasMany(TagList::class, 'uid', 'id')->where('type', 'course');
    }
    public function tagListInfo()
    {
        return $this->belongsToMany(Tag::class, 'tag_lists', 'uid', 'tag')
            ->where('type', 'course');
    }
}
