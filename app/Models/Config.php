<?php

namespace App\Models;

/**
 * App\Models\Config
 *
 * @property int $id
 * @property string $key 配置标示
 * @property array $value 配置信息
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
class Config extends Base
{
    protected $table = 'configs';

    protected $casts = ['value' => 'array'];

    protected $fillable = ['id', 'key', 'value', 'created_at', 'updated_at'];

    //  邀请配置
    const INVITE = 'invite';
    /**
     * key映射
     */
    const KeyMappings = [
        'contact' => 'contact',
        'friend' => 'friend',
        'cooperation' => 'cooperation',
        'about' => 'about',
        'register' => 'register',
        'question' => 'question',
        'service' => 'service',
        'private' => 'private',
        'ad' => 'ad',
        'upload_statement' => 'upload_statement',
        'copyright_statement' => 'copyright_statement',
        'download_statement' => 'download_statement',
        'ai_server' => 'ai_server',
        'discount_config' => 'discount_config',
    ];

    /**
     * key值映射
     */
    const KeyNameMappings = [
        'contact' => '联系方式',
        'friend' => '友情链接',
        'cooperation' => '合作伙伴',
        'about' => '关于我们',
        'register' => '注册协议',
        'question' => '常见问题',
        'service' => '服务条款',
        'private' => '隐私协议',
        'ad' => '广告',
        'upload_statement' => '上传声明',
        'copyright_statement' => '版权声明',
        'download_statement' => '下载声明',
        'ai_server' => 'Ai服务器',
        'discount_config' => '折扣配置',
    ];

    /**
     * 获取词典配置
     * @param $key
     * @param null $field
     * @return false|\Illuminate\Database\Eloquent\HigherOrderBuilderProxy|mixed
     */
    public static function getValueByKey($key, $field = null)
    {
        $config = static::query()->where('key', $key)->first();
        if (!$config) {
            return false;
        }
        return $field ? $config->value[$field] : $config->value;
    }
}
