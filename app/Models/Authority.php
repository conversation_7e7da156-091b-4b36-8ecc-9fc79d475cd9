<?php

namespace App\Models;

/**
 * App\Models\Authority
 *
 * @property int $id
 * @property string $name 权限名称
 * @property string $alias 权限别名
 * @property string|null $icon 图标
 * @property string|null $url 跳转地址
 * @property int $order 排序
 * @property int $type 0: 菜单 1: 导航 2: 按钮
 * @property int $pid 父级ID
 * @property int $show 是否显示 0: 不显示 1: 显示
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
class Authority extends Base
{
    protected $table = 'as_authorities';
    /**
     * @var array
     */
    protected $guarded=[];

    /**
     * @var string[]
     */
    protected $hidden = ['created_at', 'updated_at', 'pivot'];

    //  权限类型
    const MENU_TYPE = 0;    //  菜单

    const GPS_TYPE = 1;     //  导航

    const BUTTON_TYPE = 2;  //  按钮

    /**
     * 角色和权限的模型关联关系
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'as_role_authorities', 'authority_id', 'role_id');
    }
}
