<?php

namespace App\Models;

class Socialite extends Base
{
    protected $casts = [
        'auth_user' => 'array',
    ];

    /**
     * 第三方键值映射
     */
    const TypeMappings = [
        'weixin' => 'weixin',
        'weixinweb' => 'weixinweb',
        'github' => 'github',
        'qq' => 'qq',
    ];

    /**
     * 关联用户
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }
}
