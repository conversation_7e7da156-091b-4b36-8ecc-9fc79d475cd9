<?php

namespace App\Models;

class InvoiceInfo extends Base
{
    protected $guarded = [];

    /**
     * 发票类型映射
     */
    const InvoiceTypeMappings = [
        'normal' => 1, // 电子普通发票
        'special' => 2, // 电子专票
    ];

    /**
     * 抬头类型映射
     */
    const HeaderTypeMappings = [
        'personal' => 1, // 个人
        'company' => 2, // 单位
    ];

    protected $appends = ['invoice_type_text', 'header_type_text'];

    /**
     * 用户关联
     */
    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 开票记录关联
     */
    public function invoiceRecords()
    {
        return $this->hasMany(InvoiceRecord::class, 'invoice_info_id', 'id');
    }

    /**
     * 获取发票类型文本
     */
    public function getInvoiceTypeTextAttribute()
    {
        $types = [
            1 => '电子普通发票',
            2 => '电子专票'
        ];
        return $types[$this->invoice_type] ?? '未知';
    }

    /**
     * 获取抬头类型文本
     */
    public function getHeaderTypeTextAttribute()
    {
        $types = [
            1 => '个人',
            2 => '单位'
        ];
        return $types[$this->header_type] ?? '未知';
    }

    /**
     * 验证规则
     */
    public static function getValidationRules($invoiceType = 1, $headerType = 1)
    {
        $rules = [
            'invoice_type' => 'required|in:1,2',
            'invoice_content' => 'required|string|max:100',
            'header_type' => 'required|in:1,2',
            'header_name' => 'required|string|max:100',
        ];

        // 如果是单位抬头，需要额外字段
        if ($headerType == 2) {
            $rules['tax_number'] = 'required|string|max:50';
            $rules['registered_address'] = 'required|string|max:200';
            $rules['registered_phone'] = 'required|string|max:20';
            $rules['bank_name'] = 'required|string|max:100';
            $rules['bank_account'] = 'required|string|max:50';
        } else {
            // 个人抬头时，这些字段可选
            $rules['tax_number'] = 'nullable|string|max:50';
            $rules['registered_address'] = 'nullable|string|max:200';
            $rules['registered_phone'] = 'nullable|string|max:20';
            $rules['bank_name'] = 'nullable|string|max:100';
            $rules['bank_account'] = 'nullable|string|max:50';
        }

        return $rules;
    }

    /**
     * 验证消息
     */
    public static function getValidationMessages()
    {
        return [
            'invoice_type.required' => '发票类型不能为空',
            'invoice_type.in' => '发票类型无效',
            'invoice_content.required' => '发票内容不能为空',
            'invoice_content.max' => '发票内容不能超过100个字符',
            'header_type.required' => '抬头类型不能为空',
            'header_type.in' => '抬头类型无效',
            'header_name.required' => '抬头名称不能为空',
            'header_name.max' => '抬头名称不能超过100个字符',
            'tax_number.required' => '单位税号不能为空',
            'tax_number.max' => '单位税号不能超过50个字符',
            'registered_address.required' => '注册地址不能为空',
            'registered_address.max' => '注册地址不能超过200个字符',
            'registered_phone.required' => '注册电话不能为空',
            'registered_phone.max' => '注册电话不能超过20个字符',
            'bank_name.required' => '开户银行不能为空',
            'bank_name.max' => '开户银行不能超过100个字符',
            'bank_account.required' => '银行账号不能为空',
            'bank_account.max' => '银行账号不能超过50个字符',
        ];
    }
}
