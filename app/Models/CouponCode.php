<?php

namespace App\Models;

class CouponCode extends Base
{
    // 未使用
    const NOT_USE = 0;
    // 已使用
    const HAS_USE = 1;

    /**
     * 类型映射
     */
    const TypeMappings = [
        'platform' => 0,
        'course' => 1,
        'plug' => 2,
        'goods' => 3,
    ];

    protected $appends = ['target'];

    /**
     * 指定用户
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 使用用户
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function useUser()
    {
        return $this->hasOne(User::class, 'id', 'use_user_id');
    }

    public function getTargetAttribute($key)
    {
        if ($this->type == self::TypeMappings['course']) {
            $target = Course::query()->find($this->target_id, ['id', 'name']);
        }

        return $target ?? null;
    }

    /**
     * 获取优惠价格
     * @param $code
     * @param $userId
     * @return \Illuminate\Database\Eloquent\HigherOrderBuilderProxy|int|mixed
     */
    public static function getPrice($code, $userId, $product_type, $product_id = 0) {
        $coupon = self::query()
            ->where('code', $code)
            ->where('used', CouponCode::NOT_USE)
            ->orderByDesc('created_at')
            ->first();

        if (!$coupon) {
            return 0;
        }

        if ($coupon->type != 0 && $coupon->type != $product_type) {
            return 0;
        }

        if ($coupon->end_time && $coupon->end_time < now()) {
            return 0;
        }

        if ($coupon->user_id && $coupon->user_id != $userId) {
            return 0;
        }

        if ($coupon->target_id && $coupon->target_id != $product_id) {
            return 0;
        }

        return $coupon->price;
    }

    /**
     * 优惠码使用
     * @param $code
     * @param int $userId
     */
    public static function use($code, $userId = 0) {
        self::query()
            ->where('code', $code)
            ->update([
                'used' => CouponCode::HAS_USE,
                'use_user_id' => $userId,
            ]);
    }
}
