<?php

namespace App\Models;

class Wallet extends Base
{
    protected $guarded = [];

    /**
     * @var string[]
     */
    protected $hidden = ['created_at', 'updated_at'];

    /**
     * 初始化用户钱包
     * @param int $uid
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|static
     */
    public static function init(int $uid)
    {
        $wallet = static::query()->where('user_id', $uid)->first();
        if ($wallet) {
            return $wallet;
        }
        $wallet = new static();
        $wallet->user_id = $uid;
        $wallet->balance = '0.00';
        $wallet->income = '0.00';
        $wallet->expend = '0.00';
        $wallet->save();
        return $wallet;
    }
}
