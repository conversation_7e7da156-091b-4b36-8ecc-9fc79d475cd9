<?php

namespace App\Models;

class CourseLevel extends Base
{
    /**
     * 模型生命周期事件
     */
    protected static function booted()
    {
        static::deleting(function ($_it) {
            if (Course::query()->where('level', $_it->id)->exists()) {
                throw new \Exception("层级下存在课程，无法删除");
            }
            return true;
        });
    }

    /**
     * ID=>名称 映射
     * @return array
     */
    public static function mapping()
    {
        return self::query()
            ->get()
            ->mapWithKeys(function ($data) {
                return [$data->id => $data->title];
            })->toArray();
    }
}
