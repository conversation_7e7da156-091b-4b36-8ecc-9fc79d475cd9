<?php

namespace App\Models;

/**
 * App\Models\Role
 *
 * @property int $id
 * @property string $name 角色名称
 * @property string $alias 角色别名
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 */
class Role extends Base
{
    protected $table='as_roles';

    /**
     * @var string[]
     */
    protected $fillable = ['name', 'alias'];

    /**
     * @var string[]
     */
    protected $hidden = ['created_at', 'updated_at', 'pivot'];

    /**
     * 用户和角色的模型关联关系
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function admins()
    {
        return $this->belongsToMany(Admin::class, 'as_admin_roles');
    }

    /**
     * 角色和权限的模型关联关系
     *
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function permissions()
    {
        return $this->belongsToMany(Authority::class, 'as_role_authorities');
    }
}
