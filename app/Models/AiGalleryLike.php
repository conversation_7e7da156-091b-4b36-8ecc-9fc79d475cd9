<?php

namespace App\Models;

class AiGalleryLike extends Base
{
    protected $table = 'ai_gallery_like';

    public static function like(int $uid, int $gid)
    {
        $like = static::query()->where('user_id', $uid)->where('gid', $gid)->first();
        if ($like) {
            $like->delete();
        } else {
            $like = new static();
            $like->user_id = $uid;
            $like->gid = $gid;
            
            $like->save();
        }
            
        return true;
    }

    public static function isLike(int $uid, int $gid)
    {
        $like = static::query()->where('user_id', $uid)->where('gid', $gid)->first();
        if ($like) {
            return true;
        }

        return false;
    }

    public static function likeCount(int $gid)
    {
        return static::query()->where('gid', $gid)->count();
    }

}
