<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Facades\Hash;
use <PERSON><PERSON>\Sanctum\HasApiTokens;

class User extends Authenticatable
{
    protected $connection='mysql';
    use HasApiTokens, HasFactory, Notifiable;

    // 禁用
    const HAS_STATE = 1;
    // 未禁用
    const NOT_STATE = 0;
    /**
     * The attributes that are mass assignable.
     *
     * @var string[]
     */
//    protected $fillable = [];
    protected $guarded = [];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var array
     */
    protected $hidden = ['password', 'api_token'];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
    ];

    /**
     * @var string[]
     */
    protected $appends = ['is_pass', 'is_problem', 'is_wechat', 'is_qq'];

    /**
     * 模型生命周期事件
     */
    protected static function booted()
    {
        static::deleting(function ($_it) {
            $commentIds = Comment::query()->where('user_id', $_it->id)->pluck('id')->toArray();
            CommentReply::query()
                ->whereIn('comment_id', $commentIds)
                ->orWhere('user_id', $_it->id)
                ->update(['is_delete'=>Base::HAS_DELETE]);
            Comment::query()
                ->where('user_id', $_it->id)
                ->update(['is_delete'=>Base::HAS_DELETE]);
            return true;
        });
    }

    /**
     * 设置用户密码
     *
     * @param string $value
     */
    public function setPasswordAttribute(string $value)
    {
        $this->attributes['password'] = Hash::make($value);
    }

    /**
     * API TOKEN
     *
     * @param string $value
     */
    public function setApiTokenAttribute(string $value)
    {
        $this->attributes['api_token'] = md5($value);
    }

    /**
     * 是否设置密码
     * @return bool
     */
    public function getIsPassAttribute()
    {
        return $this->password ? true : false;
    }

    /**
     * 是否设置密码
     * @return bool
     */
    public function getIsProblemAttribute()
    {
        return SecurityAnswer::query()->where('user_id', $this->id)->exists();
    }

    /**
     * 是否绑定微信
     * @return bool
     */
    public function getIsWechatAttribute()
    {
        return Socialite::query()
            ->where('user_id', $this->id)
            ->where('type', Socialite::TypeMappings['weixinweb'])
            ->exists();
    }

    /**
     * 是否绑定QQ
     * @return bool
     */
    public function getIsQQAttribute()
    {
        return Socialite::query()
            ->where('user_id', $this->id)
            ->where('type', Socialite::TypeMappings['qq'])
            ->exists();
    }

    /**
     * @param \DateTimeInterface $date
     * @return string
     */
    protected function serializeDate(\DateTimeInterface $date): string
    {
        return $date->format($this->getDateFormat());
    }

    /**
     * 用户认证信息
     *
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function certification()
    {
        return $this->hasOne(Certification::class, 'user_id', 'id')->where('status', 1);
    }

    /**
     * 授权信息
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function socialite()
    {
        return $this->hasMany(Socialite::class, 'user_id', 'id');
    }


    public function courses()
    {
        return $this->hasMany(Course::class, 'user_id', 'id');
    }
    
    public function vip()
    {
        return $this->hasOne(AiVip::class, 'user_id', 'id');
    }
}
