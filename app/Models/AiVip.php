<?php

namespace App\Models;

class AiVip extends Base
{
    /**
     * @var string[]
     */
    protected $table = 'ai_vip';

    protected $guarded=[];

    public function user()
    {
        // return $this->hasOne(User::class, 'id', 'user_id');
        return $this->belongsTo(User::class, 'user_id');
    }

    public function getEndtimeAttribute($value)
    {
        return $value ? date('Y-m-d', $value) : '';
    }

    public function setEndtimeAttribute($value)
    {
        $this->attributes['endtime'] = strtotime($value);
    }

    public static function isVip(int $uid)
    {
        $vip = AiVip::query()->where('user_id', $uid)->first();
        if ($vip) {
            if ($vip->permanent) {//永久
                return true;
            } else { //有效期
                if(strtotime($vip->endtime) > time()) {
                    return true;
                }
            }
        }

        return false;
    }

}
