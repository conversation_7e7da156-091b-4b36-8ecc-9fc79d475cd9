<?php

namespace App\Models;

class News extends Base
{
    protected $table = 'news';

    protected $guarded = [];

    /**
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function cate()
    {
        return $this->belongsTo(NewCategory::class);
    }

    public function tagList()
    {
        return $this->hasMany(TagList::class, 'aid', 'id')
            ->where('type', 'news');
    }
    public function tagListInfo()
    {
        return $this->belongsToMany(Tag::class, 'tag_lists', 'aid', 'tag')
            ->where('type', 'news');
    }
}
