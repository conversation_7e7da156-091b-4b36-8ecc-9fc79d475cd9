<?php

namespace App\Models;

class RechargeOrder extends Base
{
    protected $guarded = [];

    /**
     * 订单支付状态映射
     */
    const StatusMappings = [
        'wait'  => 0, // 待支付
        'paid'  => 1, // 已支付
        'close' => 2, // 已关闭
    ];

    /**
     * 开票状态映射
     */
    const InvoiceStatusMappings = [
        'none' => 0, // 未开票
        'applied' => 1, // 已申请
        'completed' => 2, // 已完成
    ];

    public function user()
    {
        return $this->belongsTo(User::class, 'user_id', 'id');
    }

    /**
     * 开票记录关联
     */
    public function invoiceRecords()
    {
        return $this->hasMany(InvoiceRecord::class, 'recharge_order_id', 'id');
    }

    /**
     * 最新开票记录
     */
    public function latestInvoiceRecord()
    {
        return $this->hasOne(InvoiceRecord::class, 'recharge_order_id', 'id')
            ->orderByDesc('id');
    }

    /**
     * 获取开票状态文本
     */
    public function getInvoiceStatusTextAttribute()
    {
        $statuses = [
            0 => '未开票',
            1 => '已申请',
            2 => '已完成'
        ];
        return $statuses[$this->invoice_status] ?? '未知';
    }

    /**
     * 生成订单号
     * @return string
     */
    public static function generateOrderNo()
    {
        return date('YmdHi') . \Str::random(9);
    }
}
