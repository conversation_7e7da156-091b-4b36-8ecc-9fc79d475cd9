<?php

namespace App\Models;

class NewCategory extends Base
{
    protected $table = 'new_category';

    /**
     * 模型生命周期事件
     */
    protected static function booted()
    {
        static::deleting(function ($_it) {
            if (News::query()->where('cate_id', $_it->id)->exists()) {
                throw new \Exception("分类下存在新闻，无法删除");
            }
            return true;
        });
    }

    /**
     * 商品
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function news()
    {
        return $this->hasMany(News::class, 'cate_id', 'id');
    }
}
