<?php

namespace App\Models;

use Illuminate\Support\Facades\Hash;

/**
 * Class Admin
 * @package App\Models
 */
class Admin extends Base
{
    protected $table='as_admins';

    /**
     * @var string[]
     */
    protected $fillable = [
        'name', 'phone', 'state', 'password', 'admin_token','email', 'user_id'
    ];

    /**
     * @var string[]
     */
    protected $hidden = [
        'password',
    ];

    /**
     * 管理员状态映射
     */
    const StateMapping = [
        'normal' => 0,
        'disable' => 1
    ];

    /**
     * 删除状态映射
     */
    const DeleteMapping = [
        'no' => 0,
        'yes' => 1
    ];

    /**
     * 用户和角色的模型关联关系
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function roles()
    {
        return $this->belongsToMany(Role::class, 'as_admin_roles');
    }

    /**
     * 用户是否有权限
     *
     * @param $alias
     * @return bool
     */
    public function hasPermissions($alias)
    {
        foreach ($this->roles as $role) {
            if ($role->permissions()->where('alias', $alias)->exists()) {
                return true;
            }
        }

        return false;
    }

    /**
     * 密码hash
     *
     * @param $value
     */
    public function setPasswordAttribute($value)
    {
        $this->attributes['password'] = Hash::make($value);
    }

    public function user()
    {
        return $this->hasOne(User::class, 'id', 'user_id');
    }
}
