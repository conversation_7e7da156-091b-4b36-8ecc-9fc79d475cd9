<?php

namespace App\Models;

class AiNews extends Base
{
    protected $table = 'ai_news';

    protected $appends = ['cate'];

    protected $guarded = [];

    const CateMappings = [
        '1' => '知识',
        '2' => '资讯',
    ];

    public $map = [
        1 => '知识',
        2 => '资讯'
    ];

    public function getCateAttribute($value)
    {
        return $this->cate_id ? $this->map[$this->cate_id] : '';
    }

}
