<?php

namespace App\Models;

class Comment extends Base
{
    /**
     * @var string[]
     */
//    protected $with = ['reply'];

    /**
     * 类型映射
     */
    const TypeMappings = [
        'task' => 'task',
        'news' => 'news',
        'goods' => 'goods',
    ];

    /**
     * 模型生命周期事件
     */
    protected static function booted()
    {
        static::deleting(function ($_it) {
            $replyIds = CommentReply::query()->where('comment_id', $_it->id)->pluck('id')->toArray();
            CommentReply::query()->whereIn('parent_id', $replyIds)->update(['is_delete'=>1]);
            CommentReply::query()->where('comment_id', $_it->id)->update(['is_delete'=>1]);
            return true;
        });
    }

    /**
     * 回复
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function reply()
    {
        return $this->hasMany(CommentReply::class, 'comment_id', 'id')->where('is_delete', Base::NOT_DELETE);
    }

    /**
     * 用户
     * @return \Illuminate\Database\Eloquent\Relations\BelongsTo
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function goods()
    {
        return $this->hasOne(Goods::class, 'id', 'target_id');
    }

    /**
     * @return \Illuminate\Database\Eloquent\Relations\HasOne
     */
    public function course()
    {
        return $this->hasOne(Course::class, 'id', 'target_id');
    }
}
