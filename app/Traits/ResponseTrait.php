<?php

namespace App\Traits;

use App\Constants\ResponseCode;

trait ResponseTrait
{
    /**
     * 成功响应
     *
     * @param object|array $data
     * @param string $message
     * @return \Illuminate\Http\JsonResponse
     */
    public function responseSuccess($data = null, $message = 'success')
    {
        return response()->json([
            'msg' => $message,
            'code' => ResponseCode::SUCCESS,
            'data' => $data,
        ]);
    }

    /**
     * 错误响应.
     *
     * @param string $message
     * @param int $code
     * @param mixed $errors
     * @return \Illuminate\Http\JsonResponse
     */
    public function responseError($message, $code, $errors = null)
    {
        return response()->json([
            'msg' => $message,
            'code' => $code,
            'errors' => $errors
        ]);
    }
}
