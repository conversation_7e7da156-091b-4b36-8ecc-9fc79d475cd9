<?php

namespace App\Traits;

use App\Models\Like;
use App\Constants\ResponseCode;
use App\Exceptions\ApiException;

trait Likeable
{
    /**
     * 用户点赞
     *
     * @param int $userId
     * @throws ApiException
     */
    public function doLike(int $userId)
    {
        $like = Like::where('user_id', $userId)
            ->where('target_id', $this->id)
            ->where('target_type', get_class_name(__CLASS__))
            ->first();

        if ($like) {
            if ($like->status === Like::HAS_LIKE) {
                throw new ApiException('已点赞', ResponseCode::FORBIDDEN);
            }
            if ($like->status === Like::NOT_LIKE) {
                $like->status = Like::HAS_LIKE;
                $like->save();
            }
        } else {
            $like = new Like();
            $like->user_id = $userId;
            $like->target_id = $this->id;
            $like->target_type = get_class_name(__CLASS__);
            $like->status = Like::HAS_LIKE;
            $like->save();
        }
    }

    /**
     * 取消点赞
     *
     * @param int $userId
     * @throws ApiException
     */
    public function unLike(int $userId)
    {
        $like = Like::where('user_id', $userId)
            ->where('target_id', $this->id)
            ->where('target_type', get_class_name(__CLASS__))
            ->first();

        if ($like) {
            if ($like->status === Like::NOT_LIKE) {
                throw new ApiException('未点赞', ResponseCode::FORBIDDEN);
            }
            $like->status = Like::NOT_LIKE;
            $like->save();
        } else {
            throw new ApiException('未点赞', ResponseCode::FORBIDDEN);
        }
    }

    /**
     * 是否点赞
     *
     * @param int $userId
     * @return bool
     */
    public function isLike(int $userId)
    {
        if (!$userId) {
            return false;
        }

        $like = Like::where('user_id', $userId)
            ->where('target_id', $this->id)
            ->where('target_type', get_class_name(__CLASS__))
            ->where('status',  Like::HAS_LIKE)
            ->first();

        return $like ? true : false;
    }
}
