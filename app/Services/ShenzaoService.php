<?php

namespace App\Services;

use Carbon\Carbon;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class ShenzaoService
{
    private $client;
    private $licenseCode;

    public function __construct()
    {
        $baseURI = config('shenzao.url');
        if (!$baseURI) {
            Log::error('Invalid request address');
            return;
        }
        $licenseCode = config('shenzao.license_code');
        if (!$licenseCode) {
            Log::error('Invalid request licenseCode');
            return;
        }
        $this->licenseCode = $licenseCode;

        $this->client = new Client(['base_uri' => $baseURI, 'timeout' => 120]);
    }

    /**
     * 获取授权码信息
     * @return false|mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getByLicenseCode()
    {
        $data = [
            'licenseCode' => $this->licenseCode,
        ];
        try {
            $res = $this->client->post('/third-party/third-party/getByLicenseCode', [
                'form_params' => $data
            ]);
            $content = json_decode($res->getBody()->getContents(), true);

            if ($content['success'] == true && $content['code'] == 200) {
                return true;
            }
        } catch (\Exception $exception) {
            Log::error('getByLicenseCode：', [ $this->licenseCode, $exception->getMessage()]);
        }

        return false;
    }

    /**
     * 获取授权码信息
     * @return false|mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function getUserByUserName($userName)
    {
        $data = [
            'licenseCode' => $this->licenseCode,
            'isAccurate' => true,
            'isBlack' => false,
            'pageNumber' => 1,
            'pageSize' => 20,
            'sort' => "authTime",
            'userName' => $userName,
        ];
        try {
            $res = $this->client->post('/third-party/third-party/pagePlayerInfoList', [
                'json' => $data
            ]);
            $content = json_decode($res->getBody()->getContents(), true);

            if ($content['success'] == true && $content['code'] == 200) {
                return current($content['data']['item']);
            }

            Log::info('getUserByUserName content：', [$userName, $this->licenseCode, $content]);
        } catch (\Exception $exception) {
            Log::error('getUserByUserName：', [$userName, $this->licenseCode, $exception->getMessage()]);
        }

        return null;
    }

    /**
     * 授权用户课程权限
     * @return false|mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function authorizeUserCourseInfo($userName, $type, $courseId, $expireDay=0)
    {
        $data = $this->getUserCourseInfo($userName);

        if ($type != 1) {
            $data['courseInfos'] =  array_values(array_filter(
                $data['courseInfos'],
                function ($item) use ($courseId) {
                    return $courseId !== $item['courseId'];
                }
            ));

            // 因深造无法单独取消最后一个课程，故给予一个不存在对课程id来达到目的
            if (!$data['courseInfos']) {
                $data['courseInfos'] = [
                    ['courseId' => '111', 'expireDay' => 0]
                ];
            }
        } else {
            $data['courseInfos'][] = [
                'courseId' => $courseId,
                'expireDay' => $expireDay,
            ];
        }
        $data['licenseCode'] = $this->licenseCode;
        try {
            $res = $this->client->post('/third-party/third-party/savePlayerInfo', [
                'json' => $data
            ]);
            $content = json_decode($res->getBody()->getContents(), true);

            if ($content['success'] == true && $content['code'] == 200) {
                return true;
            }
            Log::info('authorizeUserCourseInfo content：', [$content, $data]);
        } catch (\Exception $exception) {
            Log::error('authorizeUserCourseInfo：', [$exception->getMessage(), $data]);
        }

        return false;
    }
    /**
     * 处理用户授权课程信息
     * @param $userName
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function getUserCourseInfo($userName)
    {
        $userInfo = $this->getUserByUserName($userName);
        if (!$userInfo) {
            $date = Carbon::now()->addDays(9999)->format('Y-m-d H:i:s');

            $data = [
                "userName" => $userName,
                // 随机验证方式
                "authentication" => 1,
                // 绑定方式,可用值:HARDWARE,MOVE,MOVE2,USER，分别代表：绑定硬件、默认绑定、绑定U盘、绑定账号
                "bindingMode" => "1",
                // 绑定平台
                "bindingPlatform" => "Windows,Android,IOS,MAC",
                "bindingVal" => "HD",
                // 语音播报的时间间隔s
                "broainte" => 600,
                // 采集卡：0或者1'
                "captureCard" => "0",
                // 验证时间（分钟）
                "checkTime" => 10,
                // 关闭进程 0或者1
                "closeProcess" => "0",
                // 是否关闭进程 默认0
                "closeSoftware" => 0,
                // 防摄像机：0-关闭，3-人脸+硬件
                "faceRecognition" => "0",
                // 流量限制
                "fluxLimit" => 200001000,
                // 是否允许多显示器
                "multimonr" => 0,
                // 是否开启云视频
                "openCloudVideo" => "true",
                "playerMachineMaxNumber" => 4,
                "playerStrictions" => 0,
                "qq" => "",
                //实名播放：01-未开启，02-已开启，03-已上传，04-未上传，05-未通过，06-已通过
                "realNamePlayer" => "02",
                "regTime" => $date,
                // 绑定微信：0-否，1-是
                "screenshot" => 1,
                // 人脸登录：0-关闭，1-开启，2-已注册
                "skin" => "0",
                // 软件翻录检查：0-否 1-是
                "softwareRippingCheck" => "0",
                // 声音检测 默认0
                "sounddeten" => 0,
                // 当前电脑限制登录用户数量/不同老师的数量
                "teacherIpNum" => 5,
                "teacherMacNum" => 6,
                "watermark" => "犀光_www.radirhino.com",
                "watermarkSize" => 5,
                "courseInfos" => []
            ];
        } else {
            $courseInfos = array_map(function ($value) {
                return [
                    'courseId' => $value['courseId'],
                    'expireDay' => $value['expireDay'],
                ];
            }, $userInfo['courseInfos'] ?? []) ?: [];

            $data = [
                "userName" => $userName,
                "authentication" => $userInfo['authentication'],
                "bindingMode" => (string)$userInfo['bindingMode'],
                "bindingPlatform" => $userInfo['bindingPlatform'],
                "bindingVal" => $userInfo['bindingVal'],
                "broainte" => $userInfo['broainte'],
                "captureCard" => (string)$userInfo['captureCard'],
                "checkTime" => $userInfo['checkTime'],
                "closeProcess" => (string)$userInfo['closeProcess'],
                "closeSoftware" => $userInfo['closeSoftware'],
                "faceRecognition" => $userInfo['faceRecognition'],
                "fluxLimit" => $userInfo['fluxLimit'],
                "multimonr" => $userInfo['multimonr'],
                "openCloudVideo" => $userInfo['openCloudVideo'],
                "playerMachineMaxNumber" => $userInfo['playerMachineMaxNumber'],
                "playerStrictions" => $userInfo['playerStrictions'],
                "qq" => $userInfo['qq'],
                "realNamePlayer" => $userInfo['realNamePlayer'],
                "regTime" => $userInfo['regTime'],
                "screenshot" => $userInfo['screenshot'] ? 1 : 0,
                "skin" => $userInfo['skin'],
                "softwareRippingCheck" => (string)$userInfo['softwareRippingCheck'],
                "sounddeten" => $userInfo['sounddeten'],
                "teacherIpNum" => $userInfo['teacherIpNum'],
                "watermark" => $userInfo['watermark'],
                "watermarkSize" => $userInfo['watermarkSize'],
                "courseInfos" => $courseInfos,
            ];
        }
        return $data;
    }

    /**
     * 授权用户课程权限
     * @return false|mixed
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function findByLicenseCode($courseId='', $courseName='', $authType='')
    {
        $data = [
            'licenseCode' => $this->licenseCode,
        ];
        if ($courseId) {
            $data['courseId'] = $courseId;
        }
        if ($courseName) {
            $data['courseName'] = $courseName;
        }
        if ($authType) {
            $data['authType'] = $authType;
        }
        try {
            $res = $this->client->post('/third-party/third-party/findByLicenseCode', [
                'form_params' => $data
            ]);
            $content = json_decode($res->getBody()->getContents(), true);

            if ($content['success'] == true && $content['code'] == 200) {
                return $content['data'];
            }

            Log::info('findByLicenseCode content：', [$content, $data]);
        } catch (\Exception $exception) {
            Log::error('findByLicenseCode：', [$exception->getMessage(), $data]);
        }

        return null;
    }
}
