<?php
/**
 * 支付回调类
 * @package App\Services
 *
 * <AUTHOR> <<EMAIL>>
 */

namespace App\Services;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Models\Order;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Log;

class NotifyService
{
    public function alipayNotifyValidate($param)
    {
        if ( ! self::validate($param)) {
            return;
        }

        require_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../Libs/alipay/aop/AopClient.php';
        $config                  = config('web.alipay');
        $aop                     = new \AopClient;
        $aop->appId              = $config['app_id'];
        $aop->rsaPrivateKey      = $config['merchant_private_key'];
        $aop->alipayrsaPublicKey = $config['alipay_public_key'];
        $aop->signType           = $config['sign_type'];
        $result                  = $aop->rsaCheckV1($_POST, '', $config['sign_type']);
        if ($result) {
            Log::info($_POST);
            if ($param['trade_status'] == 'TRADE_FINISHED' || $param['trade_status'] == 'TRADE_SUCCESS') {
                // 处理支付成功后的逻辑业务
                $state = ApiService::handlePayNotifyV1($param['out_trade_no'], $param['trade_no']);
                if ($state) {
                    Log::info('trade_status is success:' . $param['out_trade_no']);
                    echo 'success';
                    return;
                }
            }
            Log::error('trade_status is error:' . $param['$out_trade_no']);
            echo 'fail';
            return;
        }
        Log::error('sign verify is error:' . var_export($_POST));
        echo 'fail';
        return;
    }
}
