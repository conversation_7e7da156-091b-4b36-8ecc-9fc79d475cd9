<?php

namespace App\Services;

use App\Models\Config;
use Carbon\Carbon;

class DiscountService
{
    /**
     * 折扣配置key
     */
    const DISCOUNT_CONFIG_KEY = 'discount_config';

    /**
     * 产品类型映射
     */
    const PRODUCT_TYPES = [
        'course' => '课程',
        'plug' => '插件', 
        'goods' => '周边'
    ];

    /**
     * 获取折扣配置
     * 
     * @param string|null $type 产品类型 course/plug/goods，为空则返回全部
     * @return array|false
     */
    public static function getDiscountConfig($type = null)
    {
        $config = Config::getValueByKey(self::DISCOUNT_CONFIG_KEY);
        
        if (!$config) {
            return false;
        }

        if ($type && isset($config[$type])) {
            return $config[$type];
        }

        return $type ? false : $config;
    }

    /**
     * 设置折扣配置
     * 
     * @param string $type 产品类型
     * @param array $data 配置数据
     * @return bool
     */
    public static function setDiscountConfig($type, $data)
    {
        if (!in_array($type, array_keys(self::PRODUCT_TYPES))) {
            return false;
        }

        try {
            $config = Config::query()->where('key', self::DISCOUNT_CONFIG_KEY)->first();
            if (!$config) {
                $config = new Config();
                $config->key = self::DISCOUNT_CONFIG_KEY;
                $config->value = [];
            }

            $currentValue = $config->value ?: [];
            $currentValue[$type] = [
                'discount' => (int)$data['discount'], // 折扣数乘100存储
                'start_time' => $data['start_time'],
                'end_time' => $data['end_time'],
                'enabled' => (bool)($data['enabled'] ?? false)
            ];

            $config->value = $currentValue;
            $res = $config->save();
            logger('setDiscountConfig', [$res]);

            return $res;
        } catch (\Exception $e) {
            logger('setDiscountConfig' . $e);
            return false;
        }
    }

    /**
     * 计算折扣后价格
     * 
     * @param string $type 产品类型
     * @param float $originalPrice 原价
     * @return array ['discounted_price' => 折扣后价格, 'discount_amount' => 折扣金额, 'discount_rate' => 折扣率]
     */
    public static function calculateDiscountPrice($type, $originalPrice)
    {
        $result = [
            'discounted_price' => $originalPrice,
            'discount_amount' => 0,
            'discount_rate' => 0,
            'has_discount' => false
        ];

        $discountConfig = self::getDiscountConfig($type);
        
        if (!$discountConfig || !$discountConfig['enabled']) {
            return $result;
        }

        // 检查时间范围
        $now = Carbon::now();
        $startTime = Carbon::parse($discountConfig['start_time']);
        $endTime = Carbon::parse($discountConfig['end_time']);

        if ($now->lt($startTime) || $now->gt($endTime)) {
            return $result;
        }

        // 计算折扣
        $discountRate = $discountConfig['discount'] / 100; // 将存储的整数转换为小数
        $discountedPrice = bcmul($originalPrice, $discountRate, 2);
        $discountAmount = bcsub($originalPrice, $discountedPrice, 2);

        return [
            'discounted_price' => $discountedPrice,
            'discount_amount' => $discountAmount,
            'discount_rate' => $discountRate,
            'has_discount' => true
        ];
    }

    /**
     * 检查折扣是否有效
     * 
     * @param string $type 产品类型
     * @return bool
     */
    public static function isDiscountValid($type)
    {
        $discountConfig = self::getDiscountConfig($type);
        
        if (!$discountConfig || !$discountConfig['enabled']) {
            return false;
        }

        $now = Carbon::now();
        $startTime = Carbon::parse($discountConfig['start_time']);
        $endTime = Carbon::parse($discountConfig['end_time']);

        return $now->gte($startTime) && $now->lte($endTime);
    }

    /**
     * 获取所有产品类型
     * 
     * @return array
     */
    public static function getProductTypes()
    {
        return self::PRODUCT_TYPES;
    }
}
