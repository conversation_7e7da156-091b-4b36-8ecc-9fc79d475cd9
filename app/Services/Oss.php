<?php

namespace App\Services;

use AlibabaCloud\Client\AlibabaCloud;
use AlibabaCloud\Client\Exception\ClientException;
use AlibabaCloud\Client\Exception\ServerException;
use Illuminate\Support\Facades\Log;
use OSS\Core\OssException;
use OSS\OssClient;

class Oss extends BaseServices
{
    public function getTempAccess()
    {
        Log::info(config('oss'));
        //构建一个阿里云客户端，用于发起请求。
        //构建阿里云客户端时需要设置AccessKey ID和AccessKey Secret。
        AlibabaCloud::accessKeyClient(config('oss.access_key_id'), config('oss.access_key_secret'))
            ->regionId(config('oss.region_id')?:'cn-hangzhou')
            ->asDefaultClient();
        //设置参数，发起请求。
        try {
            $result = AlibabaCloud::rpc()
                ->product('Sts')
                 ->scheme('https') // https | http
                ->version('2015-04-01')
                ->action('AssumeRole')
                ->method('POST')
                ->host('sts.aliyuncs.com')
                ->options([
                    'query' => [
                        'RegionId' => config('oss.region_id')?:"cn-hangzhou",
                        'RoleArn' => config('oss.role_arn'),
                        'RoleSessionName' => 'alice',
                    ],
                ])
                ->request();
            $oss = $result->toArray()['Credentials'];
            $oss['BucketName']=config('oss.bucket_name');
            $oss['EndPoint']=config('oss.endpoint');
            $oss['DoMain']=config('oss.domain');
           return $oss;
        } catch (ClientException $e) {
            Log::error($e);
            echo $e->getErrorMessage() . PHP_EOL;
        } catch (ServerException $e) {
            Log::error($e);
            echo $e->getErrorMessage() . PHP_EOL;
        }
    }
    public function putObj($object,$content)
    {
        try{
            $ossClient = new OssClient(config('oss.access_key_id'), config('oss.access_key_secret'), config('oss.endpoint'));
            return $ossClient->putObject(config('oss.bucket_name'), $object, $content);
        } catch(OssException $e) {
            Log::info($e->getMessage());
            return false;
        }
    }
    public function objectExist($object)
    {
        try{
            $ossClient = new OssClient(config('oss.access_key_id'), config('oss.access_key_secret'), config('oss.endpoint'));
            return $ossClient->doesObjectExist(config('oss.bucket_name'), $object);
        } catch(OssException $e) {
            Log::info($e->getMessage());
            return false;
        }
    }


    public function uploadFile($object,$content)
    {
        try{
            $ossClient = new OssClient(config('oss.access_key_id'), config('oss.access_key_secret'), config('oss.endpoint'));
            return $ossClient->uploadFile(config('oss.bucket_name'), $object, $content);
        } catch(OssException $e) {
            Log::info($e->getMessage());
            return false;
        }
    }

}
