<?php
namespace App\Services;

use App\Models\Wallet;
use App\Models\WalletLog;
use Illuminate\Support\Facades\DB;

class IntegralService extends BaseServices
{
    /**
     * 积分收入
     * @param $user_id
     * @param $amount
     * @param $type
     * @return int|void
     */
    public function income($user_id, $amount, $type, $title, $target_type = 'order', $target_id=0)
    {
        return 0;
        if (!$user_id || !$amount || !$type) {
            return 0;
        }

        if ($amount < 0) {
            return 0;
        }

        if (!in_array($type, WalletLog::TypeMappings)) {
            return 0;
        }

        if ($type == 'login_give') {
            $walletLog = WalletLog::query()
                ->where('user_id', $user_id)
                ->where('currency', WalletLog::CurrencyMappings['integral'])
                ->where('type', WalletLog::TypeMappings['login_give'])
                ->whereDate('created_at', now())
                ->first();
            if ($walletLog) {
                return 0;
            }
        }

        try {
            DB::beginTransaction();
            $wallet = Wallet::init($user_id);
            $wallet->income = bcadd($wallet->income, $amount, 2);
            $wallet->balance = bcadd($wallet->balance, $amount, 2);
            $wallet->save();

            $walletLog = new WalletLog();
            $walletLog->title = $title;
            $walletLog->user_id = $user_id;
            $walletLog->target_type = $target_type;
            $walletLog->target_id = $target_id;
            $walletLog->currency = WalletLog::CurrencyMappings['integral'];
            $walletLog->type = $type;
            $walletLog->amount = $amount;
            $walletLog->status = WalletLog::StatusMappings['success'];
            $walletLog->action = WalletLog::ActionMappings['income'];
            $walletLog->extend = ['balance'=>$wallet->balance];
            $walletLog->save();
            DB::commit();

            return $amount;
        } catch (\Exception $exception) {
            DB::rollBack();

            return 0;
        }
    }

    /**
     * 积分支出
     * @param $user_id
     * @param $amount
     * @param $type
     * @return int
     */
    public function expend($user_id, $amount, $type, $title, $target_type = 'order', $target_id=0)
    {
        if (!$user_id || !$amount || !$type) {
            return 0;
        }

        if ($amount < 0) {
            return 0;
        }

        if (!in_array($type, WalletLog::TypeMappings)) {
            return 0;
        }

        try {
            DB::beginTransaction();
            $wallet = Wallet::init($user_id);
            $wallet->balance = bcsub($wallet->balance, $amount, 2);
            $wallet->expend = bcadd($wallet->expend, $amount, 2);
            $wallet->save();

            $walletLog = new WalletLog();
            $walletLog->title = $title;
            $walletLog->user_id = $user_id;
            $walletLog->target_type = $target_type;
            $walletLog->target_id = $target_id;
            $walletLog->currency = WalletLog::CurrencyMappings['integral'];
            $walletLog->type = $type;
            $walletLog->amount = $amount;
            $walletLog->status = WalletLog::StatusMappings['success'];
            $walletLog->action = WalletLog::ActionMappings['expend'];
            $walletLog->extend = ['balance'=>$wallet->balance];
            $walletLog->save();
            DB::commit();

            return $amount;
        } catch (\Exception $exception) {
            DB::rollBack();

            return 0;
        }
    }
}
