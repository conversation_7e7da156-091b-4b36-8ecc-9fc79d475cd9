<?php
/**
 * 支付服务类
 * @package App\Services
 *
 * <AUTHOR> <<EMAIL>>
 */

namespace App\Services;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Jobs\SendMessageJob;
use App\Jobs\ShenzaoAuthorize;
use App\Models\CouponCode;
use App\Models\Course;
use App\Models\Order;
use App\Models\RechargeOrder;
use App\Models\UserCourse;
use App\Models\Wallet;
use App\Models\WalletLog;
use Carbon\Carbon;
use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class PaymentService extends BaseServices
{
    /**
     * 支付方式(1-微信，2-支付宝)
     * @var int
     */
    public static $payMethod = 1;

    /**
     * 获取支付所需的url，用于生成二维码
     * <AUTHOR> 2021-10-03T16:26:17+0800
     *
     * @param  string  $orderNo
     * @return mixed
     */
    public function getPayCodeUrl($orderNo)
    {
        $orderInfo = Order::query()->where('order_no', $orderNo)->first();

        if (static::$payMethod == 1 && ! empty($orderInfo->qrcode_str) && $orderInfo->expired_at > time()) {
            return $orderInfo->qrcode_str;
        }

        $update = [
            'pay_method' => static::$payMethod,
            'expired_at' => bcadd(time(), 30 * 60) // 30分钟后失效
        ];
        // 微信支付
        if (static::$payMethod == 1) {
            $paymentParams = [
                'trade_type'   => 'NATIVE',
                'product_id'   => sprintf('product_id_%s', $orderNo),
                'total_fee'    => $orderInfo->order_amount * 100,
                'body'         => sprintf('犀光_%s_订单支付', $orderNo),
                'out_trade_no' => wechat_order_no()
            ];
            $result               = $this->wechatPayUrl($paymentParams);
            $update['qrcode_str'] = $result;
            $update['out_trade_no'] = $paymentParams['out_trade_no'];
            $update['wechat_out_trade_no'] = $paymentParams['out_trade_no'];
        } else {
            // 支付宝
            $outTradeNo    = alipay_order_no();
            $paymentParams = [
                'out_trade_no' => $outTradeNo,
                'subject'      => sprintf('犀光_%s_订单支付', $orderInfo->product_name),
                'total_fee'    => $orderInfo->order_amount,
                'amount'       => $orderInfo->order_amount,
                'body'         => sprintf('犀光_%s_订单支付', $orderNo)
            ];

            $update['out_trade_no'] = $paymentParams['out_trade_no'];
            $update['ali_out_trade_no'] = $paymentParams['out_trade_no'];
            $result = $this->alipayPagePay($paymentParams, $orderNo);
            logger($result);
        }


        // 更新订单信息
        Order::where('order_no', $orderNo)->update($update);

        return $result;
    }

    /**
     * 获取充值支付所需的url，用于生成二维码
     * @param $orderNo
     * @return \Illuminate\Database\Eloquent\HigherOrderBuilderProxy|mixed
     * @throws ApiException
     */
    public function getRechargePayCodeUrl($orderNo)
    {
        $orderInfo = RechargeOrder::query()->where('order_no', $orderNo)->first();

        // 过期时间
        $expiredAt =  strtotime($orderInfo->created_at) + 30 * 60;
        if ($orderInfo->pay_method == 1 && !empty($orderInfo->qrcode_str) && $expiredAt > time()) {
            return $orderInfo->qrcode_str;
        }

        $update = [];
        // 微信支付
        if ($orderInfo->pay_method == 1) {
            $paymentParams = [
                'trade_type' => 'NATIVE',
                'product_id' => sprintf('product_id_%s', $orderNo),
                'total_fee' => $orderInfo->order_amount * 100,
                'body' => sprintf('犀光_%s_光子充值订单支付', $orderNo),
                'out_trade_no' => wechat_order_no()
            ];
            $result = $this->wechatPayUrl($paymentParams);
            $update['qrcode_str'] = $result;
            $update['out_trade_no'] = $paymentParams['out_trade_no'];
            $update['wechat_out_trade_no'] = $paymentParams['out_trade_no'];
        } else {
            // 支付宝
            $outTradeNo = alipay_order_no();
            $paymentParams = [
                'out_trade_no' => $outTradeNo,
                'subject' => '犀光光子充值订单支付',
                'total_fee' => $orderInfo->order_amount,
                'amount' => $orderInfo->order_amount,
                'body' => sprintf('犀光_%s_光子充值订单支付', $orderNo)
            ];

            $update['out_trade_no'] = $paymentParams['out_trade_no'];
            $update['ali_out_trade_no'] = $paymentParams['out_trade_no'];
            $result = $this->alipayPagePay($paymentParams, $orderNo);
            logger($result);
        }

        // 更新订单信息
        RechargeOrder::query()->where('order_no', $orderNo)->update($update);

        return $result;
    }

    /**
     * 获取微信支付url
     *
     * <AUTHOR> 2021-10-03T16:33:58+0800
     *
     * @param  array          $params
     * @throws ApiException
     * @return mixed
     */
    protected function wechatPayUrl($params)
    {
        if ( ! Arr::get($params, 'trade_type')) {
            throw new ApiException('缺少必要参数trade_type！', ResponseCode::SERVER_ERR);
        }

        if ( ! Arr::get($params, 'product_id')) {
            throw new ApiException('缺少必要参数product_id！', ResponseCode::SERVER_ERR);
        }

        if ( ! Arr::get($params, 'total_fee')) {
            throw new ApiException('缺少必要参数total_fee！', ResponseCode::SERVER_ERR);
        }

        if ( ! Arr::get($params, 'body')) {
            throw new ApiException('缺少必要参数body！', ResponseCode::SERVER_ERR);
        }

        if ( ! Arr::get($params, 'out_trade_no')) {
            throw new ApiException('缺少必要参数out_trade_no！', ResponseCode::SERVER_ERR);
        }

        $result = app('wxPayApp')->order->unify($params);

        if (Arr::get($result, 'return_code') == 'FAIL') {
            throw new ApiException(
                Arr::get($result, 'return_msg'),
                ResponseCode::SERVER_ERR
            );
        }

        return Arr::get($result, 'code_url');
    }

    /**
     * 获取支付宝支付CodeUrl
     *
     * <AUTHOR> 2021-10-03T16:33:58+0800
     *
     * @param  array          $data
     * @throws ApiException
     * @return mixed
     */
    public function alipayCodeUrl($data = [])
    {
        $timeExpress = "5m";
        require_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../Libs/alipay/f2fpay/model/builder/AlipayTradePrecreateContentBuilder.php';
        require_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../Libs/alipay/f2fpay/service/AlipayTradeService.php';
        // 创建请求builder，设置请求参数
        $qrPayRequestBuilder = new \AlipayTradePrecreateContentBuilder();
        $qrPayRequestBuilder->setOutTradeNo($data['out_trade_no']);
        $qrPayRequestBuilder->setTotalAmount($data['total_fee']);
        $qrPayRequestBuilder->setTimeExpress($timeExpress);
        $qrPayRequestBuilder->setSubject($data['subject']);
        $qrPayRequestBuilder->setBody($data['body']);
        // 调用qrPay方法获取当面付应答
        $config = config('web.alipay');
        try {
            $qrPay = new \AlipayTradeService($config);
        } catch (Exception $e) {
            return ['code' => 0, 'msg' => '支付系统错误!'];
        }
        $qrPayResult = $qrPay->qrPay($qrPayRequestBuilder);
        //  根据状态值进行业务处理
        switch ($qrPayResult->getTradeStatus()) {
            case "SUCCESS":
                $response = $qrPayResult->getResponse();
                return $response->qr_code;
                break;
            case "FAILED":
                throw new ApiException('支付宝创建订单二维码失败', ResponseCode::SERVER_ERR);
                break;
            case "UNKNOWN":
                throw new ApiException('系统异常，状态未知', ResponseCode::SERVER_ERR);
                break;
            default:
                throw new ApiException('不支持的返回状态，创建订单二维码返回异常', ResponseCode::SERVER_ERR);
                break;
        }
    }

    /**
     * 支付宝网站支付接口
     *
     * <AUTHOR> 2021/10/25T11:20 上午
     *
     * @param  $params
     * @param  $orderNo
     * @throws Exception
     * @return mixed
     */
    public function alipayPagePay($params, $orderNo)
    {
        require_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../Libs/alipay/trade/pagepay/service/AlipayTradeService.php';
        require_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../Libs/alipay/trade/pagepay/buildermodel/AlipayTradePagePayContentBuilder.php';

        $timeExpress = "30m";
        //构造参数
        $payRequestBuilder = new \AlipayTradePagePayContentBuilder();
        $payRequestBuilder->setBody($params['body']);
        $payRequestBuilder->setSubject($params['subject']);
        $payRequestBuilder->setTotalAmount($params['total_fee']);
        $payRequestBuilder->setOutTradeNo($params['out_trade_no']);
        $payRequestBuilder->setTimeExpress($timeExpress);

        $config = config('web.alipay');
        $aop    = new \AlipayTradeService($config);

        $return_url = $config['return_url'] . '?order_no=' . $orderNo;
        $response = $aop->pagePay($payRequestBuilder, $return_url, $config['notify_url']);

        return $response;
    }

    /**
     * 处理支付宝异步通知
     * <AUTHOR> 2021-10-25T14:37:44+0800
     *
     * @param  array        $params
     * @throws Exception
     * @return bool
     */
    public function handleAlipayNotify(array $params)
    {
        logger(['支付宝异步通知参数：', $params]);

        require_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../Libs/alipay/trade/pagepay/service/AlipayTradeService.php';

        $config       = config('web.alipay');
        $alipaySevice = new \AlipayTradeService($config);

        $result = $alipaySevice->check($params);
        if ($result) {
            // 交易结束 或 成功，更新订单状态
            if (in_array(Arr::get($params, 'trade_status'), ['TRADE_FINISHED', 'TRADE_SUCCESS'])) {
                $orderNo = Arr::get($params, 'out_trade_no');

                $order = Order::query()->where('out_trade_no', $orderNo)->first();
                if (!$order) {
                    $order = Order::query()->where('ali_out_trade_no', $orderNo)->first();
                }
                try {
                    // 不想动原代码
                    // 此处获取平台订单号，若以前流程都没找到订单则用此方法查询订单
                    // 解决问题：用户先打开微信支付，再打开支付宝支付，然后去微信支付页面发起付款则会找不到订单
                    // 本来一开始拿平台订单才是正常流程
                    // 不想测试，加try catch 防止造成严重错误
                    if (!$order) {
                        $body = Arr::get($params, 'body');
                        $bodyNo = explode('_', $body)[1] ?? '';
                        $order = Order::query()->where('order_no', $bodyNo)->first();
                    }
                } catch (Exception $exception) {
                }

                $order->status         = Order::StatusMappings['paid'];
                $order->third_order_no = Arr::get($params, 'trade_no');
                $order->pay_time       = now();
                $order->updated_at     = now();
                if ( ! $order->save()) {
                    Log::error('handleAlipayNotify：订单：', [$orderNo, $order->user_id]);
                }

                // 使用优惠码
                if ($order->coupon_code) {
                    CouponCode::use($order->coupon_code, $order->user_id);
                }

                // 光粒赠送处理
                $this->giveIntegral($order);

                if ($order->product_type == Order::ProductTypeMappings['course']) {
                    $userCourse = UserCourse::query()
                        ->where('user_id', $order->user_id)
                        ->where('course_id', $order->product_id)
                        ->where('type', UserCourse::TypeMappings['learn'])
                        ->first();
                    if ( ! $userCourse) {
                        $userCourse            = new UserCourse();
                        $userCourse->user_id   = $order->user_id;
                        $userCourse->course_id = $order->product_id;
                        $userCourse->type      = UserCourse::TypeMappings['learn'];
                        if ( ! $userCourse->save()) {
                            Log::error('toPay：用户课程：', [$orderNo, $order->user_id]);
                        }

                        Course::query()->where('id', $order->product_id)->increment('num');
                    }

                    $shenzaoCourseId = $userCourse->course->sz_course_id;
                }

                $this->smsPublish($order, $shenzaoCourseId ?? '');
            }
            return true;
        } else {
            return false;
        }
    }

    /**
     * 处理微信异步通知
     *
     * @throws Exception
     * @return mixed
     */
    public function handleWechatNotify()
    {
        $response  = app('wxPayApp')->handlePaidNotify(function ($message, $fail) {
            Log::info($message);

            if ($message['return_code'] === 'SUCCESS') {
                // 用户是否支付成功
                if ($message['result_code'] === 'SUCCESS') {
                    $orderNo = Arr::get($message, 'out_trade_no');

                    $order = Order::query()->where('out_trade_no', $orderNo)->first();
                    if (!$order) {
                        $order = Order::query()->where('wechat_out_trade_no', $orderNo)->first();
                    }
                    try {
                        // 不想动原代码
                        // 此处获取平台订单号，若以前流程都没找到订单则用此方法查询订单
                        // 解决问题：用户先打开微信支付，再打开支付宝支付，然后去微信支付页面发起付款则会找不到订单
                        // 本来一开始拿平台订单才是正常流程
                        // 不想测试，加try catch 防止造成严重错误
                        if (!$order) {
                            $body = Arr::get($message, 'body');
                            $bodyNo = explode('_', $body)[1] ?? '';
                            $order = Order::query()->where('order_no', $bodyNo)->first();
                        }
                    } catch (Exception $exception) {
                    }

                    $order->status         = Order::StatusMappings['paid'];
                    $order->third_order_no = Arr::get($message, 'transaction_id');
                    $order->pay_time       = now();
                    $order->updated_at     = now();

                    if ( ! $order->save()) {
                        Log::error('handleWechatNotify：订单：', [$orderNo, $order->user_id]);
                    }

                    // 使用优惠码
                    if ($order->coupon_code) {
                        CouponCode::use($order->coupon_code, $order->user_id);
                    }

                    // 光粒赠送处理
                    $this->giveIntegral($order);

                    if ($order->product_type == Order::ProductTypeMappings['course']) {
                        $userCourse = UserCourse::query()
                            ->where('user_id', $order->user_id)
                            ->where('course_id', $order->product_id)
                            ->where('type', UserCourse::TypeMappings['learn'])
                            ->first();
                        if ( ! $userCourse) {
                            $userCourse            = new UserCourse();
                            $userCourse->user_id   = $order->user_id;
                            $userCourse->course_id = $order->product_id;
                            $userCourse->type      = UserCourse::TypeMappings['learn'];
                            if ( ! $userCourse->save()) {
                                Log::error('toPay：用户课程：', [$orderNo, $order->user_id]);
                            }

                            Course::query()->where('id', $order->product_id)->increment('num');
                        }

                        $shenzaoCourseId = $userCourse->course->sz_course_id;
                    }

                    $this->smsPublish($order, $shenzaoCourseId ?? '');
                } elseif ($message['result_code'] === 'FAIL') {
                    // 支付失败，暂不处理...
                    return false;
                }
            } else {
                Log::error($fail);
                return false;
            }
        });
        return $response->send();
    }

    public function smsPublish($order, $shenzaoCourseId='')
    {
        $cacheKey = 'smsPublish'.$order->order_no;
        if (Cache::has($cacheKey)) {
            return;
        } else {
            Cache::put($cacheKey, 1, 3*86400);
        }

        try {
            $productTypeMap = array_flip(Order::ProductTypeMappings);

            dispatch(new SendMessageJob($order->user->phone, 'publish', $order->product_id,$productTypeMap[$order->product_type], [
                'name' => $order->user->name,
                'code' => $order->order_no,
                'time' => Carbon::parse($order->created_at)->format('Y-m-d H:i:s'),
                'commodity' => $order->product_name,
                'money' => $order->order_amount,
            ]));

            // 课程授权深造绑定账号权限
            if ($order->product_type == Order::ProductTypeMappings['course'] && $shenzaoCourseId) {
                dispatch(new ShenzaoAuthorize($order->user->phone, $shenzaoCourseId));
            }
        } catch (Exception $exception) {
            Log::error('smsPublish', [$exception]);
        }
    }

    /**
     * 光粒赠送
     * @param $order
     */
    public function giveIntegral($order)
    {
        if ($order->give_integral > 0) {
            $wallet = Wallet::init($order->user_id);
            $wallet->income += $order->give_integral;
            $wallet->balance += $order->give_integral;
            $wallet->save();

            $type = WalletLog::TypeMappings['goods'];
            switch ($order->product_type){
                case 1:
                    $type = WalletLog::TypeMappings['course'];
                    break;
                case 2:
                    $type = WalletLog::TypeMappings['plug'];
                    break;
                case 3:
                    $type = WalletLog::TypeMappings['goods'];
                    break;
            }

            $walletLog = new WalletLog();
            $walletLog->title = '购买-'.$order->product_name;
            $walletLog->target_type = 'order';
            $walletLog->target_id = $order->id;
            $walletLog->amount = $order->give_integral;
            $walletLog->user_id = $order->user_id;
            $walletLog->currency = 'integral';
            $walletLog->action = WalletLog::ActionMappings['income'];
            $walletLog->status = WalletLog::StatusMappings['success'];
            $walletLog->type = $type;
            $walletLog->extend = ['balance'=>$wallet->balance];
            $walletLog->save();
        }
    }

    /**
     * 处理光子充值支付宝异步通知
     * @param array $params
     * @return bool
     * @throws Exception
     */
    public function handleRechargeAlipayNotify(array $params)
    {
        logger(['光子充值支付宝异步通知参数：', $params]);

        require_once dirname(__FILE__) . DIRECTORY_SEPARATOR . '../Libs/alipay/trade/pagepay/service/AlipayTradeService.php';

        $config = config('web.alipay');
        $alipaySevice = new \AlipayTradeService($config);

        $result = $alipaySevice->check($params);
        if ($result) {
            // 交易结束 或 成功，更新订单状态
            if (in_array(Arr::get($params, 'trade_status'), ['TRADE_FINISHED', 'TRADE_SUCCESS'])) {
                $orderNo = Arr::get($params, 'out_trade_no');

                $order = RechargeOrder::query()->where('out_trade_no', $orderNo)->first();
                if (!$order) {
                    $order = RechargeOrder::query()->where('ali_out_trade_no', $orderNo)->first();
                }
                $oldStatus = $order->status;
                try {
                    // 不想动原代码
                    // 此处获取平台订单号，若以前流程都没找到订单则用此方法查询订单
                    // 解决问题：用户先打开微信支付，再打开支付宝支付，然后去微信支付页面发起付款则会找不到订单
                    // 本来一开始拿平台订单才是正常流程
                    // 不想测试，加try catch 防止造成严重错误
                    if (!$order) {
                        $body = Arr::get($params, 'body');
                        $bodyNo = explode('_', $body)[1] ?? '';
                        $order = RechargeOrder::query()->where('order_no', $bodyNo)->first();
                    }
                } catch (Exception $exception) {
                }

                $order->status = RechargeOrder::StatusMappings['paid'];
                $order->third_order_no = Arr::get($params, 'trade_no');
                $order->pay_time = now();
                $order->updated_at = now();
                if (!$order->save()) {
                    Log::error('handleRechargeAlipayNotify：订单：', [$orderNo, $order->user_id]);
                }

                if ($oldStatus != $order->status) {
                    // 充值成功，发放光子
                    $this->rechargeOrderSuccessGrant($order);
                }
            }
            return true;
        } else {
            return false;
        }
    }

    /**
     * 处理光子充值微信异步通知
     *
     * @return mixed
     * @throws Exception
     */
    public function handleRechargeWechatNotify()
    {
        $response = app('wxPayApp')->handlePaidNotify(function ($message, $fail) {
            Log::info('光子充值微信异步通知：' . json_encode($message, JSON_UNESCAPED_UNICODE));

            if ($message['return_code'] === 'SUCCESS') {
                // 用户是否支付成功
                if ($message['result_code'] === 'SUCCESS') {
                    // 订单号
                    $orderNo = Arr::get($message, 'out_trade_no');

                    $order = RechargeOrder::query()->where('out_trade_no', $orderNo)->first();
                    if (!$order) {
                        $order = RechargeOrder::query()->where('wechat_out_trade_no', $orderNo)->first();
                    }
                    $oldStatus = $order->status;
                    try {
                        // 不想动原代码
                        // 此处获取平台订单号，若以前流程都没找到订单则用此方法查询订单
                        // 解决问题：用户先打开微信支付，再打开支付宝支付，然后去微信支付页面发起付款则会找不到订单
                        // 本来一开始拿平台订单才是正常流程
                        // 不想测试，加try catch 防止造成严重错误
                        if (!$order) {
                            $body = Arr::get($message, 'body');
                            $bodyNo = explode('_', $body)[1] ?? '';
                            $order = RechargeOrder::query()->where('order_no', $bodyNo)->first();
                        }
                    } catch (Exception $exception) {
                    }

                    $order->status = RechargeOrder::StatusMappings['paid'];
                    $order->third_order_no = Arr::get($message, 'transaction_id');
                    $order->pay_time = now();
                    $order->updated_at = now();
                    if (!$order->save()) {
                        Log::error('handleRechargeWechatNotify：订单：', [$orderNo, $order->user_id]);
                    }

                    if ($oldStatus != $order->status) {
                        // 充值成功，发放光子
                        $this->rechargeOrderSuccessGrant($order);
                    }
                    return true;
                } elseif ($message['result_code'] === 'FAIL') {
                    // 支付失败，暂不处理...
                    return false;
                }
            } else {
                Log::error($fail);
                return false;
            }
        });

        return $response->send();
    }

    /**
     * 光子充值成功发放处理
     * @param $order
     * @return bool
     */
    public function rechargeOrderSuccessGrant($order)
    {
        try {
            $wallet = Wallet::init($order->user_id);
            $currBalance = bcadd($wallet->balance, $order->num, 2);
            $wallet->update([
                'income' => DB::raw('income + ' . $order->num),
                'balance' => DB::raw('balance + ' . $order->num),
            ]);

            // 充值流水
            WalletLog::query()->create([
                'title' => '光子充值',
                'target_type' => WalletLog::TargetTypeMappings['recharge_order'],
                'target_id' => $order->id,
                'amount' => $order->num,
                'user_id' => $order->user_id,
                'currency' => 'integral',
                'action' => WalletLog::ActionMappings['income'],
                'status' => WalletLog::StatusMappings['success'],
                'type' => WalletLog::TypeMappings['goods'],
                'extend' => ['balance' => $currBalance],
            ]);
        } catch (\Exception $exception) {
            logger()->error($exception);
            return false;
        }

        return true;
    }
}
