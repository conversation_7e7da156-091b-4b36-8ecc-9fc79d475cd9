<?php

namespace App\Services;

use App\Models\User;
use App\Models\Wallet;
use App\Models\WalletLog;
use App\Models\RechargeOrder;
use Illuminate\Support\Facades\DB;
use App\Constants\ResponseCode;
use App\Exceptions\ApiException;

class RechargeService
{
    /**
     * 管理员充值光子
     * 
     * @param string $phone 用户手机号
     * @param float $amount 充值金额
     * @param int $adminId 管理员ID
     * @param string $remark 备注
     * @return array
     * @throws ApiException
     */
    public static function adminRecharge($phone, $amount, $adminId, $remark = '')
    {
        // 验证用户是否存在
        $user = User::query()->where('phone', $phone)->where('is_delete', 0)->first();
        if (!$user) {
            throw new ApiException('用户不存在', ResponseCode::NOT_FOUND);
        }

        // 验证充值金额
        if ($amount <= 0) {
            throw new ApiException('充值金额必须大于0', ResponseCode::PARAM_ERR);
        }

        try {
            DB::beginTransaction();

            // 创建充值订单记录
            $rechargeOrder = RechargeOrder::query()->create([
                'order_no' => self::generateRechargeOrderNo(),
                'user_id' => $user->id,
                'num' => $amount, // 光子数量等于金额
                'status' => RechargeOrder::StatusMappings['paid'], // 直接设为已支付
                'pay_method' => 0, // 管理员充值
                'order_amount' => $amount,
                'total_amount' => $amount,
                'pay_time' => now(),
                'remark' => $remark ?: '管理员充值',
            ]);

            // 初始化或获取用户钱包
            $wallet = Wallet::init($user->id);
            
            // 更新钱包余额和累计收入
            $oldBalance = $wallet->balance;
            $newBalance = bcadd($oldBalance, $amount, 2);
            
            $wallet->update([
                'balance' => $newBalance,
                'income' => bcadd($wallet->income, $amount, 2),
            ]);

            // 创建钱包流水记录
            WalletLog::query()->create([
                'title' => '管理员充值',
                'target_type' => WalletLog::TargetTypeMappings['recharge_order'],
                'target_id' => $rechargeOrder->id,
                'amount' => $amount,
                'user_id' => $user->id,
                'currency' => WalletLog::CurrencyMappings['integral'],
                'action' => WalletLog::ActionMappings['income'],
                'status' => WalletLog::StatusMappings['success'],
                'type' => WalletLog::TypeMappings['admin_recharge'],
                'extend' => [
                    'balance' => $newBalance,
                    'admin_id' => $adminId,
                    'remark' => $remark
                ],
            ]);

            DB::commit();

            return [
                'recharge_order_id' => $rechargeOrder->id,
                'order_no' => $rechargeOrder->order_no,
                'user_id' => $user->id,
                'user_name' => $user->name,
                'user_phone' => $user->phone,
                'amount' => $amount,
                'old_balance' => $oldBalance,
                'new_balance' => $newBalance,
            ];

        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApiException('充值失败：' . $e->getMessage(), ResponseCode::SERVER_ERR);
        }
    }

    /**
     * 生成充值订单号
     * 
     * @return string
     */
    private static function generateRechargeOrderNo()
    {
        return 'RC' . date('YmdHis') . mt_rand(1000, 9999);
    }

    /**
     * 获取充值记录列表
     * 
     * @param array $params 查询参数
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    public static function getRechargeList($params = [])
    {
        $query = RechargeOrder::query()
            ->with(['user:id,name,phone'])
            ->where('pay_method', 0) // 只查询管理员充值记录
            ->where('status', RechargeOrder::StatusMappings['paid']);

        // 按手机号筛选
        if (!empty($params['phone'])) {
            $query->whereHas('user', function ($q) use ($params) {
                $q->where('phone', $params['phone']);
            });
        }

        // 按订单号筛选
        if (!empty($params['order_no'])) {
            $query->where('order_no', $params['order_no']);
        }

        // 按时间范围筛选
        if (!empty($params['start_time'])) {
            $query->where('created_at', '>=', $params['start_time']);
        }
        if (!empty($params['end_time'])) {
            $query->where('created_at', '<=', $params['end_time']);
        }

        return $query->orderByDesc('id')->paginate($params['limit'] ?? 20);
    }

    /**
     * 获取充值统计信息
     * 
     * @param array $params 查询参数
     * @return array
     */
    public static function getRechargeStats($params = [])
    {
        $query = RechargeOrder::query()
            ->where('pay_method', 0) // 只统计管理员充值
            ->where('status', RechargeOrder::StatusMappings['paid']);

        // 按时间范围筛选
        if (!empty($params['start_time'])) {
            $query->where('created_at', '>=', $params['start_time']);
        }
        if (!empty($params['end_time'])) {
            $query->where('created_at', '<=', $params['end_time']);
        }

        $totalAmount = $query->sum('order_amount');
        $totalCount = $query->count();

        return [
            'total_amount' => $totalAmount,
            'total_count' => $totalCount,
            'average_amount' => $totalCount > 0 ? round($totalAmount / $totalCount, 2) : 0,
        ];
    }
}
