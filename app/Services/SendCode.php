<?php
namespace App\Services;

use AlibabaCloud\Client\AlibabaCloud;
use AlibabaCloud\Client\Exception\ClientException;
use AlibabaCloud\Client\Exception\ServerException;
use Illuminate\Support\Facades\Log;

class SendCode
{
    /**
     * @var string[]
     */
    protected $config;

    public function __construct()
    {
        $this->config = [
            'accessKeyId' => '',
            'accessSecret' => '',
            'SignName' => '',
            'TemplateCode' => '',
            'regionId' => ''
        ];
    }

    /**
     * 发送短信验证码
     * @param $phone
     * @param $code
     * @return array|void
     * @throws ClientException
     */
    public function sendCode($phone,$code)
    {
        AlibabaCloud::accessKeyClient($this->config['accessKeyId'], $this->config['accessSecret'])
            ->regionId($this->config['regionId'])
            ->asDefaultClient();

        try {
            $result = AlibabaCloud::rpc()
                ->product('Dysmsapi')
                ->version('2017-05-25')
                ->action('SendSms')
                ->method('POST')
                ->options([
                    'query' => [
                        'PhoneNumbers' => $phone,
                        'SignName' => $this->config['SignName'],
                        'TemplateCode' => $this->config['TemplateCode'],
                        'TemplateParam' => json_encode([
                            'code' => $code
                        ])
                    ],
                ])
                ->request();
            return $result->toArray();
        } catch (ClientException $e) {
            Log::error($e->getErrorMessage());
        } catch (ServerException $e) {
            Log::error($e->getErrorMessage());
        }
    }
}
