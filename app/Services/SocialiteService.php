<?php
namespace App\Services;

use App\Constants\ResponseCode;
use App\Exceptions\ApiException;
use App\Models\Socialite;
use App\Models\User;
use Illuminate\Support\Facades\Log;

class SocialiteService
{
    /**
     * 授权信息
     * @var
     */
    protected $oauthUser;

    /**
     * 授权服务类型
     * @var
     */
    protected $service;

    /**
     * @param $oauthUser
     * @param $service
     */
    public function __construct($oauthUser, $service)
    {
        $this->oauthUser = $oauthUser;

        $this->service = $service;
    }

    /**
     * 获取授权authid
     * @return mixed|string
     * @throws ApiException
     */
    public function getAuthid()
    {
        switch ($this->service) {
            case Socialite::TypeMappings['weixin']:
            case Socialite::TypeMappings['weixinweb']:
                $authid = $this->oauthUser['openid'];
                break;
            case Socialite::TypeMappings['github']:
                $authid = $this->oauthUser['node_id'];
                break;
            case Socialite::TypeMappings['qq']:
                $authid = $this->oauthUser->id;
                break;
            default:
                $authid = '';break;
        }

        if (!isset($authid) || !$authid) {
            throw new ApiException('授权信息异常', ResponseCode::PARAM_ERR);
        }

        return $authid;
    }

    /**
     * 获取绑定信息
     * @return \Illuminate\Database\Eloquent\Builder|\Illuminate\Database\Eloquent\Model|object|null
     * @throws ApiException
     */
    public function bindingInfo()
    {
        return Socialite::query()
            ->where('type', $this->service)
            ->where("authid", $this->getAuthid())
            ->first();
    }

    /**
     * 绑定
     * @param $userId
     * @return array|string[]
     * @throws ApiException
     */
    public function binding($userId)
    {
        $user = User::query()->find($userId);
        if (!$user) {
            return ['msg' => '用户信息异常', 'code' => '4404'];
        }

        $bindingInfo = $this->bindingInfo();
        Log::info('binding:', [$this->getAuthid(), $user->id, $userId]);
        if (!$bindingInfo) {
            $socialiteModel = new Socialite();
            $socialiteModel->user_id = $user->id;
            $socialiteModel->authid = $this->getAuthid();
            $socialiteModel->type = $this->service;
            $socialiteModel->auth_user = $this->oauthUser;
            if (!$socialiteModel->save()) {
                return ['msg' => '绑定失败', 'code' => '5000'];
            }

            if ($this->oauthUser) {
                $user->avatar = $user->avatar ?: $this->oauthUser->avatar;
                $user->name = $user->name ?: $this->oauthUser->nickname;
                $user->save();
            }

            return [
                'msg' => '绑定成功',
                'code' => 200
            ];
        }

        if ($bindingInfo->user_id == $user->id) {
            return ['msg' => '授权账号已绑定，无需重复绑定', 'code' => '4403'];
        }

        if ($bindingInfo->authid == $this->getAuthid()) {
            return ['msg' => '授权账号仅能绑定一次', 'code' => '4403'];
        }

        return ['msg' => '授权账号已绑定站内其它账号，请先取消绑定', 'code' => '4403'];
    }
}
