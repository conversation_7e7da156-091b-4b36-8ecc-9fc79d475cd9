<?php
namespace App\Services;

use App\Models\Course;
use App\Models\Goods;
use App\Models\Order;
use App\Models\Plug;
use Illuminate\Support\Facades\Log;

class OrderService extends BaseServices
{
    /**
     * 是否积分抵扣
     * @param $order_id
     * @return int|mixed
     */
    public function isIntegral($order_id)
    {
        $order = Order::query()->find($order_id);
        if (!$order) {
            return 0;
        }

        switch ($order->product_type) {
            case Order::ProductTypeMappings['course']:
                return Course::query()->where('id', $order->product_id)->value('is_integral') ?: 0;
                break;
            case Order::ProductTypeMappings['plug']:
                return Plug::query()->where('id', $order->product_id)->value('is_integral') ?: 0;
                break;
            case Order::ProductTypeMappings['goods']:
                return Goods::query()->where('id', $order->product_id)->value('is_integral') ?: 0;
                break;
            default:
                return 0;
                break;
        }
    }

    /**
     * 是否积分抵扣
     * @param $order_id
     * @return int|mixed
     */
    public function integralInfo($order_id)
    {
        $order = Order::query()->find($order_id);
        if (!$order) {
            return null;
        }

        switch ($order->product_type) {
            case Order::ProductTypeMappings['course']:
                return Course::query()->where('id', $order->product_id)->first();
                break;
            case Order::ProductTypeMappings['plug']:
                return Plug::query()->where('id', $order->product_id)->first();
                break;
            case Order::ProductTypeMappings['goods']:
                return Goods::query()->where('id', $order->product_id)->first();
                break;
            default:
                return null;
                break;
        }
    }
}
