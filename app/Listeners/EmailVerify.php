<?php

namespace App\Listeners;

use App\Events\SetEmail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class EmailVerify implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * @param SetEmail $event
     */
    public function handle(SetEmail $event)
    {
        $user = $event->getUser();
        $email = $event->getEmail();

        $_token = $user->api_token;

        $callbackLink = sprintf(
             '%s/#/email-verify?email=%s&_token=%s&_timestamp=%s&verify_code=%s',
            env('APP_URL'),
            $email,
            $_token,
            time(),
            md5($email . 'xiguang'. time())
        );

        Log::info('邮箱验证：', [$email, $callbackLink]);

        $content = '点击链接进行邮箱验证：'.$callbackLink;
        $subject = '邮箱验证';
        Mail::raw($content, function ($msg) use ($email, $subject) {
            $msg->to($email);
            $msg->subject($subject);
        });
    }
}
