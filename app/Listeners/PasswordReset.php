<?php

namespace App\Listeners;

use App\Events\Forget;
use App\Events\SetEmail;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class PasswordReset implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct()
    {
        //
    }

    /**
     * @param SetEmail $event
     */
    public function handle(Forget $event)
    {
        $email = $event->getEmail();

        // 直接跳转到前端重置密码页面，填写安全问题进行重置
        $time = strtotime(date('Y-m-d H'));
        $callbackLink = sprintf(
             '%s/#/reset-password?email=%s&_timestamp=%s&verify_code=%s',
            env('APP_URL'),
            $email,
            time(),
            md5($email . 'xiguang'. $time)
        );
        Log::info('密码重置：', [$email, $callbackLink]);

        $content = '点击链接进行密码重置操作：'.$callbackLink;
        $subject = '密码重置';
        Mail::raw($content, function ($msg) use ($email, $subject) {
            $msg->to($email);
            $msg->subject($subject);
        });
    }
}
