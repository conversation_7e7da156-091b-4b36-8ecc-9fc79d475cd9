<?php

namespace App\Jobs;

use App\Models\VerificationCode;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Mail;

class SendSmsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $account;

    private $scene;

    private $sign;

    /**
     * SendSmsJob constructor.
     * @param string $account
     * @param string $scene
     */
    public function __construct(string $account, string $scene)
    {
        $this->account = $account;
        $this->scene = $scene;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        Log::info('send_code');
        try {
            $model = new VerificationCode();
            $model->used = 0;
            $model->code = mt_rand(100000, 999999);
            $model->type = 'sms';
            $model->scene = $this->scene;
            $model->account = $this->account;
            $model->message = 'ok';
            $model->status = 1;
            if (!$model->save()) {
                throw new \Exception('发送失败');
            }

            Log::info("================");
            Log::info($this->account);
            Log::info("================");
            if (is_phone($this->account)){
                $res = app('easysms')->send($this->account, [
                    'template' => 'SMS_487350025',
                    'data' => ['code' => $model->code],
                ]);
                Log::info("================");
                Log::info($res);
                Log::info($model->code);
                Log::info("================");

            }

            if (is_email($this->account)) {
                $content = sprintf('【radirhino】您的验证码为%s，请于10分钟内正确输入，如非本人操作，请忽略本邮件！', $model->code);
                Mail::raw($content, function ($msg) {
                    $msg->to($this->account);
                    $msg->subject('radirhino注册验证码');
                });
            }
        } catch (\Throwable $e) {
            Log::error($e->getMessage());
        } catch (\Exception $e){
            Log::error($e->getMessage());
        }
    }
}
