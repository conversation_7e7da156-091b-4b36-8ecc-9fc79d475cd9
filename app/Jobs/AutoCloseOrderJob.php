<?php

namespace App\Jobs;

use App\Models\Goods;
use App\Models\Order;
use App\Models\Wallet;
use App\Models\WalletLog;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AutoCloseOrderJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $order;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(Order $order)
    {
        $this->order = $order;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $order = $this->order;
        if ($order->status !== Order::StatusMappings['wait']) {
            return;
        }
        try {
            DB::beginTransaction();
            $order->status = Order::StatusMappings['close'];
            $order->remark = '系统自动关闭订单';
            $order->close_time = now();
            $order->save();

            if ($order->product_type == Order::ProductTypeMappings['goods']) {
                Goods::query()
                    ->where('id', $order->product_id)
                    ->update([
                        'sales_num' => DB::raw('sales_num - ' . $order->num),
                        'stock' => DB::raw('stock + ' . $order->num),
                    ]);
            }

            if ($order->deduct_integral > 0) {
                $wallet = Wallet::init($order->user_id);
                $wallet->balance += $order->deduct_integral;
                $expend = $wallet->expend - $order->deduct_integral;
                $wallet->expend = $expend > 0 ? $expend : 0;
                $wallet->save();

                $type = WalletLog::TypeMappings['goods'];
                switch ($order->product_type){
                    case 1:
                        $type = WalletLog::TypeMappings['course'];
                        break;
                    case 2:
                        $type = WalletLog::TypeMappings['plug'];
                        break;
                    case 3:
                        $type = WalletLog::TypeMappings['goods'];
                        break;
                }

                $walletLog = new WalletLog();
                $walletLog->title = '购买-'.$order->product_name.'订单关闭退还';
                $walletLog->target_type = 'order';
                $walletLog->target_id = $order->id;
                $walletLog->amount = $order->deduct_integral;
                $walletLog->user_id = $order->user_id;
                $walletLog->currency = 'integral';
                $walletLog->action = WalletLog::ActionMappings['income'];
                $walletLog->status = WalletLog::StatusMappings['success'];
                $walletLog->type = $type;
                $walletLog->extend = ['balance'=>$wallet->balance];
                $walletLog->save();
            }
            DB::commit();
        } catch (\Throwable $e) {
            DB::rollBack();
        }
    }
}
