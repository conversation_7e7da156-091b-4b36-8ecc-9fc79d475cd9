<?php

namespace App\Jobs;

use App\Services\ShenzaoService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ShenzaoAuthorize implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $account;

    private $courseId;

    private $type;

    /**
     * @param $account string 账号
     * @param $courseId string 深造课程ID
     * @param $type bool 类型：0- 取消授权，1-新增授权
     */
    public function __construct($account, $courseId, $type = 1)
    {
        $this->account = $account;
        $this->courseId = $courseId;
        $this->type = $type;
    }

    /**
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    public function handle()
    {
        if (!$this->account || !$this->courseId) {
            return;
        }

//        $courseInfo = (new ShenzaoService())->findByLicenseCode($this->courseId);{}
//        if (!$courseInfo || !$courseInfo->courseId) {
//            Log::info('ShenzaoAuthorize not found '.$this->courseId);
//            return;
//        }

        $res = (new ShenzaoService())->authorizeUserCourseInfo($this->account, $this->type, $this->courseId);
        if (!$res) {
            // 提示推送
        }
    }
}
