<?php

namespace App\Jobs;

use App\Models\Message;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Overtrue\EasySms\EasySms;

class SendM<PERSON>ageJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $account;

    private $scene;
    private $target_id;
    private $target_type;
    private $data;
    private $config;

    /**
     * @param $account
     * @param $scene
     * @param $target_id
     * @param $target_type
     * @param array $data
     */
    public function __construct($account, $scene, $target_id, $target_type, $data = [])
    {
        $this->account = $account;
        $this->scene = $scene;
        $this->target_id = $target_id;
        $this->target_type = $target_type;
        $this->data = $data;

        $config = config('easysms');
        $config['gateways']['aliyun']['sign_name'] = '犀光参数化平台';
        $this->config = $config;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        try {
            $model = new Message();
            $model->scene = $this->scene;
            $model->account = $this->account;
            $model->message = 'ok';
            $model->status = 1;
            $model->target_id = $this->target_id;
            $model->target_type = $this->target_type;
            if (!$model->save()) {
                throw new \Exception('发送失败');
            }

            if (is_phone($this->account)) {
                if ($this->scene == 'publish') {
                    $template = 'SMS_272500364';
                } elseif ($this->scene == 'logistics') {
                    $template = 'SMS_272515535';
                } elseif ($this->scene == 'popularize') {
                    $template = 'SMS_272620327';
                }
                (new EasySms($this->config))->send($this->account, [
                    'template' => $template ?? '',
                    'data' => $this->data,
                ]);
            }
        } catch (\Throwable $exception) {
            if ($exception->getExceptions()) {
                Log::error('SendMessageJob', $exception->getExceptions());
                if (isset($exception->getExceptions()['aliyun'])) {
                    $message = $exception->getExceptions()['aliyun']->raw['Message'] ?? '';
                }
            } else {
                Log::error('SendMessageJob', [$exception]);
            }
            if (isset($model) && $model) {
                $model->message = $message ?? '';
                $model->status = 0;
                $model->save();
            }
        }
    }
}
