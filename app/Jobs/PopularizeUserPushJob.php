<?php

namespace App\Jobs;

use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class PopularizeUserPushJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    private $wheres;
    private $target_id;
    private $target_type;
    private $data;

    /**
     */
    public function __construct($target_id, $target_type, $data = [])
    {
        $this->target_id = $target_id;
        $this->target_type = $target_type;
        $this->data = $data;
    }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        User::query()
            ->where('is_delete', 0)
            ->where('state', 0)
            ->whereNotNull('phone')
            ->chunk(100, function ($users) {
                foreach ($users as $user) {
                    $data = $this->data;
                    $data['name'] = $user->name ?: $user->phone;
                    dispatch(new SendMessageJob($user->phone, 'popularize', $this->target_id, $this->target_type, $data));
                }
            });
    }
}
