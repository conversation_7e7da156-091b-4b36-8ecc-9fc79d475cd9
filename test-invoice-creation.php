<?php

// 测试发票信息创建的数据结构

// 个人抬头测试数据
$personalInvoiceData = [
    'invoice_type' => 1,
    'invoice_content' => '技术咨询服务',
    'header_type' => 1,
    'header_name' => '张三',
    'is_default' => 1
];

// 单位抬头测试数据
$companyInvoiceData = [
    'invoice_type' => 2,
    'invoice_content' => '技术咨询服务',
    'header_type' => 2,
    'header_name' => '北京科技有限公司',
    'tax_number' => '91110000********9X',
    'registered_address' => '北京市朝阳区xxx街道xxx号',
    'registered_phone' => '010-********',
    'bank_name' => '中国银行北京分行',
    'bank_account' => '********90********9',
    'is_default' => 0
];

echo "个人抬头测试数据:\n";
print_r($personalInvoiceData);

echo "\n单位抬头测试数据:\n";
print_r($companyInvoiceData);

echo "\n修复说明:\n";
echo "1. 数据库字段已设置为 nullable() 和 default('')\n";
echo "2. 控制器中为所有字段设置了默认值\n";
echo "3. 验证规则根据抬头类型动态调整\n";
echo "4. 个人抬头时，单位相关字段为可选\n";
echo "5. 单位抬头时，单位相关字段为必填\n";
