<?php

/*
 * This file is part of the overtrue/laravel-ueditor.
 *
 * (c) overtrue <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

return [
    UPLOAD_ERR_INI_SIZE => '文件大小超出php.ini中MAX_FILE_SIZE限制',
    UPLOAD_ERR_FORM_SIZE => '文件大小超出表单中MAX_FILE_SIZE限制',
    UPLOAD_ERR_PARTIAL => '文件未被完整上傳',
    UPLOAD_ERR_NO_FILE => '沒有文件被上傳',
    UPLOAD_ERR_NO_TMP_DIR => '找不到臨時文件夹',
    UPLOAD_ERR_CANT_WRITE => '文件写入失败',
    'ERROR_TMP_FILE' => '臨時文件錯誤',
    'ERROR_TMP_FILE_NOT_FOUND' => '找不到臨時文件',
    'ERROR_SIZE_EXCEED' => '文件大小超出網站限制',
    'ERROR_TYPE_NOT_ALLOWED' => '文件類型不允許',
    'ERROR_CREATE_DIR' => '目錄創建失敗',
    'ERROR_DIR_NOT_WRITEABLE' => '目錄沒有寫許可權',
    'ERROR_FILE_MOVE' => '文件保存時出錯',
    'ERROR_FILE_NOT_FOUND' => '找不到上傳文件',
    'ERROR_WRITE_CONTENT' => '寫入文件內容錯誤',
    'ERROR_UNKNOWN' => '未知錯誤',
    'ERROR_DEAD_LINK' => '鏈接不可用',
    'ERROR_HTTP_LINK' => '鏈接不是http鏈接',
    'ERROR_HTTP_CONTENTTYPE' => '鏈接contentType不正確',
    'ERROR_UNKNOWN_MODE' => '文件上傳模式錯誤',
];
