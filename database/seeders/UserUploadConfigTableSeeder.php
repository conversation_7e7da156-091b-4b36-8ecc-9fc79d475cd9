<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;

class UserUploadConfigTableSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {

        $insert=[
            'key' => 'desc',
            'value' => '<p style=\"text-align: center;\">欢迎您加入犀光大家庭，在您上传文件时，请仔细阅读下方并根据给予样式进行\"上传说明&rdquo;，<br data-v-26d7097f=\"\" />视频，文件分享。<br data-v-26d7097f=\"\" />视频文件可下载下方压缩软件。</p>',
            'created_at' => date('Y-m-d H:i:s'),
        ];
        \Illuminate\Support\Facades\DB::table('users_upload_configs')->insert($insert);
        $insert=[
            'key' => 'image',
            'value' => '',
            'created_at' => date('Y-m-d H:i:s'),
        ];
        \Illuminate\Support\Facades\DB::table('users_upload_configs')->insert($insert);

        $insert=[
            'key' => 'title',
            'value' => '必剪桌面端',
            'created_at' => date('Y-m-d H:i:s'),
        ];
        \Illuminate\Support\Facades\DB::table('users_upload_configs')->insert($insert);

        $insert=[
            'key' => 'url',
            'value' => '',
            'created_at' => date('Y-m-d H:i:s'),
        ];
        \Illuminate\Support\Facades\DB::table('users_upload_configs')->insert($insert);

        $insert=[
            'key' => 'itemdesc',
            'value' => '一键字幕，海量素材，全能剪辑，支持一键投稿',
            'created_at' => date('Y-m-d H:i:s'),
        ];
        \Illuminate\Support\Facades\DB::table('users_upload_configs')->insert($insert);
        $insert = [
            'name'=>'投稿审核',
            'alias'=>'review',
            'icon'=>'list',
            'url'=>null,
            'sort'=>0,
            'type'=>0,
            'pid'=>0,
            'show'=>1,
        ];

        $id = \Illuminate\Support\Facades\DB::table('as_authorities')->insertGetId($insert);

        $insert=[
            [
                'name'=>'视频投稿',
                'alias'=>'videoreview',
                'icon'=>null,
                'url'=>'videoreview',
                'sort'=>0,
                'type'=>1,
                'pid'=>$id,
                'show'=>1,
            ],
            [
                'name'=>'插件投稿',
                'alias'=>'chajianreview',
                'icon'=>null,
                'url'=>'chajianreview',
                'sort'=>0,
                'type'=>1,
                'pid'=>$id,
                'show'=>1,
            ],
            [
                'name'=>'素材投稿',
                'alias'=>'sucaireview',
                'icon'=>null,
                'url'=>'sucaireview',
                'sort'=>0,
                'type'=>1,
                'pid'=>$id,
                'show'=>1,
            ],
        ];
        $id = \Illuminate\Support\Facades\DB::table('as_authorities')->insert($insert);
        $insert = [
            'name'=>'上传管理',
            'alias'=>'myupload',
            'icon'=>'list',
            'url'=>'myupload',
            'sort'=>10,
            'type'=>1,
            'pid'=>41,
            'show'=>1,
        ];
        $id = \Illuminate\Support\Facades\DB::table('as_authorities')->insert($insert);
    }
}
