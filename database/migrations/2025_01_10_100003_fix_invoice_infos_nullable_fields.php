<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class FixInvoiceInfosNullableFields extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('invoice_infos', function (Blueprint $table) {
            // 修改字段为可空，并设置默认值
            $table->string('tax_number', 50)->default('')->nullable()->change();
            $table->string('registered_address', 200)->default('')->nullable()->change();
            $table->string('registered_phone', 20)->default('')->nullable()->change();
            $table->string('bank_name', 100)->default('')->nullable()->change();
            $table->string('bank_account', 50)->default('')->nullable()->change();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('invoice_infos', function (Blueprint $table) {
            // 回滚时恢复为不可空
            $table->string('tax_number', 50)->default('')->nullable(false)->change();
            $table->string('registered_address', 200)->default('')->nullable(false)->change();
            $table->string('registered_phone', 20)->default('')->nullable(false)->change();
            $table->string('bank_name', 100)->default('')->nullable(false)->change();
            $table->string('bank_account', 50)->default('')->nullable(false)->change();
        });
    }
}
