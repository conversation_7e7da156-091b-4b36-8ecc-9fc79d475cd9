<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTagTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('tags', function (Blueprint $table) {
            $table->id();
            $table->string('tag')->default('')->comment('标签');
            $table->timestamps();
        });


        Schema::create('tag_lists', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('tag')->default(0)->comment('标签ID');
            $table->unsignedInteger('uid')->default(0)->comment('稿件id');
            $table->unsignedInteger('aid')->default(0)->comment('正式ID');
            $table->string('type')->default('')->comment('类型');
            $table->timestamps();
        });



    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('tags');
        Schema::dropIfExists('tag_lists');
    }
}
