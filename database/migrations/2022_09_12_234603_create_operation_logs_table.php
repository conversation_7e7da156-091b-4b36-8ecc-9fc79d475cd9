<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateOperationLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('operation_logs', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedInteger('admin_id')->nullable()->default(0)->comment('用户ID');
            $table->string('admin_name')->nullable()->default('')->comment('用户名称');
            $table->string('ip')->nullable()->default('')->comment('IP');
            $table->string('module')->nullable()->default('')->comment('模块');
            $table->string('class')->nullable()->default('')->comment('类名');
            $table->string('request_method')->nullable()->default('')->comment('请求方式');
            $table->json('param')->nullable()->comment('请求参数');
            $table->integer('code')->default(0)->comment('响应结果状态码');
            $table->tinyText('message')->nullable()->comment('响应结果消息');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('operation_logs');
    }
}
