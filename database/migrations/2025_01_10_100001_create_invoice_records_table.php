<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInvoiceRecordsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('invoice_records', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id')->comment('用户ID');
            $table->unsignedInteger('recharge_order_id')->comment('充值订单ID');
            $table->unsignedInteger('invoice_info_id')->comment('发票信息ID');
            $table->string('invoice_no', 50)->default('')->comment('发票号码');
            $table->decimal('invoice_amount', 12, 2)->default(0.00)->comment('开票金额');
            $table->tinyInteger('status')->default(1)->comment('状态：1-待处理，2-已上传，3-重开中');
            $table->string('invoice_file', 255)->default('')->comment('发票文件路径');
            $table->timestamp('apply_time')->nullable()->comment('申请时间');
            $table->timestamp('upload_time')->nullable()->comment('上传时间');
            $table->text('remark')->nullable()->comment('备注');
            $table->timestamps();
            
            $table->index('user_id');
            $table->index('recharge_order_id');
            $table->index('status');
        });
        
        \DB::statement("ALTER TABLE `invoice_records` comment'开票记录表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invoice_records');
    }
}
