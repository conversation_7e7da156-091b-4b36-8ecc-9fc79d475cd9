<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserCoursesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_courses', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->comment('用户id');
            $table->unsignedInteger('course_id')->default(0)->comment('课程id');
//            $table->unsignedSmallInteger('progress')->default(0)->comment('观看课程内视频数量');
            $table->char('type', 20)->default('learn')->comment('分类：学习-learn/收藏-collect');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `user_courses` comment'用户课程'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_courses');
    }
}
