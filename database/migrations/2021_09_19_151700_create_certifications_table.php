<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCertificationsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('certifications', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('user_id')->comment('关联的用户ID');
            $table->char('name')->comment('真实姓名');
            $table->char('number')->comment('证件号');
            $table->char('fpic', 70)->comment('证件正面照片');
            $table->char('bpic', 70)->comment('证件反面照片');
            $table->unsignedInteger('admin_id')->default(0)->comment('审核管理员ID');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态:0-待审核1-成功2-失败');
            $table->string('reason')->nullable()->comment('审核失败原因');
            $table->timestamps();
            $table->index('user_id');
            $table->unique('number');
        });
        \DB::statement("ALTER TABLE `certifications` comment'用户认证表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('certifications');
    }
}
