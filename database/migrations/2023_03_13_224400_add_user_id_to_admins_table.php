<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddUserIdToAdminsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('as_admins', function (Blueprint $table) {
            $table->unsignedInteger('user_id')->default(0)->comment('绑定用户账号id');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('as_admins', function (Blueprint $table) {
            $table->dropColumn(['user_id']);
        });
    }
}
