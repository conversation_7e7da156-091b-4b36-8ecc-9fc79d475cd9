<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePlugsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        /**
         * extend
         * [{"link":"111.jpg","describe":"描述","type":"0"}]
         */
        Schema::create('plugs', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name')->comment('名称');
            $table->string('images')->nullable()->comment('封面图');
            $table->text('intro')->nullable()->comment('简介');
            $table->unsignedDecimal('price', 11, 2)->default(0)->comment('价格');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->char('author')->default('犀光RadiRhino')->comment('作者');
            $table->unsignedInteger('admin_id')->default(0)->comment('添加人');
            $table->unsignedTinyInteger('cate_id')->default(0)->comment('分类');
            $table->unsignedTinyInteger('state')->default(0)->comment('禁用[上下架]：否-0/是-1');
            $table->text('external_link')->nullable()->comment('外部视频链接');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `plugs` comment'插件表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('plugs');
    }
}
