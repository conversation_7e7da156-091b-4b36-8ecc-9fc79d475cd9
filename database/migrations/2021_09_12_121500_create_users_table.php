<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUsersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users', function (Blueprint $table) {
            $table->id();
            $table->char('name', 30)->comment('昵称');
            $table->string('avatar')->nullable()->comment('头像');
            $table->char('sign', 30)->default('')->comment('个人签名');
            $table->string('intro')->default('')->comment('简介');
            $table->tinyInteger('sex')->default(0)->comment('性别');
            $table->string('email')->nullable()->comment('邮箱');
            $table->string('phone')->nullable()->comment('手机号');
            $table->string('password')->default('')->comment('密码');

//            $table->json('extend')->nullable()->comment('其它信息');
            $table->string('company')->default('')->comment('公司名称');
            $table->string('occupation')->default('')->comment('职业');
            $table->string('wechat')->default('')->comment('微信');
            $table->string('qq')->default('')->comment('QQ');

            $table->tinyInteger('state')->default(0)->comment('禁用状态：1禁用，0正常');
            $table->string('source')->default('')->comment('注册来源');
            $table->string('register_ip')->default('')->comment('注册ip');
            $table->timestamp('email_verified_at')->comment('邮箱验证时间')->nullable();
            $table->string('api_token')->nullable()->comment('用户API令牌');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users');
    }
}
