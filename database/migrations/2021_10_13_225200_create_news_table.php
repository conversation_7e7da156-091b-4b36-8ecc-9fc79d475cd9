<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateNewsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('news', function (Blueprint $table) {
            $table->increments('id');
            $table->string('title')->nullable()->comment('新闻标题');
            $table->string('images')->nullable()->comment('封面图');
            $table->longText('content')->comment('新闻内容');
            $table->char('author')->default('犀光RadiRhino')->comment('作者');
            $table->integer('view_num')->default(0)->comment('查看数量');
            $table->unsignedTinyInteger('sort')->default(0)->comment('排序');
            $table->unsignedTinyInteger('is_hide')->default(0)->comment('是否隐藏');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `news` comment'新闻'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('news');
    }
}
