<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAddressIdToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->unsignedInteger('address_id')->default(0)->comment('用户地址ID');
            $table->decimal('deduct_integral', 11)->default(0)->comment('光粒抵扣数量');
            $table->decimal('give_integral', 11)->default(0)->comment('光粒赠送数量');
            $table->decimal('postage', 11)->default(0)->comment('运费');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['address_id', 'deduct_integral', 'give_integral', 'postage']);
        });
    }
}
