<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAsAdminRolesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('as_admin_roles', function (Blueprint $table) {
            $table->unsignedTinyInteger('admin_id');
            $table->unsignedTinyInteger('role_id');
            $table->unique(['admin_id', 'role_id']);
        });
        \DB::statement("ALTER TABLE `as_admin_roles` comment'管理员角色关联'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('as_admin_roles');
    }
}
