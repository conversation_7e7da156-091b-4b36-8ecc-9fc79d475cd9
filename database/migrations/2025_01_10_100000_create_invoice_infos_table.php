<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateInvoiceInfosTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('invoice_infos', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id')->comment('用户ID');
            $table->tinyInteger('invoice_type')->default(1)->comment('发票类型：1-电子普通发票，2-电子专票');
            $table->string('invoice_content', 100)->default('技术咨询服务')->comment('发票内容');
            $table->tinyInteger('header_type')->default(1)->comment('抬头类型：1-个人，2-单位');
            $table->string('header_name', 100)->comment('抬头名称');
            $table->string('tax_number', 50)->default('')->nullable()->comment('单位税号');
            $table->string('registered_address', 200)->default('')->nullable()->comment('注册地址');
            $table->string('registered_phone', 20)->default('')->nullable()->comment('注册电话');
            $table->string('bank_name', 100)->default('')->nullable()->comment('开户银行');
            $table->string('bank_account', 50)->default('')->nullable()->comment('银行账号');
            $table->tinyInteger('is_default')->default(0)->comment('是否默认：1-是，0-否');
            $table->timestamps();
            
            $table->index('user_id');
        });
        
        \DB::statement("ALTER TABLE `invoice_infos` comment'发票信息表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('invoice_infos');
    }
}
