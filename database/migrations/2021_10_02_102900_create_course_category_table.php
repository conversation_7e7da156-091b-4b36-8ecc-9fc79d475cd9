<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCourseCategoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        // 工作流 = 分类
        Schema::create('course_category', function (Blueprint $table) {
            $table->increments('id');
            $table->char('name', 20)->comment('分类名称');
            $table->string('intro', 191)->comment('工作流介绍');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `course_category` comment'课程分类表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('course_category');
    }
}
