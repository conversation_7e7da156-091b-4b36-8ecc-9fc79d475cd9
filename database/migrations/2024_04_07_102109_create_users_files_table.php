<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUsersFilesTable extends Migration
{


    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('users_files', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name')->default('')->comment('名称');
            $table->string('images')->nullable()->comment('封面图');
            $table->longText('intro')->nullable()->comment('简介');
            $table->unsignedDecimal('price', 11, 2)->default(0)->comment('价格');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->char('author')->default('犀光RadiRhino')->comment('作者');
            $table->unsignedInteger('admin_id')->default(0)->comment('添加人');
            $table->unsignedTinyInteger('cate_id')->default(0)->comment('分类');
            $table->unsignedTinyInteger('state')->default(0)->comment('');
            $table->string('external_link')->nullable()->comment('外部视频链接');
            $table->unsignedInteger('give_integral')->default(0)->comment('赠送积分数量');
            $table->unsignedInteger('download_package')->default(0)->default(0)->comment('下载包');
            $table->unsignedInteger('download_manual')->default(0)->comment('下载手册');
            $table->unsignedInteger('download_case')->default(0)->comment('下载案列');
            $table->unsignedInteger('is_integral')->default(0)->comment('是否积分抵扣');
            $table->unsignedInteger('integral_num')->default(0)->comment('抵扣数量');
            $table->string('web_link')->nullable()->comment('外部网站跳转链接');
            $table->unsignedInteger('user_id')->default(0)->comment('作者ID');
            $table->unsignedInteger('dashang')->default(0)->comment('打赏');
            $table->string('type')->nullable()->comment('类型');
            $table->unsignedInteger('status')->default(0)->comment('0 草稿 2发布');
            $table->longText('detail')->nullable()->comment('详情');
            $table->string('videos')->nullable()->comment('视频');
            $table->unsignedTinyInteger('is_check')->default(0)->comment('审核状态');
            $table->string('errors')->nullable()->comment('失败原因');
            $table->unsignedInteger('vid')->default(0)->comment('正式id');
            $table->timestamps();
        });


        Schema::create('users_plug', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name')->default('')->comment('名称');
            $table->string('images')->nullable()->comment('封面图');
            $table->longText('intro')->nullable()->comment('简介');
            $table->unsignedDecimal('price', 11, 2)->default(0)->comment('价格');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->char('author')->default('犀光RadiRhino')->comment('作者');
            $table->unsignedInteger('admin_id')->default(0)->comment('添加人');
            $table->unsignedTinyInteger('cate_id')->default(0)->comment('分类');
            $table->unsignedTinyInteger('state')->default(0)->comment('');
            $table->string('external_link')->nullable()->comment('外部视频链接');
            $table->unsignedInteger('give_integral')->default(0)->comment('赠送积分数量');
            $table->unsignedInteger('download_package')->default(0)->comment('下载包');
            $table->unsignedInteger('download_manual')->default(0)->comment('下载手册');
            $table->unsignedInteger('download_case')->default(0)->comment('下载案列');
            $table->unsignedInteger('is_integral')->default(0)->comment('是否积分抵扣');
            $table->unsignedInteger('integral_num')->default(0)->comment('抵扣数量');
            $table->string('web_link')->nullable()->comment('外部网站跳转链接');
            $table->unsignedInteger('user_id')->default(0)->comment('作者ID');
            $table->unsignedInteger('dashang')->default(0)->comment('打赏');
            $table->string('type')->nullable()->comment('类型');
            $table->unsignedInteger('status')->default(0)->comment('0 草稿 2发布');
            $table->longText('detail')->nullable()->comment('详情');
            $table->string('videos')->nullable()->comment('视频');
            $table->unsignedTinyInteger('is_check')->default(0)->comment('审核状态');
            $table->string('errors')->nullable()->comment('失败原因');
            $table->unsignedInteger('vid')->default(0)->comment('正式id');
            $table->timestamps();
        });


        Schema::create('users_goods', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name')->default('')->comment('名称');
            $table->string('cover')->nullable()->comment('封面图');
            $table->text('images')->nullable()->comment('图片');
            $table->longText('intro')->nullable()->comment('简介');
            $table->string('desc')->nullable()->comment('商品介绍');
            $table->unsignedInteger('sales_num')->default(0)->comment('销量');
            $table->unsignedInteger('stock')->default(0)->comment('库存');
            $table->unsignedInteger('total_stock')->default(0)->comment('总库存');
            $table->unsignedDecimal('price', 11, 2)->default(0)->comment('价格');
            $table->unsignedDecimal('market_price', 11, 2)->default(0)->comment('市场价格');
            $table->unsignedInteger('postage')->default(0)->comment('邮费');
            $table->unsignedTinyInteger('status')->default(0)->comment('上下架：0-下架，1-上架');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->unsignedInteger('cate_id')->default(0)->comment('分类');
            $table->unsignedTinyInteger('is_delete')->default(0)->comment('是否删除：0-否，1-是');
            $table->unsignedInteger('give_integral')->default(0)->comment('赠送积分数量');
            $table->unsignedInteger('admin_id')->default(0)->comment('添加人');
            $table->unsignedInteger('is_integral')->default(0)->comment('是否积分抵扣');
            $table->unsignedInteger('integral_num')->default(0)->comment('抵扣数量');
            $table->string('material')->nullable()->comment('素材附件');
            $table->unsignedInteger('user_id')->default(0)->comment('作者ID');
            $table->unsignedTinyInteger('is_check')->default(0)->comment('审核状态');
            $table->string('errors')->nullable()->comment('失败原因');
            $table->unsignedInteger('vid')->default(0)->comment('正式id');
            $table->timestamps();
        });


        Schema::create('user_role_cates', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->comment('用户ID');
            $table->unsignedInteger('cate_id')->default(0)->comment('类型id');
            $table->string('type')->comment('类型');
            $table->timestamps();
        });


        Schema::create('users_upload_configs', function (Blueprint $table) {
            $table->increments('id');
            $table->string('key')->default('')->comment('配置标示');
            $table->string('value')->nullable()->comment('配置信息');
            $table->timestamps();
        });

        \DB::statement("ALTER TABLE `users_files` comment'课程表'");
        \DB::statement("ALTER TABLE `users_plug` comment'插件表'");
        \DB::statement("ALTER TABLE `users_goods` comment'商品表'");
        \DB::statement("ALTER TABLE `user_role_cates` comment'角色权限关联'");
        \DB::statement("ALTER TABLE `users_upload_configs` comment'配置'");

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('users_files');
        Schema::dropIfExists('users_plug');
        Schema::dropIfExists('users_goods');
        Schema::dropIfExists('user_role_cates');
        Schema::dropIfExists('users_upload_configs');
    }
}
