<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateNewCategoryTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('new_category', function (Blueprint $table) {
            $table->increments('id');
            $table->char('name', 20)->comment('名称');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `new_category` comment'新闻'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('new_category');
    }
}
