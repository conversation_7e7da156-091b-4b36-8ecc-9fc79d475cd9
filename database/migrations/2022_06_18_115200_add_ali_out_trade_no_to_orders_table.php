<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddAliOutTradeNoToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->string('ali_out_trade_no')->nullable()->comment('生成阿里三方支付订单号');
            $table->string('wechat_out_trade_no')->nullable()->comment('生成微信三方支付订单号');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['ali_out_trade_no', 'wechat_out_trade_no']);
        });
    }
}
