<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateMessagesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('messages', function (Blueprint $table) {
            $table->id();
            $table->string('scene')->comment('发送场景：publish-追踪，logistics-物流，popularize-推广');
            $table->unsignedInteger('status')->default(1)->comment('发送状态: 0-发送失败 1-发送成功');
            $table->text('content')->nullable()->comment('发送内容');
            $table->string('account')->comment('发送账号');
            $table->string('message')->nullable()->comment('sms发送后 第三方返回的信息');
            $table->unsignedInteger('target_id')->default(0)->comment('目标ID');
            $table->char('target_type', 20)->default('')->comment('目标类型');
            $table->timestamps();
            $table->index('account');
        });
        \DB::statement("ALTER TABLE `messages` comment'短信推送'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('messages');
    }
}
