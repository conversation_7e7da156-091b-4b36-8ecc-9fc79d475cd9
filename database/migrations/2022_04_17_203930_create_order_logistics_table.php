<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateOrderLogisticsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('order_logistics', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id')->comment('用户id');
            $table->unsignedInteger('order_id')->comment('订单id');
            $table->string('logistics_name')->comment('物流公司名称');
            $table->string('logistics_code', 30)->comment('快递code');
            $table->string('logistics_no')->comment('物流单号');
            $table->unsignedTinyInteger('delivery_status')->default(0)->comment('发货状态: 0-待发货 1-已发货');
            $table->timestamp('delivery_time')->nullable()->comment('发货时间');
            $table->unsignedTinyInteger('receipt_status')->default(0)->comment('收货状态:0-待收货 1-已收货');
            $table->timestamp('receipt_time')->nullable()->comment('收货时间');
            $table->timestamps();
            $table->index('order_id');
        });
        \DB::statement("ALTER TABLE `order_logistics` comment'订单物流信息'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('order_logistics');
    }
}
