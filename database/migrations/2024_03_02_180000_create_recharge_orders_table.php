<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration {
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('recharge_orders', function (Blueprint $table) {
            $table->increments('id');
            $table->string('order_no')->default('')->comment('充值单号');
            $table->unsignedInteger('user_id')->default(0)->comment('用户id');
            $table->unsignedInteger('num')->default(0)->comment('充值光子数量');
            $table->unsignedTinyInteger('status')->default(0)->comment('状态：0-未支付,1-已支付,3-已关闭');
            $table->unsignedTinyInteger('pay_method')->default(0)->comment('支付类型(1-微信,2-支付宝)');
            $table->decimal('order_amount', 12)->default(0)->comment('订单金额');
            $table->decimal('total_amount', 12)->default(0)->comment('订单总金额');
            $table->string('ali_out_trade_no')->default('')->comment('生成阿里三方支付订单号');
            $table->string('wechat_out_trade_no')->default('')->comment('生成微信三方支付订单号');
            $table->string('out_trade_no')->default('')->comment('为第三方生成的订单号');
            $table->string('third_order_no')->default('')->comment('第三方订单号');
            $table->timestamp('pay_time')->nullable()->comment('支付时间');
            $table->timestamp('close_time')->nullable()->comment('关闭时间');
            $table->string('remark')->default('')->comment('备注');
            $table->string('qrcode_str')->default('')->comment('支付二维码字符串');

            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `recharge_orders` comment'光子充值订单'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('recharge_orders');
    }
};
