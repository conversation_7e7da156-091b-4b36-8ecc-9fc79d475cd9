<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddUserFans extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_fans', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('fans_id')->default(0)->comment('粉丝ID');
            $table->unsignedInteger('user_id')->default(0)->comment('用户ID');
            $table->timestamps();
            //
        });

    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_fans');
//        Schema::table('user_fans', function (Blueprint $table) {
//            //
//        });
    }
}
