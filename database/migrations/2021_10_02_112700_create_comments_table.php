<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCommentsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('comments', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->comment('用户id');
            $table->unsignedInteger('target_id')->default(0)->comment('目标id');
            $table->char('target_type', 30)->default('task')->comment('目标类型：视频-task/新闻-news');
            $table->text('content')->nullable()->comment('内容');
            $table->unsignedInteger('like')->default(0)->comment('赞');
            $table->unsignedTinyInteger('is_top')->default(0)->comment('置顶：0-否/1-是');
            $table->unsignedTinyInteger('is_delete')->default(0)->comment('是否删除');

            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `comments` comment'评论'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('comments');
    }
}
