<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateLikesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('likes', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id')->comment('点赞用户');
            $table->char('target_type', 30)->comment('点赞目标类型：评论-comment');
            $table->unsignedInteger('target_id')->comment('点赞目标ID');
            $table->unsignedTinyInteger('status')->default(0)->comment('是否取消点赞:0-否/1-是');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `likes` comment'点赞'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('likes');
    }
}
