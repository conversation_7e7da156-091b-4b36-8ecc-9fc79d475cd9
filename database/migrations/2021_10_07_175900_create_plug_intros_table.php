<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePlugIntrosTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('plug_intros', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('plug_id')->default(0)->comment('插件id');
            $table->string('content')->default('')->comment('介绍内容');
            $table->string('images')->default('')->comment('图片链接');
            $table->unsignedTinyInteger('sort')->default(0)->comment('排序');
            $table->unsignedInteger('admin_id')->default(0)->comment('添加人');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `plug_intros` comment'插件功能介绍'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('plug_intros');
    }
}
