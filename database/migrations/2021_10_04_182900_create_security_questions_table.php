<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSecurityQuestionsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('security_questions', function (Blueprint $table) {
            $table->increments('id');
            $table->char('title', 20)->comment('问题');
            $table->unsignedTinyInteger('hide')->default(0)->comment('是否隐藏：0-否/1-显示');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `security_questions` comment'安全问题'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('security_questions');
    }
}
