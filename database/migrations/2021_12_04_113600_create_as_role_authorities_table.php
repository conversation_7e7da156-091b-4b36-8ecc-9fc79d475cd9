<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAsRoleAuthoritiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('as_role_authorities', function (Blueprint $table) {
            $table->unsignedInteger('role_id');
            $table->unsignedInteger('authority_id');
            $table->unique(['role_id', 'authority_id']);
        });
        \DB::statement("ALTER TABLE `as_role_authorities` comment'角色权限关联'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('as_role_authorities');
    }
}
