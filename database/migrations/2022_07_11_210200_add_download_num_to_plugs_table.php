<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDownloadNumToPlugsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('plugs', function (Blueprint $table) {
            $table->unsignedInteger('download_package')->default(0)->comment('下载包');
            $table->unsignedInteger('download_manual')->default(0)->comment('下载手册');
            $table->unsignedInteger('download_case')->default(0)->comment('下载案列');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('plugs', function (Blueprint $table) {
            $table->dropColumn(['download_package', 'download_manual', 'download_case']);
        });
    }
}
