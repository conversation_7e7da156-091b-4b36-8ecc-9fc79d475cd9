<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAsRolesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('as_roles', function (Blueprint $table) {
            $table->id();
            $table->char('name', 40)->unique()->comment('角色名称');
            $table->char('alias', 40)->unique()->comment('角色别名');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `as_roles` comment'角色'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('as_roles');
    }
}
