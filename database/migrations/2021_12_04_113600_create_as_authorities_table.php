<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAsAuthoritiesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('as_authorities', function (Blueprint $table) {
            $table->id();
            $table->string('name')->unique()->comment('权限名称');
            $table->string('alias')->unique()->comment('权限别名');
            $table->string('icon')->nullable()->comment('图标');
            $table->string('url')->nullable()->comment('跳转地址');
            $table->integer('sort')->default(0)->comment('排序');
            $table->tinyInteger('type')->comment('0: 菜单 1: 导航 2: 按钮');
            $table->integer('pid')->default(0)->comment('父级ID');
            $table->integer('show')->default(1)->comment('是否显示 0: 不显示 1: 显示');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `as_authorities` comment'权限表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('as_authorities');
    }
}
