<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateVerificationCodesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('verification_codes', function (Blueprint $table) {
            $table->id();
            $table->string('code')->comment('验证码');
            $table->string('scene')->comment('发送场景');
            $table->string('type')->comment('验证类型: sms-手机短信 email-邮件类型');
            $table->unsignedInteger('status')->comment('发送状态: 0-发送失败 1-发送成功');
            $table->unsignedTinyInteger('used')->comment('使用状态:0-未使用 1-已使用');
            $table->timestamp('used_at')->nullable()->comment('使用时间');
            $table->string('account')->comment('发送账号');
            $table->string('message')->nullable()->comment('sms发送后 第三方返回的信息');
            $table->timestamps();
            $table->index('account');
        });
        \DB::statement("ALTER TABLE `verification_codes` comment'验证码'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('verification_codes');
    }
}
