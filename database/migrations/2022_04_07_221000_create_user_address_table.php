<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateUserAddressTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('user_address', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id')->comment('用户id');
            $table->char('name',30)->comment('用户昵称');
            $table->char('phone',11)->comment('联系电话');
//            $table->string('province')->comment('省');
//            $table->string('city')->comment('市');
//            $table->string('area')->comment('区');
            $table->string('address')->comment('详细地址');
            $table->string('post_code')->nullable()->comment('邮政编码');
            $table->unsignedTinyInteger('status')->default(0)->comment('默认地址：0-否，1-是');
            $table->timestamps();
            $table->index('user_id');
        });
        \DB::statement("ALTER TABLE `user_address` comment'用户收货地址'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('user_address');
    }
}
