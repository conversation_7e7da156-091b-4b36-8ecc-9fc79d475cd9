<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateGoodsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('goods', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name')->comment('商品名称');
            $table->string('cover')->nullable()->comment('封面图');
            $table->json('images')->nullable()->comment('商品图片');
            $table->string('intro')->default('')->comment('商品简介或者商品卖点说明');
            $table->text('desc')->comment('商品介绍');
            $table->unsignedInteger('sales_num')->default(0)->comment('销售量');
            $table->unsignedInteger('stock')->default(0)->comment('库存');
            $table->unsignedInteger('total_stock')->default(0)->comment('总库存');
            $table->unsignedDecimal('price', 19,2)->default(0)->comment('售价');
            $table->unsignedDecimal('market_price', 19,2)->default(0)->comment('市场价格');
            $table->unsignedInteger('postage')->default(0)->comment('邮费');
            $table->unsignedTinyInteger('status')->default(1)->comment('上下架：0-下架，1-上架');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->unsignedSmallInteger('cate_id')->comment('分类id');
            $table->unsignedTinyInteger('is_delete')->default(0)->comment('是否删除：0-否，1-是');
            $table->unsignedInteger('give_integral')->default(0)->comment('赠送积分数量');
            $table->unsignedInteger('admin_id')->default(0)->comment('管理员id');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `goods` comment'商品表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('goods');
    }
}
