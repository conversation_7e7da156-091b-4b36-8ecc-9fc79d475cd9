<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCommentReplysTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('comment_replys', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->comment('用户id');
            $table->unsignedInteger('comment_id')->default(0)->comment('内容id');
            $table->unsignedInteger('parent_id')->default(0)->comment('评论回复上级id');
            $table->text('content')->nullable()->comment('内容');
            $table->unsignedInteger('like')->default(0)->comment('赞');
            $table->unsignedTinyInteger('is_delete')->default(0)->comment('是否删除');
            $table->unsignedTinyInteger('is_read')->default(0)->comment('是否读取');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `comment_replys` comment'评论回复'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('comment_replys');
    }
}
