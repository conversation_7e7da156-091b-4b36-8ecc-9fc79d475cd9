<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCouponCodesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('coupon_codes', function (Blueprint $table) {
            $table->id();
            $table->char('code', 30)->unique()->comment('优惠码');
            $table->unsignedInteger('user_id')->default(0)->comment('指定用户ID');
            $table->unsignedInteger('use_user_id')->default(0)->comment('使用用户');
            $table->unsignedTinyInteger('used')->default(0)->comment('是否使用');
            $table->timestamp('end_time')->nullable()->comment('结束时间');
            $table->unsignedDecimal('price', 11, 2)->default(0)->comment('抵扣价格');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `coupon_codes` comment'优惠码'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('coupon_codes');
    }
}
