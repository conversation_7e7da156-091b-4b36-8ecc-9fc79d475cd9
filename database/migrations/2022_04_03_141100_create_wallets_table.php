<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWalletsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wallets', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id')->default(0)->comment('用户ID');
            $table->unsignedDecimal('income', 12)->default(0)->comment('总收益');
            $table->unsignedDecimal('balance', 12)->default(0)->comment('余额');
            $table->unsignedDecimal('expend', 12)->default(0)->comment('支出');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `wallets` comment'钱包'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wallets');
    }
}
