<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddInvoiceFieldsToRechargeOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('recharge_orders', function (Blueprint $table) {
            $table->unsignedInteger('invoice_count')->default(0)->comment('开票次数');
            $table->tinyInteger('invoice_status')->default(0)->comment('开票状态：0-未开票，1-已申请，2-已完成');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('recharge_orders', function (Blueprint $table) {
            $table->dropColumn(['invoice_count', 'invoice_status']);
        });
    }
}
