<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreatePlugDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('plug_details', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('plug_id')->comment('插件id');
            $table->string('version_name')->nullable()->comment('版本名称');
            $table->string('version_code')->nullable()->comment('版本号');
            $table->json('platform')->nullable()->comment('支持平台');
            $table->text('description')->nullable()->comment('描述');
            $table->char('type', 30)->default('package')->comment('分类：插件包-package/手册-manual/案例-case');
            $table->string('link')->default('')->comment('下载链接');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->unsignedInteger('admin_id')->default(0)->comment('添加人');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `plug_details` comment'插件内容'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('plug_details');
    }
}
