<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCourseLevelsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('course_levels', function (Blueprint $table) {
            $table->increments('id');
            $table->string('title')->nullable()->comment('名称');
            $table->unsignedTinyInteger('sort')->default(0)->comment('排序');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `course_levels` comment'课程层级'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('course_levels');
    }
}
