<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWatchTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('watch', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('user_id')->default(0)->comment('用户id');
            $table->unsignedInteger('course_id')->default(0)->comment('课程ID');
            $table->unsignedInteger('course_detail_id')->default(0)->comment('课程详情id');
            $table->unsignedTinyInteger('status')->default(0)->comment('观看状态：看完-1/在看-0');
            $table->unsignedInteger('last_time_out')->default(0)->comment('上次观看到');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `watch` comment'观看信息'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('watch');
    }
}
