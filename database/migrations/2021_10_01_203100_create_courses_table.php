<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCoursesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('courses', function (Blueprint $table) {
            $table->increments('id');
            $table->string('name')->comment('名称');
            $table->string('images')->nullable()->comment('封面图');
            $table->longText('intro')->nullable()->comment('简介');
            $table->string('explain')->nullable()->comment('主页介绍');
            $table->unsignedTinyInteger('cate_id')->default(0)->comment('分类');
            $table->timestamp('end_time')->nullable()->comment('视频有效期结束时间');
            $table->timestamp('publish_time')->nullable()->comment('发布时间');
            $table->unsignedTinyInteger('is_publish')->default(0)->comment('发布状态');
            $table->unsignedDecimal('price', 11, 2)->default(0)->comment('价格');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->unsignedInteger('level')->default(0)->comment('层级');
            $table->char('author')->default('犀光RadiRhino')->comment('作者');
            $table->unsignedInteger('admin_id')->default(0)->comment('添加人');
            $table->unsignedTinyInteger('is_delete')->default(0)->comment('是否删除');

            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `courses` comment'课程表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('courses');
    }
}
