<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateSocialitesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('socialites', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id')->default(0)->comment('用户id');
            $table->string('authid')->default(0)->comment('第三方id');
            $table->char('type', 30)->default('weixin')->comment('类型：微信-weixin/QQ-qq');
            $table->json('auth_user')->nullable()->comment('授权登陆信息');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `socialites` comment'第三方授权登陆信息'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('socialites');
    }
}
