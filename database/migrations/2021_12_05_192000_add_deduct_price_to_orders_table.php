<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddDeductPriceToOrdersTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->decimal('total_amount', 11, 2)->default(0)->comment('订单总金额');
            $table->decimal('deduct_price', 11, 2)->default(0)->comment('抵扣金额');
            $table->char('coupon_code', 30)->default('')->comment('优惠码');
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::table('orders', function (Blueprint $table) {
            $table->dropColumn(['deduct_price', 'total_amount']);
        });
    }
}
