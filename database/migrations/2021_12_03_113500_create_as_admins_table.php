<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateAsAdminsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('as_admins', function (Blueprint $table) {
            $table->id();
            $table->string('name')->comment('昵称');
            $table->string('phone')->comment('电话');
            $table->string('email')->nullable()->comment('邮箱');
            $table->string('password')->comment('密码');
            $table->string('admin_token')->nullable()->comment('令牌');
            $table->unsignedTinyInteger('state')->default(0)->comment('状态:0-正常,1-禁用');
            $table->unsignedTinyInteger('is_delete')->default(0)->comment('删除:0-未删除,1-已删除');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `as_admins` comment'管理员'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('as_admins');
    }
}
