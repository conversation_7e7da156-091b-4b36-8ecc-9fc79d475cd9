<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateWalletLogsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('wallet_logs', function (Blueprint $table) {
            $table->id();
            $table->char('title', 50)->comment('流水标题');
            $table->unsignedInteger('user_id')->comment('关联的用户ID');
            $table->unsignedInteger('target_id')->default(0)->comment('目标ID');
            $table->char('target_type', 20)->comment('关联类型: order/user');
            $table->unsignedDecimal('amount', 19,2)->comment('本次变动金额');
            $table->unsignedTinyInteger('action')->comment('收支动作:1-收入/2-支出');
            $table->unsignedTinyInteger('status')->comment('状态:0-待处理/1-成功/2-失败');
            $table->char('currency', 20)->comment('货币类型');
            $table->char('type', 30)->comment('流水类型');
            $table->json('extend')->nullable()->comment('扩展字段');
            $table->timestamps();
            $table->index('user_id');
            $table->index('type');
            $table->index(['target_id', 'type', 'action', 'created_at', 'amount']);
        });
        \DB::statement("ALTER TABLE `wallet_logs` comment'流水记录表'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('wallet_logs');
    }
}
