<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateCourseDetailsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('course_details', function (Blueprint $table) {
            $table->increments('id');
            $table->unsignedInteger('course_id')->default(0)->comment('课程id');
            $table->string('name')->comment('内容名称');
            $table->string('images')->nullable()->comment('封面图');
            $table->char('type', 20)->default('task')->comment('分类：任务-task/章节-chapter');
            $table->string('link')->default('')->comment('视频链接');
            $table->unsignedInteger('chapter_id')->default(0)->comment('章节标题');
            $table->unsignedInteger('views')->default(0)->comment('播放次数');
            $table->unsignedTinyInteger('is_free')->default(1)->comment('免费：免费-0/付费-1');
            $table->unsignedTinyInteger('state')->default(0)->comment('禁用[上下架]：否-0/是-1');
            $table->unsignedInteger('duration')->default(0)->comment('时长');
            $table->unsignedInteger('sort')->default(0)->comment('排序');
            $table->unsignedInteger('admin_id')->default(0)->comment('添加人');
            $table->timestamps();
        });
        \DB::statement("ALTER TABLE `course_details` comment'课程详情'");
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('course_details');
    }
}
